import { Converter, DslData, AthComponentType, ConvertOutput, DslSchema } from '../type';

export const LcdpFooterButtonGroupConverter: Converter = {
  key: AthComponentType.FOOTER_BUTTON_GROUP,
  toSchema: (dsl: DslData): ConvertOutput<DslSchema, DslData> => {
    const { group = [], ...dslInfo } = dsl;
    return {
      data: {
        componentName: AthComponentType.FOOTER_BUTTON_GROUP,
        props: { dslInfo },
      },
      childrenData: [
        {
          key: 'children',
          data: group.length > 0 ? group : [{ type: AthComponentType.DYNAMIC_OPERATION }],
        },
      ],
    };
  },

  toDsl: (dslSchema: DslSchema): ConvertOutput<DslData, DslSchema> => {
    const { props, children = [] } = dslSchema;
    return {
      data: {
        ...props.dslInfo,
        type: AthComponentType.FOOTER_BUTTON_GROUP,
      },
      childrenData: [
        {
          key: 'group',
          data: children,
        },
      ],
    };
  },

  valid: (dsl: DslData) => {
    return true;
  },
};
