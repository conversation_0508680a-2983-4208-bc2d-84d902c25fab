import {
  IPublicModelDocumentModel,
  IPublicModelLocateEvent,
  IPublicModelPluginContext,
  IPublicModelSkeletonItem,
} from '@alilc/lowcode-types';
import { v4 as uuidv4 } from 'uuid';
import { AthLowCodeEventName } from '../plugin-ath-loader/type';
import { AthComponentType } from '@/tools/business/lcdp-converter/type';
import { debounceRefresh, handleDragPoint } from './tools';
import { AthLowCodeConfigKey } from '../plugin-ath-loader/type';
import { delay } from 'lodash';
import { hotkey, common, material, canvas } from '@alilc/lowcode-engine';
import { ButtonAttachMode, ButtonType } from '../plugin-ath-setter/components/Button/enum';
import {
  findTargetNodeByTypes,
  getAttachMode,
  setNestedValue,
} from '../plugin-ath-setter/components/Button/tools';
import {
  BusinessButtonBaseTypeSet,
  ToolbarButtonTypeSet,
} from '../plugin-ath-setter/components/Button/constant';
import { DefaultButtonInfoTypeMap } from '../plugin-ath-setter/components/Button/defaultButtonValue';
import { IBusinessButtonInfo } from '../plugin-ath-setter/components/Button/buttonCoreTypes';
import EventEmitter from 'eventemitter3';

const LcdpInitPlugin = (ctx: IPublicModelPluginContext, options: any) => {
  return {
    async init() {
      console.log('LcdpInitPlugin init');
      const { project, config, skeleton, plugins, event } = ctx;

      (window as any).lowcode = ctx; // 临时调试用

      // 删除大纲树start
      skeleton.remove({
        area: 'rightArea',
        name: 'outline-backup-pane',
        type: 'Panel',
      });
      skeleton.remove({
        area: 'leftArea',
        name: 'outline-master-pane',
        type: 'PanelDock',
      });

      /**
       * 禁用除escape外所有快捷键
       */
      // 创建全局事件发射器
      const hotkeyEventEmitter = new EventEmitter();
      // 挂载到 lowcode 全局对象，供其他项目使用
      (window as any).lowcode = (window as any).lowcode || {};
      (window as any).lowcode.hotkeyEventEmitter = hotkeyEventEmitter;
      // 监听 postMessage 作为备用方案
      window.addEventListener('message', (event) => {
        if (event.data?.type === 'hotkey:enable') {
          hotkeyEventEmitter.emit('hotkey:enable');
        } else if (event.data?.type === 'hotkey:disable') {
          hotkeyEventEmitter.emit('hotkey:disable');
        }
      });
      let isHotkeyEnabled = false;
      // 监听快捷键控制事件
      hotkeyEventEmitter.on('hotkey:enable', () => {
        isHotkeyEnabled = true;
      });

      hotkeyEventEmitter.on('hotkey:disable', () => {
        isHotkeyEnabled = false;
      });

      hotkey.bind(
        [
          'right',
          'left',
          'up',
          'down',
          // 'command+s',
          // 'command+p',
          // 'command+d',
          // 'command+z',
          // 'shift+command+z',
          // 'backspace',
          // 'command+c',
          // 'command+v',
          // 'command+x',
          // 'option+up',
          // 'option+down',
          // 'option+left',
          // 'option+right',
        ],
        () => {
          // 根据标志决定是否允许快捷键
          return isHotkeyEnabled;
        },
      );

      let currentPanel: any = null;

      event.on(`common:${AthLowCodeEventName.LowCodeIsSideBarRivetUpdate}`, (isSideBarRivet) => {
        if (currentPanel) {
          // 官方没有暴露api，原码中是这样实现的
          const isFloat = currentPanel?.parent?.name === 'leftFloatArea';
          if (isSideBarRivet === isFloat) {
            currentPanel?.skeleton?.toggleFloatStatus(currentPanel);
          }
        }
      });

      // 主要 api中的skeleton getPanel 在实际中是 undefined，所以才 使用了 该方案
      skeleton.onShowPanel((paneName?: string, panel?: IPublicModelSkeletonItem) => {
        const panelInstance = panel?.parent?.current;
        if (currentPanel?.id === panelInstance?.id) {
          // 代表面板内实现了 toggleFloatStatus
          // 那就需要同步 数据给 外部
          // 官方没有暴露api，原码中是这样实现的
          const isFloat = panelInstance?.parent?.name === 'leftFloatArea';
          event.emit(AthLowCodeEventName.AthIsSideBarRivetHandle, !isFloat);
        }
        currentPanel = panelInstance;
      });

      plugins.delete('OutlinePlugin');
      // 删除大纲树end

      // =======引擎全局逻辑处理=======
      const scenarioName = options['scenarioName'];
      const scenarioDisplayName = options['displayName'] || scenarioName;
      const scenarioInfo = options['info'] || {};
      // 保存在 config 中用于引擎范围其他插件使用
      config.set('scenarioName', scenarioName);
      config.set('scenarioDisplayName', scenarioDisplayName);
      config.set('scenarioInfo', scenarioInfo);

      canvas?.dragon?.onDrag((e: IPublicModelLocateEvent) => {
        handleDragPoint(e);
      });

      // canvas?.dragon?.onDragend((e) => {
      //   const insertion = document.querySelector('.lc-insertion');
      //   if (insertion?.style) {
      //     insertion.style.opacity = '1';
      //   }
      // });

      // 全局的事件注册
      // =======project相关逻辑=======
      // project?.currentDocument?.onImportSchema((schema) => {
      //   console.log('onImportSchema');
      //   console.log(schema);
      // });

      project.onChangeDocument((document: IPublicModelDocumentModel) => {
        document.onAddNode((node) => {
          // console.log(
          //   '🚀 [Generated Log]: path = src/plugins/lcdp-init-plugin/index.tsx, scope = LcdpInitPlugin.init, onAddNode, node = ',
          //   node,
          // );
          node.setPropValue('dslInfo.id', uuidv4());

          // 单号组件拖拽组件默认赋值
          if (node?.componentName === 'ADD_DOCUMENTID_CONTROL') {
            node.setPropValue(
              'dslInfo.queryAction.tmAction.productName',
              config.get(AthLowCodeConfigKey.AthDynamicWorkDesignInfo).dynamicWorkDesignConfig
                .businessConfig.productName,
            );
          }
          if (['ATHENA_TABLE', 'FORM_LIST'].includes(node?.componentName)) {
            if (node.getPropValue('dslInfo.comeFrom')) {
              delay(() => event.emit('setter:table-form-open'), 100);
              node.setPropValue('dslInfo.comeFrom', undefined);
            }
          }
          if (node && node?.componentName === 'DATA_QUERY') {
            node.setPropValue('manualDrag', true);
          }
          debounceRefresh(document);
        });

        document.onMountNode((payload) => {
          const { node } = payload;
          // 查询方案组件在被添加时，默认生成一个出口组件
          if (node && node?.componentName === 'DATA_QUERY' && !!node.getPropValue('manualDrag')) {
            const { snippets = [] } = material.getComponentMeta('OUTLET')?.getMetadata() as any;
            node.setPropValue('manualDrag', false);
            const newNode = node.document?.createNode(snippets[0]?.schema ?? {});
            newNode && node.parent?.insertAfter(newNode, node);
          }
          /**
           * 自动创建TABLE_GROUP时，需要额外处理打印按钮
           */
          if (node?.componentName === AthComponentType.TABLE_GROUP) {
            const tableNode = node?.parent;
            const tableDslInfo = tableNode?.getPropValue('dslInfo');
            const child = node?.children;
            child?.forEach((childNode) => {
              const dslInfo = childNode?.getPropValue('dslInfo');
              if (dslInfo?.type === ButtonType.BUTTON_PRINT) {
                dslInfo.attachMode = ButtonAttachMode.ROW;
                dslInfo.targetSchema = tableDslInfo?.schema;
                dslInfo.targetPath = tableDslInfo?.path;
                childNode?.setPropValue('dslInfo', { ...dslInfo });
              }
            });
          }
          /**
           * 当按钮被拖进表格和表单时，为按钮添加相应的targetSchema,targetPath
           */
          if (node?.componentName === ButtonType.BUTTON) {
            const targetNode = findTargetNodeByTypes(
              [AthComponentType.ATHENA_TABLE, AthComponentType.FORM_LIST],
              node,
            );
            if (targetNode) {
              const dslInfo = targetNode?.getPropValue('dslInfo');
              const { schema, path } = dslInfo ?? {};
              const nodeDslInfo = node.getPropValue('dslInfo');
              node.setPropValue('dslInfo', {
                ...(nodeDslInfo ?? {}),
                targetSchema: schema,
                targetPath: path,
              });
            } else {
              const nodeDslInfo = node.getPropValue('dslInfo');
              node.setPropValue('dslInfo', {
                ...(nodeDslInfo ?? {}),
                targetSchema: null,
                targetPath: null,
              });
            }
            const currentNodeDslInfo = node.getPropValue('dslInfo');
            if (BusinessButtonBaseTypeSet.has(currentNodeDslInfo?.type)) {
              const attachMode = getAttachMode(node) ?? null;
              currentNodeDslInfo.attachMode = attachMode;
              if (attachMode) {
                const defaultInfo = DefaultButtonInfoTypeMap.get(currentNodeDslInfo?.type);
                currentNodeDslInfo.operation = (defaultInfo as IBusinessButtonInfo).operation;
              } else {
                if (ToolbarButtonTypeSet.has(currentNodeDslInfo?.type)) {
                  delete currentNodeDslInfo.operation;
                }
              }
            }
            node.setPropValue('dslInfo', { ...currentNodeDslInfo });
          }
        });

        document.onRemoveNode((node) => {
          console.log(
            '🚀 [Generated Log]: path = src/plugins/lcdp-init-plugin/index.tsx, scope = LcdpInitPlugin.init, onRemoveNode, node = ',
            node,
          );
          if (
            node.componentName === AthComponentType.AthGridsterChild ||
            node.parent?.componentName === AthComponentType.AthGridsterChild
          ) {
            debounceRefresh(document);
          }
        });

        document.onChangeNodeProp((info) => {
          if (
            [AthComponentType.AthGridsterChild, AthComponentType.AthGridster].includes(
              info.node.componentName as AthComponentType,
            ) &&
            info.key !== 'items'
          ) {
            debounceRefresh(document);
          }
        });

        document.onChangeNodeChildren((info) => {
          if (
            [AthComponentType.AthGridsterChild, AthComponentType.AthGridster].includes(
              info.node.componentName as AthComponentType,
            )
          ) {
            debounceRefresh(document);
          }
        });
      });

      // project.addPropsTransducer((props: any): any => {
      //   if (props.path === '') {
      //     console.log('addPropsTransducer:', props);
      //     const schema = project.createDocument()?.exportSchema(IPublicEnumTransformStage.Save);
      //     const nodePath = props.path;
      //     const nodeSchema = props.schema;
      //     const fullPath = [nodePath, nodeSchema].filter(Boolean).join('.');
      //     const operations = ((schema?.props?.operations as any[]) ?? []).filter((item) => {
      //       return item.target === fullPath && item.mode === 'row';
      //     });
      //     if (!isEqual(props['operationsRow'], operations)) {
      //       props['operationsRow'] = operations;
      //     }
      //   }
      //   return props;
      // }, IPublicEnumTransformStage.Save);

      // =======skeleton相关逻辑=======
      // skeleton.onShowPanel((paneName) => {
      //   console.log('onShowPanel:', paneName);
      // });

      // TODO 关于lowcode多语言的机制 还需继续研究
      const { intl, getLocale, setLocale } = common.utils.createIntl({});
      const locale = config?.get('locale') || 'zh-CN';
      setLocale(locale);

      // TODO: 临时方案，后续需要优化
      // lowcode 1.1.4 版本 多语言不支持繁体  https://github.com/alibaba/lowcode-engine/issues/2505
      // 升级版本 报错很多 成本过高 因此暂时如果是繁体，则切换成简体，只再中间画布的时候生效

      project.onSimulatorRendererReady(() => {
        setTimeout(() => {
          project.simulatorHost?.set('locale', locale === 'zh-TW' ? 'zh-CN' : locale);
          project.simulatorHost?.rerender?.();
        });
      });
    },
  };
};
LcdpInitPlugin.pluginName = 'LcdpInitPlugin';
LcdpInitPlugin.meta = {
  preferenceDeclaration: {
    title: '保存插件配置',
    properties: [
      {
        key: 'scenarioName',
        type: 'string',
        description: '用于localstorage存储key',
      },
      {
        key: 'displayName',
        type: 'string',
        description: '用于显示的场景名',
      },
      {
        key: 'info',
        type: 'object',
        description: '用于扩展信息',
      },
    ],
  },
};
export default LcdpInitPlugin;
