import React, { Component, useEffect, useMemo, useState } from 'react';
import { Button, Modal, Form, Input, Radio, SelectProps, Select } from 'antd';
import './index.scss';
import {
  IPublicModelNode,
  IPublicModelSettingField,
  IPublicTypePropChangeOptions,
} from '@alilc/lowcode-types';
import { GridSetting, GridSettingSearchInfo } from 'src/plugins/plugin-ath-loader/type';
import { lcdpConverterManager } from '@/tools/business/lcdp-converter/LcdpConverterManager';
import { DslData } from '@/tools/business/lcdp-converter/type';
import CommonSetterLayout, { CommonSetterLayoutProps } from '../../components/common-setter-layout';
import i18next from 'i18next';

interface ItemProps {
  label: string;
  value: string;
}

export interface AthSearchInfoSetterProps {
  field: IPublicModelSettingField;
  value: DslData;
  options: {
    titleProps: CommonSetterLayoutProps;
  };
}

const AthSearchInfoSetter: React.FC<AthSearchInfoSetterProps> = (
  props: AthSearchInfoSetterProps,
) => {
  const {
    field,
    value,
    options: { titleProps },
    onChange,
  } = props;
  const [gridSettings, setGridSettings] = useState<GridSetting[]>([]);
  const [currentGridSetting, setCurrentGridSetting] = useState<GridSetting>();

  useEffect(() => {
    const dispose = field?.node?.document?.onChangeNodeProp(
      (info: IPublicTypePropChangeOptions) => {
        const { node, key } = info;
        if (node === field?.node?.document?.root && key === 'gridSettings') {
          setValue();
        }
      },
    );
    setValue();
    return () => {
      dispose?.();
    };
  }, []);

  useEffect(() => {
    updateCurrentGridSetting();
  }, [gridSettings, value]);

  const setValue = () => {
    const { gridSettings = [] } = field?.node?.document?.root?.propsData as any;
    setGridSettings(gridSettings);
  };

  const options: ItemProps[] = useMemo(() => {
    if (!field?.node?.children) return [];
    //这是业务逻辑，取到所有TABLE_GROUP组件包裹的子组件，都是字段组件
    return field.node.children
      .filter(
        (child: IPublicModelNode) =>
          child.componentName === 'TABLE_GROUP' &&
          !child.children?.get(0)?.componentName.includes('BUTTON'),
      )
      .map((child: IPublicModelNode) => {
        const { headerName, lang, schema } = child.children?.get(0)?.getPropValue('dslInfo');
        return {
          label: lang?.headerName?.[i18next.language] ?? headerName,
          value: schema,
          lang,
        };
      });
  }, [field]);

  const optionList: { value: string; label: React.ReactNode }[] = useMemo(() => {
    return (
      currentGridSetting?.searchInfo?.map((searchInfo: GridSettingSearchInfo) => {
        return { value: searchInfo.searchField, label: searchInfo.searchName };
      }) ?? []
    );
  }, [currentGridSetting]);

  const sharedProps: SelectProps = {
    mode: 'multiple',
    style: { width: '100%' },
    options,
    maxTagCount: 'responsive',
  };

  const selectProps: SelectProps = {
    value: optionList,
    onChange: (value: { value: string; label: React.ReactNode }[]) => {
      const searchInfo = value.map((item) => {
        const option = options?.find((op) => op.value === item.value);
        return {
          searchName: item.label,
          lang: {
            searchName: option?.lang?.headerName,
          },
          searchField: item.value,
          dataType: 'string',
        };
      });

      const updateCurrentGridSetting = {
        ...currentGridSetting,
        searchInfo,
      } as GridSetting;

      const updateCurrentGridSettings = gridSettings.filter((gridSetting: GridSetting) => {
        return (
          gridSetting.gridPath !== currentGridSetting?.gridPath &&
          gridSetting.gridSchema !== currentGridSetting?.gridSchema
        );
      });
      updateCurrentGridSettings.push(updateCurrentGridSetting);

      field?.node?.document?.root?.setPropValue('gridSettings', updateCurrentGridSettings as any);
      setCurrentGridSetting(updateCurrentGridSetting as GridSetting);
      // onChange(updateCurrentGridSetting?.searchInfo);
      // 更新处理advanced-search
      const hideDefaultToolbar =
        field?.node
          ?.getPropValue('dslInfo.setting.hideDefaultToolbar')
          ?.filter((item) => item !== 'advanced-search') ?? [];
      if (!value || value.length === 0) hideDefaultToolbar.push('advanced-search');
      field?.node?.setPropValue('dslInfo.setting.hideDefaultToolbar', hideDefaultToolbar);
    },
  };

  const updateCurrentGridSetting = () => {
    const { path: nodePath, schema: nodeSchema } = value;
    const gridSetting = gridSettings.find((gridSetting: GridSetting) => {
      return gridSetting.gridPath === nodePath && gridSetting.gridSchema === nodeSchema;
    });
    setCurrentGridSetting(
      gridSetting ?? {
        gridPath: nodePath,
        gridSchema: nodeSchema,
        searchInfo: [],
      },
    );
  };

  return (
    <CommonSetterLayout {...titleProps}>
      <div className="ath-search-info-setter">
        <Select {...sharedProps} {...selectProps} size="small" labelInValue={true} mode="tags" />
      </div>
    </CommonSetterLayout>
  );
};

export default AthSearchInfoSetter;
