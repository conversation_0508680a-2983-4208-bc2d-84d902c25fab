import React, { Component, Suspense, useEffect, useMemo, useRef, useState } from 'react';
import { ConfigProvider, Button, Modal, Form, Input, Radio } from 'antd';
import { RightSquareOutlined } from '@ant-design/icons';
import { AthTreeDataNode } from '@/plugins/plugin-ath-field-panel/type';
import './index.scss';
import { config } from '@alilc/lowcode-engine';
import { AthLowCodeConfigKey, AthLowCodeEventName } from '@/plugins/plugin-ath-loader/type';
import { cloneDeep } from 'lodash';
import { findNodeByKey } from '@/plugins/plugin-ath-field-panel/tools';
import i18n, { t } from 'i18next';
import AthenaDesignerCoreMFComponent from '@/components/AthenaDesignerCoreMFComponent';
import CommonSetterLayout, { CommonSetterLayoutProps } from '../../components/common-setter-layout';
import { event } from '@alilc/lowcode-engine';
import { findPathInTree, processTree } from './config';
import theme from '@/config/theme.json';
import { getDataSourceNameFactory, queryFieldTree } from '@/tools/utils/common';
import { IPublicModelSettingField } from '@alilc/lowcode-types';

export interface AthSelectAssociationFieldSetterProps {
  value: any;
  options: {
    titleProps: CommonSetterLayoutProps;
    selectProps: {
      controlLevel?: boolean;
    };
  };
  onChange: (value: any) => void;
  field: IPublicModelSettingField;
}
const AthSelectAssociationFieldSetter: React.FC<AthSelectAssociationFieldSetterProps> = (
  props: AthSelectAssociationFieldSetterProps,
) => {
  const { value, options, field, onChange } = props;
  const [isOpenSelectFieldModal, setIsOpenSelectFieldModal] = useState(false);
  const [treeMapOrigin, setTreeMapOrigin] = useState<Map<string, any>>();

  const fullPath = useMemo(() => {
    const { schema, path } = value;
    return [path, schema].filter(Boolean).join('.');
  }, [value]);
  const dataSourceNameFN = getDataSourceNameFactory();
  const dataSourceName = dataSourceNameFN(field);

  useEffect(() => {
    const eventKey = `common:${AthLowCodeEventName.LowCodeFieldTreeMapUpdate}`;
    updateTreeDataOrigin(cloneDeep(config.get(AthLowCodeConfigKey.AthFieldTreeMap) ?? new Map()));
    event.on(eventKey, updateTreeDataOrigin);
    return () => {
      event.off(eventKey, updateTreeDataOrigin);
    };
  }, []);

  const updateTreeDataOrigin = (athTreeDataNode: Map<string, any>) => {
    if (!!dataSourceName) {
      // 当有数据源时，只塞当前数据源
      const newMap = new Map([...athTreeDataNode].filter(([k, v]) => k === dataSourceName));
      setTreeMapOrigin(newMap);
    } else {
      setTreeMapOrigin(athTreeDataNode);
    }
  };

  useEffect(() => {
    updateTreeDataOrigin(cloneDeep(config.get(AthLowCodeConfigKey.AthFieldTreeMap) ?? new Map()));
  }, [dataSourceName]);

  // 根据meta中的配置项组装treeData
  const treeData = useMemo(() => {
    let _athTreeDataNode = cloneDeep(queryFieldTree(field, treeMapOrigin)) || [];
    if (options?.selectProps?.controlLevel) {
      const isFoundPath = findPathInTree(_athTreeDataNode, value?.path);
      _athTreeDataNode = isFoundPath
        ? _athTreeDataNode.map((node) => processTree(node, value?.path))
        : _athTreeDataNode;
    }
    return _athTreeDataNode;
  }, [value, treeMapOrigin]);

  const showText = useMemo(() => {
    if (!value.schema) return t('请选择字段');
    const node = findNodeByKey(treeData, fullPath);
    return node?.description?.[i18n.language] ?? '';
    // return node?.description?.[i18n.language] ?? t('未找到节点');
  }, [fullPath, value.schema, treeData]);

  const handleOpenSelectFieldModal = () => {
    setIsOpenSelectFieldModal(true);
  };

  // 选择字段弹窗确认
  const handleSelectFieldModalSubmit = (nodes: AthTreeDataNode[]) => {
    const node = nodes[0] ?? {};
    const { data_name: schema, path, description} = node;
    onChange({ schema, path, description });
    setIsOpenSelectFieldModal(false);
  };

  // 选择字段弹窗取消
  const handleSelectFieldModalCancel = () => {
    setIsOpenSelectFieldModal(false);
  };

  return (
    <ConfigProvider theme={theme}>
      <CommonSetterLayout {...options.titleProps}>
        <div
          className="ath-select-association-field-setter"
          onClick={() => {
            handleOpenSelectFieldModal();
          }}
        >
          <Input
            className="setter-input"
            placeholder="请输入"
            value={showText}
            readOnly
            suffix={<RightSquareOutlined />}
          />
        </div>
        <AthenaDesignerCoreMFComponent
          componentName="SelectFieldModal"
          componentProps={{
            isOpen: isOpenSelectFieldModal,
            dataSourceName,
            fieldTreeMap: treeMapOrigin,
            title: t('dj-选择字段'),
            initCheckedKeys: [fullPath],
            onCancel: handleSelectFieldModalCancel,
            onSubmit: handleSelectFieldModalSubmit,
          }}
        />
      </CommonSetterLayout>
    </ConfigProvider>
  );
};

export default AthSelectAssociationFieldSetter;
