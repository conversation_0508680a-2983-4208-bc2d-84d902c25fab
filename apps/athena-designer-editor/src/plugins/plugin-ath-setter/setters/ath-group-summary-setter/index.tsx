import React, { Component, useEffect, useMemo, useState } from 'react';
import {
  IPublicEnumTransformStage,
  IPublicModelNode,
  IPublicModelSettingField,
} from '@alilc/lowcode-types';
import './index.scss';
import i18n, { t } from 'i18next';
import { DslData } from '@/tools/business/lcdp-converter/type';
import EditOptionModal from './edit-option-modal';
import OptionItem from './option-item';

import { DndProvider, useDrag, useDrop } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { Option, SelectOptionsItem } from './type';
import { baseOption } from './config';
import CommonSetterLayout, { CommonSetterLayoutProps } from '../../components/common-setter-layout';

export interface AthGroupSummarySetterProps {
  value: Option[];
  field: IPublicModelSettingField;
  options?: {
    titleProps?: CommonSetterLayoutProps;
  };
  onChange: (value: Option[]) => void;
}

const AthGroupSummarySetter: React.FC<AthGroupSummarySetterProps> = (
  props: AthGroupSummarySetterProps,
) => {
  const { value = [], field, onChange, options } = props;
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editIndex, setEditIndex] = useState(-1);
  const editInfo = useMemo(() => {
    return value[editIndex] ?? { ...baseOption };
  }, [value, editIndex]);

  const selectOptions: SelectOptionsItem[] = useMemo(() => {
    if (!field?.node?.children) return [];
    //这是业务逻辑，取到所有TABLE_GROUP组件包裹的子组件，都是字段组件
    return field.node.children
      .filter(
        (child: IPublicModelNode) =>
          child.componentName === 'TABLE_GROUP' &&
          !child.children?.get(0)?.componentName.includes('BUTTON'),
      )
      .map((child: IPublicModelNode) => {
        const { headerName, schema } = child.children?.get(0)?.getPropValue('dslInfo');
        return {
          label: headerName,
          value: schema,
        };
      });
  }, [field]);

  const showModal = (index: number = -1) => {
    setEditIndex(index);
    setIsModalOpen(true);
  };

  const handleOk = (option: Option) => {
    const optionsResult = [...value];

    if (editIndex >= 0) {
      optionsResult.splice(editIndex, 1, option);
    } else {
      optionsResult.push(option);
    }

    onChange(optionsResult);
    setIsModalOpen(false);
  };

  const handleDelete = (index = 0) => {
    const optionsResult = [...value];
    optionsResult.splice(index, 1);
    onChange(optionsResult);
  };

  const handleMove = (fromIndex: number, toIndex: number) => {
    let optionsResult = [...value];
    const [removed] = optionsResult.splice(fromIndex, 1);
    optionsResult.splice(toIndex, 0, removed);
    onChange(optionsResult);
  };

  return (
    <CommonSetterLayout {...(options?.titleProps ?? {})}>
      <div className="ath-group-summary-setter">
        <div className="option-title">
          <span>{t('dj-合计字段')}</span>
          <span
            onClick={() => {
              showModal();
            }}
          >
            +&nbsp;{t('dj-添加')}
          </span>
        </div>
        <div className="option-list">
          <DndProvider backend={HTML5Backend} context={window}>
            {value.map((option, index) => {
              return (
                <OptionItem
                  key={index}
                  index={index}
                  title={`${option.schema}（${option.title}）`}
                  onMove={handleMove}
                  onEdit={showModal}
                  onDelete={handleDelete}
                />
              );
            })}
          </DndProvider>
        </div>

        <EditOptionModal
          title={editIndex >= 0 ? t('dj-修改选项') : t('dj-增加选项')}
          open={isModalOpen}
          editInfo={editInfo}
          selectOptions={selectOptions}
          onCancel={() => {
            setIsModalOpen(false);
          }}
          onOk={handleOk}
        />
      </div>
    </CommonSetterLayout>
  );
};

export default AthGroupSummarySetter;
