
upstream frontend52 {
  server 127.0.0.1:22680;
}
upstream frontend {
  server 127.0.0.1:22681;
}
upstream eshop {
  server 127.0.0.1:22690;
}
upstream backend {
  server 127.0.0.1:22620;
}
upstream mobile {
  server 127.0.0.1:22480;
}
upstream fdfs_group {
#    server **************:8888;
   server *************:8888;
}
upstream ueditor {
#    server **************:8080;
   server *************:8080;
}
upstream adp_fx_backend {
  server 127.0.0.1:22621;
}
upstream adp_fx_frontend {
  server 127.0.0.1:22682;
}
server {

  listen 80;

  include /etc/nginx/conf.d/fenxiao/*.conf;

   location / {
    index index.html index.htm;
    proxy_pass http://frontend52/;
    proxy_http_version 1.1;
    proxy_pass_header Server;
    proxy_set_header Host $http_host;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto https;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Scheme $scheme;
    proxy_connect_timeout 10;
    proxy_read_timeout 300;
    client_max_body_size 20M;
    client_body_buffer_size 1024k;
  }

        location /CROSS/RESTful {
            proxy_pass http://************:9990;

            proxy_set_header digi-key $http_digi_key;
            proxy_set_header digi-protocol $http_digi_protocol;
            proxy_set_header digi-type $http_digi_type;
            proxy_set_header digi-host $http_digi_host;
            proxy_set_header digi-service $http_digi_service;
            proxy_set_header digi-datakey $http_digi_datakey;
        }

        location /athena-designer {
            proxy_pass http://127.0.0.1:22621;
        }

        # location / {
        #  root /ms/lowcode-frontend/dist; # 指定静态文件的根目录
        #  try_files $uri $uri/ /index.html; # 确保单页应用（SPA）能够正确加载
        #  index index.html index.htm; # 默认的首页文件
        # }
}



server {
   listen 9200;

   # include /etc/nginx/conf.d/fenxiao/*.conf;
   
   location / {
    index index.html index.htm;
    proxy_pass http://frontend/;
    proxy_http_version 1.1;
    proxy_pass_header Server;
    proxy_set_header Host $http_host;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto https;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Scheme $scheme;
    proxy_connect_timeout 10;
    proxy_read_timeout 300;
    client_max_body_size 20M;
    client_body_buffer_size 1024k;
  }


}


server {
    listen 9300;
    
    # 全局代理参数
    proxy_http_version 1.1;
    proxy_pass_header Server;
    proxy_set_header Host $http_host;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto https;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Scheme $scheme;
    proxy_connect_timeout 10;
    proxy_read_timeout 300;
    client_body_buffer_size 1024k;

    # 后端服务路由
    location /athena-designer {
        proxy_pass http://adp_fx_backend;
        client_max_body_size 200M;  # 特殊上传需求保留大文件限制
    }

    # 前端服务路由组
    location ~ ^/(athena-designer-editor|athena-designer-core|athena-designer-editor-components|athena-mechanism-core|scheduler) {
        proxy_pass http://adp_fx_frontend;
        client_max_body_size 20M;
    }

    # 默认路由
    location / {
        proxy_pass http://adp_fx_frontend/;
        client_max_body_size 20M;
    }
}


#server {
#    listen 9200; # 监听80端口
#    server_name *************; # 你的域名或IP地址
 
#    location / {
#        root /ms/lowcode-frontend/dist; # 指定静态文件的根目录
#        try_files $uri $uri/ /index.html; # 确保单页应用（SPA）能够正确加载
#        index index.html index.htm; # 默认的首页文件
#    }
 
    # 可以添加额外的location块来处理特定的URI或路径
    # location /api {
    #     proxy_pass http://backend-server; # 例如代理到后端API服务器
    # }
# }
