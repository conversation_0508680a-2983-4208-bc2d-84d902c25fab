const CACHE_NAME = `athena-web-cache-${Date.now()}`; // 使用当前时间戳生成唯一名称;
const urlReg = /https:\/\/[a-zA-Z0-9.-]+\.digiwincloud\.com(?:\.cn)?/;
const URLS_TO_CACHE = [
  'https://alifd.alicdn.com/npm/@alifd/theme-lowcode-light@0.2.1/variables.css',
  'https://alifd.alicdn.com/npm/@alifd/theme-lowcode-light@0.2.1/dist/next.var.min.css',
  'https://uipaas-assets.com/prod/npm/@alilc/lowcode-engine/1.1.4/dist/css/engine-core.css',
  'https://uipaas-assets.com/prod/npm/@alilc/lowcode-engine-ext/1.0.6/dist/css/engine-ext.css',
  '/scheduler/static/lib/lowcode/@alifd/next/1.26.4/next.min.css',
  '/scheduler/static/lib/lowcode/NextTable/1.0.1/next-table.css',
  '/scheduler/static/lib/lowcode/@alilc/lowcode-materials/1.0.7/view.css',
  '/scheduler/static/lib/lowcode/@alifd/layout/2.0.7/view.css',
  '/scheduler/static/lib/lowcode/@alifd/fusion-ui/2.0.0/view.css',
  '/scheduler/static/lib/lowcode/antd/5.18.3/reset.min.css',
  '/scheduler/static/lib/lowcode/react-simulator-renderer.css',
  '/scheduler/static/lib/monaco-editor/vs/editor/editor.main.css',

  'https://g.alicdn.com/code/lib/react/16.13.1/umd/react.production.min.js',
  'https://g.alicdn.com/code/lib/react-dom/16.13.1/umd/react-dom.production.min.js',
  'https://g.alicdn.com/code/lib/prop-types/15.7.2/prop-types.js',
  'https://g.alicdn.com/platform/c/react15-polyfill/0.0.1/dist/index.js',
  'https://g.alicdn.com/platform/c/lodash/4.6.1/lodash.min.js',
  'https://g.alicdn.com/mylib/moment/2.24.0/min/moment.min.js',
  'https://g.alicdn.com/code/lib/alifd__next/1.23.24/next.min.js',
  'https://uipaas-assets.com/prod/npm/@alilc/lowcode-engine/1.1.4/dist/js/engine-core.js',
  'https://uipaas-assets.com/prod/npm/@alilc/lowcode-engine-ext/1.0.6/dist/js/engine-ext.js',
  // '/scheduler/static/lib/lowcode/micro-app-environment.js',
  '/scheduler/static/lib/lowcode/moment/2.24.0/moment.min.js',
  '/scheduler/static/lib/lowcode/dayjs/1.10.4/dayjs.min.js',
  '/scheduler/static/lib/lowcode/lodash/4.6.1/lodash.min.js',
  '/scheduler/static/lib/lowcode/@alifd/next/1.26.4/next-with-locales.min.js',
  '/scheduler/static/lib/lowcode/NextTable/1.0.1/next-table.js',
  '/scheduler/static/lib/lowcode/@alilc/lowcode-materials/1.0.7/view.js',
  '/scheduler/static/lib/lowcode/@alifd/layout/2.0.7/view.js',
  '/scheduler/static/lib/lowcode/@alifd/fusion-ui/2.0.0/view.js',
  '/scheduler/static/lib/lowcode/dayjs/1.11.10/dayjs.min.js',
  '/scheduler/static/lib/lowcode/antd/5.18.3/antd.min.js',
  '/scheduler/static/lib/lowcode/react-simulator-renderer.js',
  'https://at.alicdn.com/t/font_2535522_4oyyibdwj5v.js',
  '/scheduler/static/lib/monaco-editor/vs/loader.js',
  '/scheduler/static/lib/monaco-editor/vs/editor/editor.main.js',
  '/scheduler/static/lib/monaco-editor/vs/editor/editor.main.nls.js',
  '/scheduler/static/lib/monaco-editor/vs/basic-languages/javascript/javascript.js',
  '/scheduler/static/lib/monaco-editor/vs/language/typescript/tsMode.js',
  '/scheduler/static/lib/monaco-editor/vs/base/worker/workerMain.js',
  '/scheduler/static/lib/monaco-editor/vs/base/common/worker/simpleWorker.nls.js',
  '/scheduler/static/lib/monaco-editor/vs/language/typescript/tsWorker.js',
  '/scheduler/static/lib/monaco-editor/vs/language/json/jsonMode.js',
  '/scheduler/static/lib/monaco-editor/vs/language/json/jsonWorker.js',

  'https://i.alicdn.com/artascope-font/20160419204543/font/roboto-thin.woff2',
  'https://i.alicdn.com/artascope-font/20160419204543/font/roboto-bold.woff2',
  'https://i.alicdn.com/artascope-font/20160419204543/font/roboto-regular.woff2',
  'https://i.alicdn.com/artascope-font/20160419204543/font/roboto-medium.woff2',
  'https://at.alicdn.com/t/font_2896606_40w0asgq16c.woff2',
  'https://img.alicdn.com/tfs/TB1CmVgayERMeJjy0FcXXc7opXa-308-200.gif',
  '/scheduler/static/lib/monaco-editor/vs/base/browser/ui/codicons/codicon/codicon.ttf',

  /**
   * 需要处理资源更新
   */
  '/athena-designer-editor/css/index.css',
  '/scheduler/static/assets/iconfont/iconfont-app/iconfont.js',
  '/scheduler/static/assets/iconfont/iconfont-platform/iconfont.js',
  '/athena-designer-editor-components/build/lowcode/view.js',
  '/athena-designer-editor/js/index.js',
  // '/athena-designer-core/remoteEntry.js',
  '/assets/share/hooks.json',
  '/assets/i18n/zh_CN/basic.json',
  '/athena-designer-editor-components/build/lowcode/assets-prod.json',
];

const cacheAssets = async (cache) => {
  for (const asset of URLS_TO_CACHE) {
    try {
      if (asset.startsWith('http') || asset.startsWith('https')) {
        const response = await fetch(asset);
        if (response.ok) {
          await cache.put(asset, response);
          console.log(`[ServiceWorker] Cached full path: ${asset}, response = `, response);
        }
      } else {
        const baseUrl = self.location.origin;
        const fullUrl = new URL(asset, baseUrl).href;
        const response = await fetch(fullUrl);
        if (response.ok) {
          await cache.put(fullUrl, response);
          console.log(`[ServiceWorker] Cached relative path: ${fullUrl}, response = `, response);
        } else {
          console.log(`[ServiceWorker] Cache relative path: ${fullUrl} failure`);
        }
      }
    } catch (error) {
      console.error(`[ServiceWorker] Failed to cache ${asset}:`, error);
    }
  }
};

self.addEventListener('install', (event) => {
  console.log('[ServiceWorker] Installing..., CACHE_NAME = ', CACHE_NAME);
  event.waitUntil(
    caches
      .open(CACHE_NAME)
      .then(cacheAssets)
      .then(() => {
        console.log('[ServiceWorker] ServiceWorker take control of the page...');
        return self.skipWaiting(); // 返回 Promise
      }),
  );
});

self.addEventListener('activate', (event) => {
  console.log('[ServiceWorker] Activating...');
  event.waitUntil(
    caches
      .keys()
      .then((cacheNames) => {
        console.log('[ServiceWorker] previous deleting old cache, CACHE_NAME = ', CACHE_NAME);
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (cacheName !== CACHE_NAME) {
              console.log('[ServiceWorker] Deleting old cache, cacheName = ', cacheName);
              return caches.delete(cacheName);
            }
            return Promise.resolve(); // 避免 map 中返回 undefined
          }),
        );
      })
      .then(() => {
        console.log('[ServiceWorker] ServiceWorker take control of the page...');
        return self.clients.claim(); // 返回 Promise
      })
      .catch((error) => {
        console.error('[ServiceWorker] Activation failed:', error);
      }),
  );
});

const updateCache = (request, cachedResponse) => {
  return new Promise((resolve) => {
    console.log('[ServiceWorker] try to update cache...');
    const cachedETag = cachedResponse.headers.get('ETag');
    if (cachedETag && urlReg.test(request.url)) {
      console.log('[ServiceWorker] need to check cache dirty or not...');
      const fetchOptions = {
        headers: {
          'If-None-Match': cachedETag,
        },
      };
      fetch(request, fetchOptions)
        .then((networkResponse) => {
          if (networkResponse.ok) {
            console.log('[ServiceWorker] exist update, update cache...');
            caches.keys().then((cacheNames) => {
              cacheNames.map((cacheName) => {
                caches.open(cacheName).then((cache) => {
                  cache.put(request, networkResponse.clone());
                });
              });
            });
            resolve(networkResponse);
          } else {
            console.log('[ServiceWorker] cache still freshes, return cache...');
            resolve(cachedResponse);
          }
        })
        .catch((error) => {
          console.log('[ServiceWorker] update cache error, return cache...', error);
          resolve(cachedResponse);
        });
    } else {
      console.log('[ServiceWorker] cdn assets...');
      resolve(cachedResponse);
    }
  })
};

// 拦截网络请求，优先返回缓存数据
self.addEventListener('fetch', (event) => {
  const requestUrl = event.request.url;
  if (!URLS_TO_CACHE.some(url => {
    if (url.startsWith('http') || url.startsWith('https')) {
      return url === requestUrl;
    } else {
      const baseUrl = self.location.origin;
      const fullUrl = new URL(url, baseUrl).href;
      return fullUrl === requestUrl;
    }
  })) {
    return;
  }
  event.respondWith(
    caches.match(event.request, { ignoreVary: true }).then((response) => {
      if (response) {
        console.log(`[ServiceWorker]: get data from ServiceWorker, url = `, event.request.url);
        updateCache(event.request, response.clone()).then((resp) => {
          return resp;
        });
      }
      console.log(`[ServiceWorker]: get data from network, url = `, event.request.url);
      return fetch(event.request);
    }),
  );
});
