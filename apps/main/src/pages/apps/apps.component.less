
.home-item-description {
    padding: 0;
    padding-bottom: 16px;
    font-size: 13px;
    color: #666666;
    font-weight: 400;
    font-family: PingFang SC, PingFang SC-Regular;
  }

  .home-link {
    color: #2012d9;
    font-size: 13px;
    margin-left: 6px;
    cursor: pointer;
  }

  .hot-app {
    margin-left: 0;
    font-size: 18px;
    font-family: PingFang SC, PingFang SC-Medium;
    font-weight: 500;
    text-align: left;
    color: #333333;
  }
:host {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  ::ng-deep {
    .ag-header {
      background-color: #f8fafb;
    }
    .ag-root-wrapper {
      border: unset;
    }
    .ag-row-odd {
      background-color: unset;
    }
    .ag-cell {
      display: flex;
      align-items: center;

      app-custom-render-name,
      app-custom-column-tooltip {
        width: 100%;
      }
    }
  }

  .toggle-btn {
    background-color: #fafafa;
    padding: 2px;
    border-radius: 4px;
    .icon {
      font-size: 15px;
    }
    ::ng-deep {
      .ant-radio-button-wrapper {
        height: 24px;
        background-color: transparent;
        border-color: transparent;
        color: #6a4cff50;
        &:hover {
          color: #6a4cff;
        }
      }
      .ant-radio-button-wrapper-checked {
        border-color: transparent;
        background-color: #fff;
        color: #6a4cff;
        box-shadow: 0px 1px 3px 0px rgba(202, 202, 202, 0.26);
        &::before,
        &::after {
          background-color: transparent;
        }
      }
      .ant-radio-button-wrapper:first-child {
        border-top-left-radius: 5px;
        border-bottom-left-radius: 5px;
      }
      .ant-radio-button-wrapper:last-child {
        border-top-right-radius: 5px;
        border-bottom-right-radius: 5px;
      }
    }
  }
}
.solution-center {
  padding: 16px 20px;
  background: #ffffff;
  border-radius: 8px;
  margin-bottom: 16px;
  .title {
    font-size: 18px;
    font-weight: 500;
    color: #333333;
    margin: 0;
  }
  .description {
    padding-top: 5px;
    font-size: 13px;
    color: #666666;
  }
  .enter-box {
    height: 36px;
    font-size: 14px;
    font-weight: 500;
    padding-top: 6px;
    margin-bottom: 0;
    display: flex;
    align-items: center;
    > li {
      cursor: pointer;
      padding: 0 16px;
      display: flex;
      justify-content: center;
      align-items: center;
      height: 20px;
      min-width: 106px;
      &:first-child {
        padding-left: 0;
      }
      &:nth-child(2) {
        border-left: 1px solid #d5d5d5;
        border-right: 1px solid #d5d5d5;
      }
      img {
        width: 20px;
      }
      > span {
        margin-left: 6px;
      }
    }
    .active-category {
      color: #2012d9;
    }
  }

  .create-types {
    display: grid;
    grid-template-columns: repeat(5, minmax(0, 1fr));
    grid-gap: 16px;
    padding-top: 16px;
    min-height: 146px;
    .create-item {
      display: flex;
      align-items: center;
      padding: 24px 28px;
      height: 129px;
      background: #f1f5ff;
      border-radius: 8px;
      cursor: pointer;
      margin-bottom: 16px;
      &:hover {
        transition: all 0.2s;
        border: 1px solid #6a4cff;
        box-shadow: 0 1px 5px 0 rgba(126, 126, 126, 0.5);
        transform: translateY(-4px);
      }
      .img-box {
        width: 70px;
        min-width: 70px;
        height: 70px;
        margin-right: 18px;
        img {
          display: block;
          height: 100%;
          width: 100%;
        }
      }
      .item-info {
        display: flex;
        flex-direction: column;
        .info-title {
          font-size: 18px;
          // font-family: PingFangSC, PingFangSC-Medium;
          font-weight: 500;
          color: #000000;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
          margin-bottom: 4px;
        }
        .description {
          font-size: 13px;
          font-weight: 400;
          color: #666666;
          line-height: 18px;
          overflow: hidden;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
        }
        .app-link {
          color: #2012d9;
          font-size: 13px;
          cursor: pointer;
          margin-top: 16px;
        }
      }
    }
  }
  @media (min-width: 1601px) {
    .create-types {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }
  @media (min-width: 1340px) and (max-width: 1600px) {
    .create-types {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }

  @media (min-width: 1040px) and (max-width: 1339px) {
    .create-types {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }

  @media (min-width: 758px) and (max-width: 1039px) {
    .create-types {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }

  @media (max-width: 757px) {
    .create-types {
      grid-template-columns: repeat(1, minmax(0, 1fr));
    }
  }
}

.home-container {
  flex: 1;
  // height: 100%;
  width: 100%;
  // overflow-y: hidden;
  // overflow-x: hidden;
  position: relative;
  padding: 10px;
  background-color: #fff;
  padding: 16px 20px;
  background: #ffffff;
  border-radius: 8px;
  .adempty {
    margin-top: 100px;
  }
  .title {
    font-size: 18px;
    color: #333333;
  }
  .head-menu {
    margin-bottom: 8px;
    .menu-item {
      cursor: pointer;
      &:hover {
        color: #6a4cff;
      }
    }
    .selected {
      color: #6a4cff;
    }
    ::ng-deep .ant-divider-vertical {
      height: 15px;
      background: #666666;
      margin: 0 10px;
      line-height: 22px;
    }
  }
  .top-box {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-bottom: 16px;
    .long-error {
      position: absolute;
      font-size: 12px;
    }
    .top-left {
      display: flex;
      ::ng-deep {
        // .adp-select.ant-select:not(.ant-select-customize-input).ant-select:not(.ant-select-customize-input) .ant-select-selector {
        //   height: 27px;
        // }
        .ant-input-group-addon:first-child {
          border: none;
          // transform: translateY(-0.5px);
        }
        .ant-input-group-addon .ant-select {
          margin-top: 0;
          margin-bottom: 0;
        }
        .ant-input-affix-wrapper {
          height: 28px;
        }
        // .ant-select-arrow {
        //   top: 46%;
        // }
        // .ant-select-single.ant-select-sm:not(.ant-select-customize-input).ant-select-show-arrow .ant-select-selection-item,
        // .ant-select-single.ant-select-sm:not(.ant-select-customize-input).ant-select-show-arrow .ant-select-selection-placeholder {
        //   // padding-top: 2px;
        // }
        // .adp-select.ant-select:not(.ant-select-customize-input).ant-select:not(.ant-select-customize-input) .ant-select-selector {

        // }
      }
      .op-search-select {
        width: 115px;
        min-width: 115px;
        right: -1px;
        background-color: #ffffff;
      }
      .auth-search-select {
        margin-left: 10px;
        ::ng-deep .ant-select-selector {
          border-radius: 0px;
        }
      }
    }
    .top-right {
      color: #6a4cff;
      font-size: 14px;
      cursor: pointer;
      display: flex;
      align-items: center;
      .div-btn {
        border: 1px solid;
        border-radius: 4px;
        padding: 2px 4px;
        margin-left: 12px;
      }

      .excel-app {
        margin-right: 15px;
        margin-left: 15px;
      }
    }
  }
  .manage-title {
    color: #6a4cff;
  }
  .content-spin {
    height: calc(100% - 66px);
    padding-bottom: 10px;
  }
  .content-box {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    grid-gap: 16px;
    // :ng-deep {
    .app-card-container {
      width: calc((100% - 3 * 16px) / 4);
    }
    // }
    .visibility-wrap {
      display: block;
      height: 124px;
      border-radius: 8px;
      background-color: #fafafa;
    }
  }

  @media (min-width: 1601px) {
    .content-box {
      ::ng-deep {
        .app-card-container {
          width: calc((100% - 3 * 16px) / 4);
        }
      }
    }
  }
  @media (min-width: 1340px) and (max-width: 1600px) {
    .content-box {
      ::ng-deep {
        .app-card-container {
          width: calc((100% - 3 * 16px) / 4);
        }
      }
    }
  }

  @media (min-width: 1040px) and (max-width: 1339px) {
    .content-box {
      ::ng-deep {
        .app-card-container {
          width: calc((100% - 2 * 16px) / 3);
        }
      }
    }
  }

  @media (min-width: 758px) and (max-width: 1039px) {
    .content-box {
      ::ng-deep {
        .app-card-container {
          width: calc((100% - 16px) / 2);
        }
      }
    }
  }

  @media (max-width: 757px) {
    .content-box {
      ::ng-deep {
        .app-card-container {
          width: 100%;
        }
      }
    }
  }
}

::ng-deep .app-empty-container {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
  ::ng-deep .ant-empty-image {
    margin-bottom: 6px;
  }
}
::ng-deep {
  .ag-gird-table {
    height: calc(100% - 66px);
    .ag-header-cell-menu-button {
      display: none;
    }
  }
}
.app-card-operate {
  cursor: pointer;
  & > div {
    padding: 6px 12px;
    &:hover {
      background: #eef0fe;
    }
  }
}

.table-wrapper {
  display: block;
  min-height: calc(100% - 80px);
  position: relative;
  .apps-table {
    position: absolute;
    width: 100%;
    height: 100%;
  }
  ::ng-deep {
    nz-table .ant-table-header {
      height: 48px;
      .ant-table-thead {
        height: 48px;
      }
    }
    nz-table .ant-table-header table .ant-table-thead > tr > th {
      &:first-child {
        padding-left: 24px;
      }
      &:last-child {
        padding-right: 24px;
      }
    }
    nz-table .ant-table table .ant-table-tbody .ant-table-row td {
      height: 48px;
      &:first-child {
        padding-left: 24px;
      }
      &:last-child {
        padding-right: 24px;
      }
    }
  }
}

.op-icon {
  margin-right: 10px;
  color: #6a4cff;
  font-size: 14px;
  &:last-child {
    margin-right: 0;
  }
}
