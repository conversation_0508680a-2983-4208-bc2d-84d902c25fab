.form-info {
  .description ::ng-deep {
    .ant-input-suffix {
      align-items: unset;
    }
  }
  ::ng-deep .ant-form-item-has-error .add-app-icon {
    border-color: red;
  }
}

::ng-deep .add-app-modal {
  .form-info {
    padding-left: 4px;
  }
  .form-item-layout {
    display: flex;
    .form-item-icon {
      width: 80px;
    }
    .form-item-common {
      flex: 1;
    }
  }
  .ant-form-item {
    font-size: 13px;
    color: #333333;
    margin-bottom: 16px;

    .control-tip {
      font-size: 12px;
      margin-top: 4px;
      color: #999999;
    }
  }

  .upload-zip-modal {
    .icon {
      font-size: 20px;
    }
    .show-file {
      display: flex;
      align-items: center;
      height: 48px;
      .show-file-excel {
        width: 20px;
        color: #289939;
      }
      .show-file-name {
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .show-file-size {
        width: 100px;
        text-align: right;
      }
      .show-file-close {
        width: 20px;
        text-align: right;
        &:hover {
          color: #6A4CFF;
        }
      }
    }
    ::ng-deep nz-spin {
      height: calc(100% - 52px);
    }
    ::ng-deep .ant-upload-drag {
      width: 348px;
      height: 216px;
      border: 2px dashed #d9d9d9;
      p.ant-upload-text {
        font-size: 14px;
      }
      .ant-btn {
        border-radius: 45px;
        margin-top: 23px;
      }
    }
    ::ng-deep .footer-buttons {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      margin-top: 24px;
      .ant-btn-primary {
        margin-left: 24px;
      }
    }
  }

  .upload-zip-progress {
    .progress-item {
      display: flex;
      line-height: 32px;
      align-items: center;
      .progress-title {
        width: 50px;
        align-self: flex-start
      }
      .progress-content {
        flex: 1;
        max-height: 250px;
        overflow-y: auto;
      }
    }
    .progress-fail {
      width: 100%;
      text-align: center;
      margin-bottom: 12px;
    }
  } 

  .modal-footer {
    text-align: center;
    padding-top: 10px;
    button {
      width: 88px;
      height: 32px;
      border-radius: 20px;
      margin: 0 11px;
      &:not(.ant-btn-primary) {
        border-color: #6A4CFF;
        color: #6A4CFF;
        &:hover {
          border-color: #5a4fee;
          color: #5a4fee;
        }
      }
    }
  }
}

::ng-deep .repeat-modal {
  .i-text-dark {
    color: #333333;
    font-size: 14px;
    padding-left: 50px;
  }

  .form-info {
    padding-left: 4px;
    padding: 20px 50px;
  }
  .ant-form-item {
    font-size: 13px;
    color: #333333;
    margin-bottom: 16px;
  }

  .modal-footer {
    text-align: center;
    padding-top: 10px;
    button {
      width: 88px;
      height: 32px;
      border-radius: 20px;
      margin: 0 11px;
      &:not(.ant-btn-primary) {
        border-color: #6A4CFF;
        color: #6A4CFF;
        &:hover {
          border-color: #5a4fee;
          color: #5a4fee;
        }
      }
    }
  }
}
