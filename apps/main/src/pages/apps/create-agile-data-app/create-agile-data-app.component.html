<!--新建解决方案-->
<nz-modal
  nzClassName="add-app-modal"
  [nzWidth]="'400px'"
  [nzVisible]="emptyVisible"
  [nzTitle]="'dj-新建解决方案' | translate"
  [nzClosable]="!progressShow"
  [nzMaskClosable]="false"
  [nzFooter]="null"
  [nzClosable]="true"
  (nzOnCancel)="handleCancelApp()"
  (nzAfterClose)="handleModalClose()"
>
  <ng-container *nzModalContent>
    <nz-spin [nzSpinning]="saveAppLoading">
      <div *ngIf="!progressShow">
        <form nz-form [formGroup]="appForm" [nzNoColon]="true" class="form-info login-form" [nzLayout]="'vertical'">
          <nz-form-item *ngIf="agileDataImportAuth">
            <nz-form-control>
              <nz-radio-group formControlName="way" ngDefaultControl (ngModelChange)="handleWayChanged($event)">
                <label nz-radio nzValue="default">
                  {{ 'dj-创建空白解决方案' | translate }}
                </label>
                <label nz-radio nzValue="zip">
                  {{ 'dj-基于已有解决方案创建新解决方案' | translate }}
                </label>
              </nz-radio-group>
            </nz-form-control>
          </nz-form-item>
          <nz-form-item>
            <nz-form-control
              [nzErrorTip]="
                'dj-请输入解决方案名称，且字符长度不超过' + (params?.appType === 12 ? '8' : '40')
                  | translate: { m: params?.appType === 12 ? 8 : 40 }
              "
            >
              <app-modal-input-agile
                ngDefaultControl
                formControlName="name"
                [attr]="{
                  name: '名称',
                  required: true,
                  lang: appLang?.name,
                  maxLength: params?.appType === 12 ? 8 : 40,
                  needLang: true
                }"
                (callBack)="handlePatchLangApp('name', $event)"
              ></app-modal-input-agile>
            </nz-form-control>
          </nz-form-item>

          <nz-form-item>
            <nz-form-control [nzErrorTip]="userErrorTpl">
              <app-modal-input
                ngDefaultControl
                formControlName="code"
                [attr]="{
                  name: '代号',
                  required: true,
                  addOnAfter: appCodeEnv
                }"
                [value]="appForm.get('code')?.value"
                (callBack)="handlePatchApp('code', $event)"
              ></app-modal-input>
              <ng-template #userErrorTpl let-control>
                <ng-container *ngIf="control.hasError('required')">{{ 'dj-请输入' | translate }}</ng-container>
                <ng-container *ngIf="control.hasError('minlength')">{{ 'dj-最少2位字符' | translate }}</ng-container>
                <ng-container *ngIf="control.hasError('maxlength')">{{ 'dj-最多10位字符' | translate }}</ng-container>
                <ng-container *ngIf="control.hasError('duplicated')">{{ 'dj-app-regular4' | translate }}</ng-container>
                <!--<ng-container *ngIf="control.hasError('duplicated')">{{
                  (isInternalTenant(teamId) ? 'dj-app-regular4' : 'dj-app-regular3') | translate
                }}</ng-container>-->
              </ng-template>
            </nz-form-control>
          </nz-form-item>

          <div *ngIf="appForm.get('way').value === 'default'">
            <div class="form-item-layout" *ngIf="params?.appType !== 12">
              <div class="form-item-icon">
                <nz-form-item>
                  <nz-form-control
                    [nzErrorTip]="'dj-必填！' | translate"
                    [nzValidateStatus]="!appForm.get('iconName').value && appForm.get('iconName').dirty ? 'error' : ''"
                  >
                    <app-add-app-icon
                      [required]="true"
                      [label]="'dj-解决方案图标' | translate"
                      [value]="value"
                      (change)="onAppIconChange($event)"
                    ></app-add-app-icon>
                  </nz-form-control>
                </nz-form-item>
              </div>
            </div>

            <div *ngIf="params?.appType === 12">
              <nz-form-item>
                <nz-form-control>
                  <app-add-app-upload-image
                    [label]="'dj-解决方案图标' | translate"
                    (change)="onAppImageChange($event)"
                  ></app-add-app-upload-image>
                </nz-form-control>
              </nz-form-item>
            </div>
            <nz-form-item *ngIf="params?.appType !== 12">
              <nz-form-control>
                <nz-radio-group formControlName="source" ngDefaultControl>
                  <label nz-radio nzValue="dcp">
                    {{ 'dj-连接地端系统' | translate }}
                  </label>
                  <label nz-radio nzValue="bmd">
                    {{ 'dj-连接云端系统' | translate }}
                  </label>
                </nz-radio-group>
                <div class="control-tip">{{ 'dj-依据当前解决方案的数据来源系统选择' | translate }}</div>
              </nz-form-control>
            </nz-form-item>

            <nz-form-item *ngIf="params?.appType !== 12">
              <nz-form-control>
                <nz-radio-group formControlName="appSystem" ngDefaultControl>
                  <label nz-radio nzValue="dataset">
                    {{ 'dj-数据集体系' | translate }}
                  </label>
                  <label nz-radio nzValue="metric">
                    {{ 'dj-指标场景体系' | translate }}
                  </label>
                </nz-radio-group>
              </nz-form-control>
            </nz-form-item>

            <nz-form-item>
              <nz-form-control [nzErrorTip]="'dj-请输入' | translate">
                <app-textarea-input-agile
                  class="description"
                  ngDefaultControl
                  formControlName="description"
                  [innerLabel]="true"
                  [attr]="{
                    type: 'textarea',
                    name: '描述',
                    required: true,
                    lang: appLang?.description,
                    needLang: true,
                    maxLength: 200
                  }"
                  (callBack)="handlePatchLangApp('description', $event)"
                ></app-textarea-input-agile>
              </nz-form-control>
            </nz-form-item>

            <div *ngIf="params?.appType === 12">
              <nz-form-item>
                <nz-form-label>{{ 'dj-开场白' | translate }}</nz-form-label>
                <nz-form-control>
                  <app-textarea-input-agile
                    class="introduction"
                    ngDefaultControl
                    formControlName="introduction"
                    [innerLabel]="false"
                    [attr]="{
                      type: 'textarea',
                      maxCharacterCount: 38,
                      maxLength: 38,
                      lang: appLang?.introduction,
                      needLang: true,
                      placeholder: '用于该应用发布后运行态对话开场白，引导用户开启对话'
                    }"
                    (callBack)="handlePatchLangApp('introduction', $event)"
                  ></app-textarea-input-agile>
                </nz-form-control>
              </nz-form-item>

              <nz-form-item>
                <nz-form-label>{{ 'dj-输入框文案' | translate }}</nz-form-label>
                <nz-form-control [nzErrorTip]="'dj-最多输入20个文字' | translate">
                  <app-modal-input-agile
                    ngDefaultControl
                    formControlName="prompt"
                    [innerLabel]="false"
                    [attr]="{
                      name: '输入框文案',
                      lang: appLang?.prompt,
                      needLang: true,
                      maxLength: 20,
                      placeholder: '例：请直接像我提问，最多输入20个文字'
                    }"
                    (callBack)="handlePatchLangApp('prompt', $event)"
                  ></app-modal-input-agile>
                </nz-form-control>
              </nz-form-item>
            </div>
          </div>

          <div *ngIf="appForm.get('way').value === 'zip'" class="upload-zip-modal">
            <nz-spin [nzSpinning]="saveAppLoading" nzTip="正在上传...">
              <nz-upload
                nzType="drag"
                nzAccept=".zip"
                [nzLimit]="1"
                [nzDisabled]="!!uploadFile"
                [nzBeforeUpload]="beforeUpload"
                [nzFileListRender]="fileTemplateRef"
              >
                <ng-container>
                  <i adIcon [iconfont]="'iconupload1'" aria-hidden="true" class="icon"></i>
                  <p class="ant-upload-text">{{ 'dj-选择或拖拽文件到这里上传' | translate }}</p>
                  <p class="ant-upload-hint">
                    {{ 'dj-支持5MB以内的.zip格式文件，仅支持一个文件' | translate }}
                  </p>
                </ng-container>
                <ng-template #fileTemplateRef>
                  <div class="show-file" *ngIf="uploadFile">
                    <div class="show-file-excel">
                      <i adIcon type="file-text" theme="outline"></i>
                    </div>
                    <div class="show-file-name">{{ uploadFile.name }}</div>
                    <div class="show-file-size">{{ (uploadFile.size! / 1024).toFixed(2) }}KB</div>
                    <div class="show-file-close">
                      <i (click)="removeFile()" adIcon type="close-circle" theme="outline"></i>
                    </div>
                  </div>
                </ng-template>
              </nz-upload>
            </nz-spin>
          </div>
        </form>
      </div>
      <div *ngIf="progressShow" class="upload-zip-progress">
        <div *ngIf="progressApp">
          <nz-alert
            *ngIf="progressApp?.progress >= 0 && progressApp?.progress < 100"
            nzType="info"
            [nzMessage]="'dj-上传成功，正在创建数据...' | translate"
            nzShowIcon
          ></nz-alert>
          <nz-alert
            *ngIf="progressApp?.progress === -100"
            nzType="error"
            [nzMessage]="'dj-新解决方案创建失败' | translate"
            nzShowIcon
          ></nz-alert>
          <nz-alert
            *ngIf="progressApp?.progress === 100"
            nzType="success"
            [nzMessage]="'dj-新解决方案创建成功' | translate"
            nzShowIcon
          ></nz-alert>
          <div class="progress-item">
            <div class="progress-title">{{ 'dj-任务' | translate }}:</div>
            <div class="progress-content">{{ progressApp?.taskId }}</div>
          </div>
          <div class="progress-item">
            <div class="progress-title">{{ 'dj-代号' | translate }}:</div>
            <div class="progress-content">{{ progressApp?.application }}</div>
          </div>
          <div class="progress-item">
            <div class="progress-title">{{ 'dj-进度' | translate }}:</div>
            <div *ngIf="progressApp?.progress === -100" class="progress-content">
              <nz-progress [nzPercent]="progressApp?.progress || 0" nzStatus="exception"></nz-progress>
            </div>
            <div *ngIf="progressApp?.progress >= 0" class="progress-content">
              <nz-progress [nzPercent]="progressApp?.progress || 0"></nz-progress>
            </div>
          </div>
          <div *ngIf="progressApp?.progress === -100" class="progress-item">
            <div class="progress-title">{{ 'dj-原因' | translate }}:</div>
            <div class="progress-content">{{ progressApp?.message }}</div>
          </div>
        </div>
        <div *ngIf="!progressApp" class="progress-fail">
          {{ 'dj-导入成功，创建进度查询失败' | translate }}
        </div>
      </div>
      <div class="modal-footer">
        <div *ngIf="!progressShow">
          <button ad-button adType="default" (click)="handleCancelApp()">
            {{ 'dj-取消' | translate }}
          </button>
          <button ad-button adType="primary" (click)="handleSaveApp()">
            {{ 'dj-确定' | translate }}
          </button>
        </div>
        <div *ngIf="progressShow">
          <button *ngIf="!progressApp || progressApp?.progress === -100" ad-button (click)="handleCloseProcess()">
            {{ 'dj-关闭' | translate }}
          </button>
          <button *ngIf="progressApp?.progress === 100" ad-button adType="primary" (click)="handleEnterApp()">
            {{ 'dj-进入解决方案' | translate }}
          </button>
        </div>
      </div>
    </nz-spin>
  </ng-container>
</nz-modal>

<nz-modal
  [nzVisible]="repeatVisible"
  [nzTitle]="'dj-解决方案已存在' | translate"
  (nzOnCancel)="repeatVisible = false"
  [nzFooter]="null"
  nzClassName="repeat-modal"
  [nzWidth]="'460px'"
>
  <ng-container *nzModalContent>
    <nz-spin [nzSpinning]="saveAppLoading">
      <p class="i-text-dark">{{ 'dj-请输入正确appToken进行校验' | translate }}</p>
      <div class="m10">
        <form nz-form [formGroup]="repeatForm" [nzNoColon]="true" class="form-info">
          <nz-form-item>
            <nz-form-control [nzErrorTip]="errorTpl">
              <input type="text" nz-input formControlName="appToken" [placeholder]="'dj-请输入' | translate" />
              <ng-template #errorTpl let-control>
                <ng-container *ngIf="control.hasError('required')">{{ 'dj-必填！' | translate }}</ng-container>
              </ng-template>
            </nz-form-control>
          </nz-form-item>
        </form>
        <div class="modal-footer">
          <button ad-button adType="default" (click)="handleCancelRepeat()">
            {{ 'dj-取消' | translate }}
          </button>
          <button ad-button adType="primary" (click)="handleSaveRepeat()">
            {{ 'dj-确定' | translate }}
          </button>
        </div>
      </div>
    </nz-spin>
  </ng-container>
</nz-modal>
