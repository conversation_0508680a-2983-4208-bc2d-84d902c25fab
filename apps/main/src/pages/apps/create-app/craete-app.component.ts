import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { ExcelAppService } from './excel-app/excel-app.service';
import { DatePipe } from '@angular/common';
import { AdUserService } from 'pages/login/service/user.service';
import {
  AbstractControl,
  AsyncValidatorFn,
  FormArray,
  FormBuilder,
  FormGroup,
  ValidationErrors,
  Validators,
} from '@angular/forms';
import { AppService } from '../app.service';
import { ComponentsService } from 'common/service/components.service';
import { TranslateService } from '@ngx-translate/core';
import { NzMessageService } from 'ng-zorro-antd/message';
import { isInternalTenant, S4 } from 'common/utils/core.utils';
import { AppTypes } from 'pages/app/typings';
import { SystemConfigService } from 'common/service/system-config.service';
import { GlobalService } from 'common/service/global.service';
import { TenantService } from 'pages/login/service/tenant.service';
import _ from 'lodash';
import { first } from 'rxjs/operators';
import { SolutionCardService } from 'app/service/solution-card.service';
import { LocaleService } from 'common/service/locale.service';

export interface IResourceInfo {
  envCode?: string;
  envName?: string;
  hostId?: string;
  hostName?: string;
  dbId?: string;
  dbName?: string;
}

@Component({
  selector: 'app-create-app',
  templateUrl: './create-app.component.html',
  styleUrls: ['./create-app.component.less'],
  providers: [ComponentsService],
})
export class CreateAppComponent implements OnInit, OnChanges {
  /**
   * 弹窗类型
   */
  @Input() opType: 'edit' | 'create' = 'create';
  @Input() type: string;
  @Input() visible: boolean = false;
  // 提交时params也一起提交
  @Input() params: any = {};
  @Input() isHomePage: boolean = true;
  // 修改解决方案时需要传入
  @Input() appCode: string;
  // 是否忽略提交之后的successCallback, 编辑进来的需要忽略，调用方会进行后续处理
  @Input() ignoreSuccessCallback: boolean = false;
  @Output() afterCreated: EventEmitter<any> = new EventEmitter();
  @Output() readonly visibleChange = new EventEmitter<boolean>();
  @Output() goApp = new EventEmitter();
  @Output() refreshAppList = new EventEmitter();

  noServiceCodes: string[] = [];
  nzAutoTips: Record<string, Record<string, string>> = {
    default: {
      required: this.translateService.instant('dj-请选择或输入'),
      serviceCodeError:
        this.noServiceCodes.join('、') + this.translateService.instant('dj-产品名称不存在，请重新选择或输入'),
    },
  };
  get serviceCodeStatus() {
    const control = this.appForm.get('serviceCode');
    return control?.pending ? 'validating' : control;
  }

  repeatVisible: boolean = false;
  tenantId: string;
  teamId: string;
  appForm: FormGroup;
  repeatForm: FormGroup;
  appLang: any;
  saveAppLoading: boolean;
  repeatAppData: any;
  value: any;
  emptyVisible: boolean;
  logActiveEnvCode: string = '';
  // 解决方案后端
  serviceVisible: boolean = false;
  targetList: any[] = [];
  applicationCode: string = '';

  logVisible: boolean = false;
  newAppCode: string = '';
  codeList: any[] = [];

  // 所有可用资源
  appParams: any;
  availableServiceList: any[] = [];
  envList: any[] = [];
  hostList: any[] = [];
  rdsList: any[] = [];
  appCodeEnv: string;
  developerPortalUrl: string;
  /**
   * 是否禁用无解决方案后端选项，修改创建时选择了非无解决方案后端选项的场景需要禁用无解决方案后端选项
   */
  disabledNoServerType: boolean = false;
  /**
   * 缓存已经校验过的serviceCode
   * key: string - serviceCode
   * value: boolean - true - 有报错，false - 无报错
   */
  cacheCheckedServiceCodeMap = new Map<string, boolean>();
  /**
   * 编辑时，创建失败的服务名
   */
  failureService: string[] = [];

  /**
   * 编辑时，pending状态的服务名
   */
  pendingService: string[] = [];
  /**
   * 编辑时，初始化的envCode列表
   */
  editInitServerResourceCode: string[] = [];
  /**
   * 缓存接口的解决方案信息
   */
  cacheApplicationInfo;

  isInternalTenant = isInternalTenant;

  // 是否是数据驱动解决方案
  get isDataDriveApp() {
    return this.params?.appType === AppTypes.DTD;
  }

  get isAiApp() {
    return this.params?.appType === AppTypes.AI_MODEL;
  }

  // 是否是场景化套件解决方案
  get isKITApp() {
    return this.params?.appType === AppTypes.SCENARIO_KIT;
  }

  // 是否是模型驱动解决方案
  get isModelDriveApp() {
    return this.params?.appType === AppTypes.MODEL_DRIVEN;
  }

  //是否是高代码应用
  get isHighApp() {
    return this.params?.appType === AppTypes.HIGH_CODE;
  }

  get excelAppVisible() {
    return this.visible && this.type === 'excel';
  }

  get createType(): boolean {
    return this.appForm.controls?.createType?.value || 1;
  }

  set excelAppVisible(val) {
    this.excelAppService.excelAppVisible = val;
    this.visibleChange.emit(val);
  }

  /**
   * 是不是体验用户 + 体验环境
   */
  get userIsExperience() {
    return this.tenantService.isEduAndisExperience();
  }

  // 获取 资源列表 的 FormArray
  get serverResources(): FormArray {
    return this.appForm.get('serverResources') as FormArray;
  }

  /**
   * 弹窗标题
   */
  get modalTitle(): string {
    return this.opType === 'edit' ? '修改解决方案' : '新建解决方案';
  }

  get codeListMap() {
    return new Map(this.codeList?.map((code) => [code.value, 0]) ?? []);
  }

  transferAppNotNeedService = false; // 从工作台应用迁移时，不需要服务,编辑的时候 也不需要维护服务

  showErrorMessage: boolean = false;
  addResourceTip: string = '';

  dynamicsAttributes: any[];

  constructor(
    private excelAppService: ExcelAppService,
    private datePipe: DatePipe,
    private userService: AdUserService,
    private app: AppService,
    private fb: FormBuilder,
    private languageService: LocaleService,
    public componentService: ComponentsService,
    private message: NzMessageService,
    private translateService: TranslateService,
    private configService: SystemConfigService,
    private globalService: GlobalService,
    private tenantService: TenantService,
    public solutionCardService: SolutionCardService,
  ) {

    this.configService.getConfig().subscribe((config: any) => {
      this.appCodeEnv = config.appCodeEnv;
      this.developerPortalUrl = config.developerPortalUrl;
    });
    this.tenantId = this.userService.getUser('tenantId');
    this.teamId = this.userService.getUser('teamId');
    this.applicationCode = this.getDefaultCode();
    this.appForm = this.fb.group({
      code: [
        this.getDefaultCode(),
        [Validators.required, Validators.minLength(2), Validators.maxLength(10)],
        [this.app.codeValidator],
      ],
      name: [null, [Validators.required, Validators.pattern('^.{0,40}$')]],
      description: [null, [Validators.required]],
      iconName: [null, Validators.required],
      iconBgcolor: [null, Validators.required],
      commonApp: [false],
    });
    this.repeatForm = this.fb.group({
      appToken: [null, [Validators.required]],
    });
    // 调用处理动态表单
    this.excelAppService.finish$.subscribe((res) => {
      this.onExcelModalCancel();
      this.afterCreated.emit(res);
    });
  }

  /**
   * 处理动态表单
   * @returns
   */
  handleSetDynamicsAttribute(): void {
    this.dynamicsAttributes = this.solutionCardService.getDynamicsAttributes(this.params?.appType);
    if (this.dynamicsAttributes.length === 0) {
      return;
    }
    // 处理表单
    this.dynamicsAttributes.forEach((attr) => {
      switch (attr.componentType) {
        case 'CHECKBOX':
          if (!this.appForm.get(attr.formControlName)) {
            this.appForm.addControl(attr.formControlName, this.fb.control(attr.defaultValue));
          } else {
            this.appForm.patchValue({
              [attr.formControlName]: attr.defaultValue,
            });
          }
          break;
      }
    });
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (Object.keys(changes).includes('visible') || Object.keys(changes).includes('type')) {
      // this.visible = changes?.visible?.currentValue;
      if (this.visible) {
        // 调用处理动态表单
        this.handleSetDynamicsAttribute();
        this.handleAddApp(this.type);
      }
    }
  }

  async ngOnInit() {
    if (this.opType === 'edit') {
      this.appForm.get('code').setValidators([Validators.required]);
      this.cacheApplicationInfo = await this.queryApplicationDetail();
      this.initEditApp(null, true);
    } else {
      this.initCreateApp();
    }
  }

  /**
   * 新建初始化
   */
  async initCreateApp() {
    this.appForm.patchValue({
      iconName: '1.png',
      iconBgcolor: '#0189FF',
    });
    if (this.params.name && this.params.description) {
      this.appForm.patchValue({
        name: this.params.name,
        description: this.params.description,
      });
      this.appLang = this.params.lang;
    }
    this.value = { image: '1.png', color: '#0189FF' };
    this.emptyVisible = this.visible && this.type === 'empty';

    // 创建模型驱动解决方案的额外逻辑
    if (this.isModelDriveApp) {
      this.handleOptionChange(3);
      // 获取已有后端服务
      await this.getServiceCodeList();
      if (this.userIsExperience) {
        const DPBAS = this.codeList.find((e) => e.label === this.tenantService.getExperienceServiceCode())?.value;
        const serviceCode = DPBAS ? [DPBAS] : undefined;
        this.appForm.patchValue({
          createType: 1,
          serviceCode: serviceCode,
        });
      }
    } else if (this.isAiApp) {
      if (!this.appForm.contains('createType')) {
        this.appForm.addControl('createType', this.fb.control(1, Validators.required));
      }
      this.appForm.get('createType').setValue(3);
    }
    // 创建高代码应用的额外逻辑
    if (this.isHighApp) {
      this.appForm.addControl('deploymentType', this.fb.control('backend', Validators.required));
      this.handleInitHightCodeResources();
    }
  }

  /**
   * 编辑/切换初始化
   * @param {string} createType - 类型, 编辑初始化不传，使用接口createType 切换初始化需要传
   */
  async initEditApp(currentCreateType?: number, initValue: boolean = false) {
    if (this.cacheApplicationInfo) {
      const iconName = this.cacheApplicationInfo.application.iconName ?? '1.png';
      const iconBgcolor = this.cacheApplicationInfo.application.iconBgcolor ?? '#0189FF';
      if (initValue) {
        this.appForm.patchValue({
          iconName,
          iconBgcolor,
          code: this.cacheApplicationInfo.application.code ?? '',
          name: this.cacheApplicationInfo.application.name ?? null,
          description: this.cacheApplicationInfo.application.description ?? null,
        });
        this.appLang = this.cacheApplicationInfo.application.lang;
        this.value = { image: iconName, color: iconBgcolor };
      }
      this.emptyVisible = this.visible && this.type === 'empty';
      // 创建模型驱动解决方案的额外逻辑
      if (this.isModelDriveApp) {
        const createType = currentCreateType ? currentCreateType : this.cacheApplicationInfo.createType ?? 1;
        this.appForm.addControl('createType', this.fb.control(createType, Validators.required));
        if (createType === 1) {
          const serviceCode = this.cacheApplicationInfo.serviceCode ?? null;
          /**
           * 避免初始化校验
           */
          this.codeList = serviceCode?.map((info: string) => ({ value: info }));
          const serviceCodeControl = this.fb.control(serviceCode, {
            validators: Validators.required,
            asyncValidators: this.serviceCodeValidator(),
          });
          this.appForm.addControl('serviceCode', serviceCodeControl);
          this.appForm.removeControl('serverResources');
          this.disabledNoServerType = this.cacheApplicationInfo.createType !== 3;
          // 获取已有后端服务
          this.getServiceCodeList();
        } else if (createType === 2) {
          // 新建解决方案后端
          const currentEnvMap = new Map();
          const serverResources = this.cacheApplicationInfo.serverResources ?? [];
          this.failureService = [];
          this.pendingService = [];
          serverResources.forEach((service) => {
            currentEnvMap.set(service.envCode, 0);
            if (service.status === 'fail') {
              this.failureService.push(service.envName);
            }
            if (['multi_pending', 'pending'].includes(service.status)) {
              this.pendingService.push(service.envName);
            }
          });
          const serverResourcesFormArray = serverResources.map((resource) => {
            this.editInitServerResourceCode.push(resource.envCode);
            return this.createNewResource(resource);
          });
          this.appForm.addControl(
            'serverResources',
            this.fb.array(
              serverResourcesFormArray.length === 0 ? [this.createNewResource()] : serverResourcesFormArray,
              this.minResourceLength(1),
            ),
          );
          this.appForm.removeControl('serviceCode');
          this.disabledNoServerType = this.cacheApplicationInfo.createType !== 3;
          this.getAvailableServiceList().then(() => {
            this.setEnvList();
            serverResources.forEach((source, index) => {
              this.dealEnvChange(index, source.envCode, false);
            });
          });
        } else if (createType === 3) {
          this.appForm.removeControl('serviceCode');
          this.appForm.removeControl('serverResources');
        }
      }
      if (this.isHighApp) {
        this.transferAppNotNeedService = !(
          this.cacheApplicationInfo.application?.hasOwnProperty('deploymentType') &&
          this.cacheApplicationInfo.application.deploymentType
        );
        this.handleHighAppEdit();
      }
    }
  }

  /**
   *  编辑高代码应用
   */
  handleHighAppEdit() {
    if (this.transferAppNotNeedService) {
      return;
    }
    this.appForm.addControl(
      'deploymentType',
      this.fb.control(this.cacheApplicationInfo.application.deploymentType, Validators.required),
    );
    this.appForm.get('deploymentType')?.disable();
    const serverResources = this.cacheApplicationInfo.serverResources ?? [];
    this.failureService = [];
    this.pendingService = [];
    serverResources.forEach((service) => {
      if (service.status === 'fail') {
        this.failureService.push(service.envName);
      }
      if (['multi_pending', 'pending'].includes(service.status)) {
        this.pendingService.push(service.envName);
      }
    });
    const hightCodeResourcesFormArray = serverResources.map((resource) => {
      this.editInitServerResourceCode.push(resource.envCode);
      return this.createHightNewResource(resource);
    });
    this.appForm.addControl(
      'serverResources',
      this.fb.array(
        hightCodeResourcesFormArray.length === 0 ? [this.createHightNewResource()] : hightCodeResourcesFormArray,
        this.minHightResourceLength(1),
      ),
    );
    this.getAvailableServiceList({
      appType: AppTypes.HIGH_CODE,
    }).then(() => {
      this.setEnvList();
      serverResources.forEach((source, index) => {
        this.dealHightEnvChange(index, source.envCode, false);
      });
    });
  }

  async queryApplicationDetail() {
    const response = await this.app.getApplicationDetail(this.appCode).toPromise();
    if (response.code === 0 && response.data) {
      return response.data;
    }
    return null;
  }

  handleExcelAppReset() {
    this.excelAppService.reset();
  }

  handleGoApp(appCode) {
    this.afterCreated.emit(this.appParams);
    this.onMoadlCancel();
    this.goApp.emit(appCode);
  }

  serviceCodeValidator(): AsyncValidatorFn {
    return (control: AbstractControl): Promise<ValidationErrors | null> => {
      return new Promise((resolve) => {
        const serviceCode = control.value ?? [];
        if (serviceCode.length === 0) {
          resolve(null);
          return;
        }
        const newServiceCode = serviceCode[serviceCode.length - 1];
        if (this.codeListMap.has(newServiceCode)) {
          resolve(null);
          return;
        }
        if (this.cacheCheckedServiceCodeMap.has(newServiceCode)) {
          const isError = this.cacheCheckedServiceCodeMap.get(newServiceCode);
          resolve(isError ? { serviceCodeError: true } : null);
        } else {
          this.app.checkServiceValid(newServiceCode).subscribe(
            (res) => {
              if (res.code !== 0) {
                resolve({ serviceCodeError: true });
                this.cacheCheckedServiceCodeMap.set(newServiceCode, true);
              } else {
                resolve(null);
                this.cacheCheckedServiceCodeMap.set(newServiceCode, false);
              }
            },
            () => {
              resolve({ serviceCodeError: true });
              this.cacheCheckedServiceCodeMap.set(newServiceCode, true);
            },
          );
        }
      });
    };
  }

  checkServiceCode(codes) {
    const codeValus = this.codeList.map((item) => item.value);
    const result = codes.filter((code) => !codeValus.includes(code));
    return result;
  }

  // resource最少添加一个
  minResourceLength(min: number) {
    return (formArray: AbstractControl): ValidationErrors | null => {
      const array = formArray as FormArray;
      return array.length >= min && array.value.filter((info) => info.envCode && info.hostId && info.dbId).length >= min
        ? null
        : { minLength: '请至少设置一组资源' };
    };
  }

  handleOptionChange(createType: number) {
    if (!this.isModelDriveApp) return;
    // 如果是模型驱动解决方案则需要添加格外的表单字段
    this.appForm.addControl('createType', this.fb.control(createType, Validators.required));
    if (this.opType === 'edit') {
      this.initEditApp(createType);
    } else {
      // 保留原有新建逻辑
      if (createType === 1) {
        this.appForm.addControl(
          'serviceCode',
          this.fb.control(null, Validators.required, [this.serviceCodeValidator()]),
        );
        this.appForm.removeControl('serverResources');
        return;
      } else if (createType === 2) {
        // 新建解决方案后端
        this.getAvailableServiceList().then(() => this.setEnvList());
        this.appForm.removeControl('serviceCode');
        this.appForm.addControl(
          'serverResources',
          this.fb.array([this.createNewResource()], this.minResourceLength(1)),
        );
      } else if (createType === 3) {
        this.appForm.removeControl('serviceCode');
        this.appForm.removeControl('serverResources');
      }
    }
  }

  // 获取已经设置的后端服务code
  async getServiceCodeList(): Promise<void> {
    const res = await this.app.getServiceCodeList().toPromise();
    if (res.code === 0) {
      this.codeList = res.data?.map((item) => {
        return {
          label: item,
          value: item,
        };
      });
    }
  }

  setEnvList(envList = []) {
    const sourceLists = envList.length > 0 ? envList : this.availableServiceList ?? [];
    this.envList = sourceLists.map((item) => {
      return {
        label: item.env,
        value: item.envCode,
      };
    });
  }

  // 获取可用服务资源
  getAvailableServiceList(param?: any) {
    return new Promise((resolve) => {
      this.app.getAvailableServiceList(param).subscribe(
        (res) => {
          if (res.code === 0) {
            this.availableServiceList = [];
            this.showErrorMessage = false;
            this.addResourceTip = '';
            if (res?.data?.code === 200) {
              this.availableServiceList = res.data?.data;
              this.addResourceTip = 'dj-建议测试区和正式区一起创建；创建后不可更改，请谨慎操作！';
              resolve(res.data?.data ?? []);
            }
            if (res?.data?.code === 1000) {
              this.showErrorMessage = true;
            }
            if (res?.data?.code === 1001) {
              this.addResourceTip = res.data?.msg;
            }
          }
        },
        () => {},
      );
    });
  }

  /**
   * 清理host和db数据&下拉选项
   * @param {number} index
   * @param {boolean} clearOptions
   */
  clearHostAndDb(index: number, clearOptions = false, deleteOp = false) {
    if (this.serverResources.length <= index) return;
    if (!deleteOp) {
      this.serverResources.at(index).patchValue({
        hostId: null,
        dbId: null,
      });
    }
    if (clearOptions) {
      this.hostList.splice(index, 1);
      this.rdsList.splice(index, 1);
    }
  }

  afterEnvChange(index: number, clearOptions = false, callClear = true, deleteOp = false) {
    const serverResources = this.serverResources.value ?? [];
    const resourcesMap = new Map(serverResources.map((item) => [item.envCode, 0]));
    /**
     * 处理禁用，每个表单的环境禁用是同步的，每个选项只能一个人选
     */
    this.envList.map((env) => (env.disabled = resourcesMap.has(env.value)));
    /**
     * 环境清理，自动清理主机和数据库选择数据和下拉数据，因为这两者依赖环境
     */
    if (callClear) {
      this.clearHostAndDb(index, clearOptions, deleteOp);
    }
  }

  dealEnvChange(index, envCode, callClear = true) {
    const target = this.availableServiceList.find((item) => item.envCode === envCode);
    this.hostList[index] = this.formatData(target?.hostList);
    this.rdsList[index] = this.formatData(target?.rdsList);
    this.afterEnvChange(index, !envCode, callClear);
  }

  // 环境改变 自动改变主机和数据库的下拉
  handleEnvChange(type, $event, index) {
    const envCode = $event.value;
    const envInfo = this.envList.find((env) => env.value === envCode);
    this.handlePatchApp(type, $event, index);
    if (envInfo) {
      this.handlePatchApp('envName', { value: envInfo.label }, index);
    }
    this.dealEnvChange(index, envCode);
  }

  handleHostChange($event, index) {
    const hostId = $event.value;
    const hostInfo = this.hostList[index]?.find((host) => host.value === hostId);
    this.handlePatchApp('hostId', $event, index);
    if (hostInfo) {
      this.handlePatchApp('hostName', { value: hostInfo.label }, index);
    }
  }

  handleDbChange($event, index) {
    const dbId = $event.value;
    const dbInfo = this.rdsList[index]?.find((db) => db.value === dbId);
    this.handlePatchApp('dbId', $event, index);
    if (dbInfo) {
      this.handlePatchApp('dbName', { value: dbInfo.label }, index);
    }
  }

  handleCloseModal() {
    this.logVisible = false;
  }

  // 数据库和主机的数据格式化
  formatData(target) {
    if (!target?.length) return [];
    return target.map((item) => {
      return {
        label: item.sourceName,
        value: item.sid,
        disabled: item?.applications?.length > 0,
      };
    });
  }

  getDefaultCode(): string {
    const reflectHead = {
      '1': 'A',
      '4': 'B',
      '5': 'A',
      '6': 'A',
      '8': 'C',
      '9': 'G',
    };
    return `${reflectHead[this.params?.appType] || 'A'}${S4()}${S4()[0]}${S4()}`;
  }

  // 新增
  handleAddApp(type: string): void {
    if (type === 'empty') {
      this.visible = true;
      this.visibleChange.emit(true);
      this.applicationCode = this.getDefaultCode();
      this.appForm.get('code').setValue(this.applicationCode);
      this.handleInitLang();
    } else if (type === 'excel') {
      this.excelAppVisible = true;
    }
  }

  // 处理国际化
  handleInitLang(): void {
    this.appLang = {
      name: this.componentService.formatLang({}, 'name'),
      description: this.componentService.formatLang({}, 'description'),
    };
  }

  // 机制表单赋值
  handlePatchApp(key: any, data: any, index?: number): void {
    const listFormKey = ['envCode', 'hostId', 'dbId', 'envName', 'hostName', 'dbName'];
    if (!listFormKey.includes(key)) {
      this.appForm.patchValue({ [key]: data?.value });
    } else {
      const formGroup = this.serverResources.at(index) as FormGroup;
      formGroup.patchValue({
        [key]: data.value,
      });
    }
    if (data.needLang) {
      this.appLang = {
        ...(this.appLang || {}),
        [key]: data.lang?.value || data.lang,
      };
    }
  }

  // 选择图标
  onAppIconChange(data) {
    const { image, color } = data;
    this.appForm.patchValue({
      iconName: image,
      iconBgcolor: color,
    });
    this.value = { image: image, color: color };
  }

  /**
   * 从解决方案模板安装
   * @param param
   */
  private async installApp(param: any): Promise<void> {
    this.saveAppLoading = true;
    try {
      const result = await this.app
        .installExampleApp(Object.assign(param, { sourceCode: this.params.sourceCode }))
        .toPromise();
      if (result.code === 0) {
        this.message.success(this.translateService.instant('dj-保存成功！'));
        this.onMoadlCancel();
        this.afterCreated.emit(param);
      }
    } finally {
      this.saveAppLoading = false;
    }
  }

  /**
   * 保存高代码应用
   */
  handleHighAppSave(values, param) {
    // 针对模型驱动应用重组下入参
    const applicationInfo = _.omit(param, 'serverResources');
    const highParams = {
      application: {
        ...applicationInfo,
      },
      serverResources: values.serverResources || [],
    };
    if (this.opType === 'edit' && highParams.serverResources?.length > 0) {
      const cacheResource = this.cacheApplicationInfo?.serverResources ?? [];
      const cacheResourceMap = new Map(cacheResource.map((resource) => [resource.envCode, 0]));
      highParams.serverResources =
        highParams.serverResources.filter((resource) => !cacheResourceMap.has(resource.envCode)) || [];
    }

    if (highParams.serverResources?.length > 0) {
      this.logActiveEnvCode = highParams.serverResources[0].envCode;
    }
    const api = this.opType === 'edit' ? this.app.highCodeEdit : this.app.highCodeAdd;
    api.call(this.app, highParams).subscribe(
      (res) => {
        this.beforeSuccessCallback(res, {
          ...highParams,
          ...applicationInfo,
        });
      },
      (e) => {
        this.errorCallback(e, highParams);
      },
    );
  }

  afterAsyncFormValid() {
    if (this.appForm.valid) {
      const values = this.appForm.getRawValue();
      if (this.opType !== 'edit') {
        // 拼接CN、TW
        values.code = values.code + this.appCodeEnv;
      }
      // 除模型驱动解决方案的入参
      const param = {
        ...values,
        ...this.params,
        lang: this.appLang,
      };
      if (this.params?.sourceCode) {
        this.installApp(param);
        return;
      }

      this.saveAppLoading = true;
      // 如果是模型驱动解决方案 调用新的添加解决方案的接口
      if (this.isModelDriveApp) {
        // 针对模型驱动解决方案重组下入参
        const applicationInfo = _.omit(param, 'serverResources');
        const paramV3 = {
          application: {
            ...applicationInfo,
          },
          ...values,
          appType:param.appType,
          serverResources: values.serverResources || [], // 保证有个默认值
        };
        if (this.opType === 'edit' && paramV3.serverResources?.length > 0) {
          const cacheResource = this.cacheApplicationInfo?.serverResources ?? [];
          const cacheResourceMap = new Map(cacheResource.map((resource) => [resource.envCode, 0]));
          paramV3.serverResources = paramV3.serverResources.filter(
            (resource) => !cacheResourceMap.has(resource.envCode),
          );
        }
        if (
          paramV3.createType === 2 &&
          paramV3.serverResources?.length > 0 &&
          paramV3.serverResources.some((resource) => !resource.envCode || !resource.hostId || !resource.dbId)
        ) {
          this.message.error(this.translateService.instant('dj-请完善资源设置或删除'));
          this.saveAppLoading = false;
          return;
        }
        if (paramV3.serverResources?.length > 0) {
          this.logActiveEnvCode = paramV3.serverResources[0].envCode;
        }

        if (this.opType === 'edit') {
          paramV3.commonApp = this.cacheApplicationInfo.application.commonApp;
          paramV3.application.commonApp = this.cacheApplicationInfo.application.commonApp;
        }

        const api = this.opType === 'edit' ? this.app.updateAppV3 : this.app.addAppV3;
        api.call(this.app, paramV3).subscribe(
          (res) => {
            this.beforeSuccessCallback(res, paramV3);
          },
          (e) => {
            this.errorCallback(e, paramV3);
          },
        );
        return;
      }
      if (this.isHighApp) {
        this.handleHighAppSave(values, param);
        return;
      }
      (this.solutionCardService.isDynamicsApp(this.params?.appType)
        ? this.solutionCardService.addDynamicsApp(
            param,
            this.solutionCardService.getDynamicsAppCreateUrl(this.params?.appType),
          )
        : this.app.addApp(param)
      ).subscribe(
        (res) => {
          this.beforeSuccessCallback(res, param);
        },
        (e) => {
          this.errorCallback(e, param);
        },
      );
    }
  }

  // 保存解决方案
  handleSaveApp(): void {
    for (const i of Object.keys(this.appForm?.controls)) {
      this.appForm.controls[i].markAsDirty();
      this.appForm.controls[i].updateValueAndValidity();
    }
    if (this.isHighApp) {
      if (!this.transferAppNotNeedService) {
        const serverResources = this.appForm.get('serverResources') as FormArray;
        serverResources.controls.forEach((item: any, index) => {
          for (const i of Object.keys(item?.controls)) {
            item.controls[i].markAsDirty();
            item.controls[i].updateValueAndValidity();
          }
        });
      }

      if (this.appForm.valid) {
        this.afterAsyncFormValid();
      }
      return;
    }

    const createType = this.appForm.get('createType')?.value;
    // 1有异步校验
    if (createType === 1) {
      this.appForm.statusChanges.pipe(first()).subscribe((status) => {
        if (status === 'VALID') {
          this.afterAsyncFormValid();
        }
      });
    } else {
      this.afterAsyncFormValid();
    }
  }

  beforeSuccessCallback(res, param) {
    /**
     * 只在编辑时且未新创建服务时，直接结束
     */
    if (this.ignoreSuccessCallback && param?.serverResources?.length === 0) {
      this.message.success(this.translateService.instant('dj-保存成功！'));
      this.afterCreated.emit(param);
    } else {
      this.successCallback(res, param);
    }
  }

  successCallback(res, param) {
    this.appParams = param; // 保存下入参 以便跳转使用
    this.saveAppLoading = false;
    if (res.code === 0) {
      this.message.success(this.translateService.instant('dj-保存成功！'));

      // 模型驱动解决方案 有显示日志的逻辑
      // 新建解决方案后端才需要显示日志
      if (this.isModelDriveApp && param.createType === 2) {
        this.logVisible = true;
        this.newAppCode = res.data?.code || param?.application?.code;
        this.refreshAppList.emit();
        return;
      }
      if (this.isHighApp) {
        this.logVisible = true;
        this.newAppCode = res.data?.code || param?.application?.code;
        this.refreshAppList.emit();
        return;
      }

      // 非模型驱动应用直接跳转 或模型驱动的已有服务
      this.onMoadlCancel();
      this.afterCreated.emit(param);
    }
  }
  errorCallback(e, param) {
    this.saveAppLoading = false;
    if (e?.error?.errorCode === -1) {
      this.emptyVisible = false;
      this.repeatForm.reset();
      this.repeatAppData = param;
      this.repeatVisible = true;
    }
  }

  /**
   * 批量更新解决方案的解决方案后端
   * @param code
   */
  batchAddServiceCode(code) {
    const applicationServiceCodes = this.targetList?.reduce((pre, cur) => {
      pre.push({
        serviceCode: cur.serviceCode,
      });
      return pre;
    }, []);
    const param = {
      application: code,
      applicationServiceCodes,
    };
    this.app.batchAddServiceCode(param).subscribe((res) => {
      // this.message.success(this.translateService.instant('dj-解决方案后端保存成功!'));
    });
  }

  handleSaveRepeat(): void {
    for (const i of Object.keys(this.repeatForm?.controls)) {
      this.repeatForm.controls[i].markAsDirty();
      this.repeatForm.controls[i].updateValueAndValidity();
    }
    if (this.repeatForm.valid) {
      this.saveAppLoading = true;
      const { appToken } = this.repeatForm.getRawValue();
      const param = {
        ...this.repeatAppData,
        appToken,
      };
      this.app.addApp(param).subscribe(
        (res) => {
          this.saveAppLoading = false;
          if (res.code === 0) {
            this.message.success(this.translateService.instant('dj-保存成功！'));
            this.repeatVisible = false;
            this.afterCreated.emit(param);
            this.visibleChange.emit(false);
          }
        },
        () => {
          this.saveAppLoading = false;
        },
      );
    }
  }

  handleCancelRepeat() {
    this.repeatVisible = false;
    this.visibleChange.emit(false);
  }

  onMoadlCancel() {
    this.emptyVisible = false;
    this.visibleChange.emit(false);
  }

  onExcelModalCancel() {
    this.excelAppVisible = false;
  }

  handleModalClose() {
    this.appForm.reset();
  }

  // 打开解决方案后端开窗
  handleOpenModel() {
    this.serviceVisible = true;
  }

  // 关闭解决方案后端开窗
  handleCloseModel(e = []) {
    this.serviceVisible = false;
    this.getServiceCodeList();
  }
  handleCloseLogModal() {
    this.onMoadlCancel();
  }

  // resource最少添加一个
  minHightResourceLength(min: number) {
    return (formArray: AbstractControl): ValidationErrors | null => {
      const array = formArray as FormArray;
      return array.length >= min && array.value.filter((info) => info.envCode && info.hostId).length >= min
        ? null
        : { minLength: '请至少设置一组资源' };
    };
  }

  createNewResource(info?: IResourceInfo) {
    return this.fb.group({
      envCode: [info?.envCode ?? null],
      dbId: [info?.dbId ?? null],
      hostId: [info?.hostId ?? null],
      envName: [info?.envName ?? null],
      dbName: [info?.dbName ?? null],
      hostName: [info?.hostName ?? null],
    });
  }

  beforeCheckAddOrRemove() {
    if (this.failureService.length > 0) {
      this.message.error(
        this.translateService.instant('dj-创建失败，请先前往创建日志重新创建', {
          service: this.failureService.join('、'),
        }),
      );
      return false;
    }
    if (this.pendingService.length > 0) {
      this.message.error(
        this.translateService.instant('dj-创建中，请先前往创建日志重新创建', {
          service: this.pendingService.join('、'),
        }),
      );
      return false;
    }
    return true;
  }

  /**
   * 添加资源
   */
  addSource() {
    if (!this.beforeCheckAddOrRemove()) return;
    this.serverResources.push(this.createNewResource());
  }

  /**
   * 删除资源
   */
  deleteSource(index: number) {
    if (!this.beforeCheckAddOrRemove()) return;
    const envCode = this.serverResources.at(index).value?.envCode;
    if (this.editInitServerResourceCode.includes(envCode)) {
      return;
    }
    this.serverResources.removeAt(index);
    this.afterEnvChange(index, false, true, true);
  }

  createHightNewResource(info?: any) {
    return this.fb.group({
      envCode: [info?.envCode ?? null, [Validators.required]],
      hostId: [info?.hostId ?? null, [Validators.required]],
      envName: [info?.envName ?? null, [Validators.required]],
      hostName: [info?.hostName ?? null, [Validators.required]],
    });
  }

  /**
   * 初始化高代码应用的服务资源
   */
  handleInitHightCodeResources() {
    this.getAvailableServiceList({
      appType: AppTypes.HIGH_CODE,
    }).then(() => this.setEnvList());
    this.appForm.addControl(
      'serverResources',
      this.fb.array([this.createHightNewResource()], this.minHightResourceLength(1)),
    );
  }

  /**
   * 添加高代码资源
   */
  addHighSource() {
    if (!this.beforeCheckAddOrRemove()) return;
    this.serverResources.push(this.createHightNewResource());
  }

  /**
   * 删除高代码资源
   */
  deleteHighSource(index: number) {
    if (!this.beforeCheckAddOrRemove()) return;
    const envCode = this.serverResources.at(index).value?.envCode;
    if (this.editInitServerResourceCode.includes(envCode)) {
      return;
    }
    this.serverResources.removeAt(index);
    this.afterHighEnvChange(index, false, true, true);
  }

  dealHightEnvChange(index, envCode, callClear = true) {
    const target = this.availableServiceList.find((item) => item.envCode === envCode);
    this.hostList[index] = this.formatData(target?.hostList);
    this.afterHighEnvChange(index, !envCode, callClear);
  }

  // 环境改变 自动改变主机和数据库的下拉
  handleHightEnvChange(type, $event, index) {
    const envCode = $event.value;
    const envInfo = this.envList.find((env) => env.value === envCode);
    this.handlePatchHightForm(type, $event, index);
    if (envInfo) {
      this.handlePatchHightForm('envName', { value: envInfo.label }, index);
    }
    this.dealHightEnvChange(index, envCode);
  }

  handleHighHostChange($event, index) {
    const hostId = $event.value;
    const hostInfo = this.hostList[index]?.find((host) => host.value === hostId);
    this.handlePatchHightForm('hostId', $event, index);
    if (hostInfo) {
      this.handlePatchHightForm('hostName', { value: hostInfo.label }, index);
    }
  }

  afterHighEnvChange(index: number, clearOptions = false, callClear = true, deleteOp = false) {
    const serverResources = this.serverResources.value ?? [];
    const hightCodeResourcesMap = new Map(serverResources.map((item) => [item.envCode, 0]));
    /**
     * 处理禁用，每个表单的环境禁用是同步的，每个选项只能一个人选
     */
    this.envList.map((env) => (env.disabled = hightCodeResourcesMap.has(env.value)));
    /**
     * 环境清理，自动清理主机和数据库选择数据和下拉数据，因为这两者依赖环境
     */
    if (callClear) {
      this.clearHighHost(index, clearOptions, deleteOp);
    }
  }

  /**
   * 清理host和db数据&下拉选项
   * @param {number} index
   * @param {boolean} clearOptions
   */
  clearHighHost(index: number, clearOptions = false, deleteOp = false) {
    if (this.serverResources.length <= index) return;
    if (!deleteOp) {
      this.serverResources.at(index).patchValue({
        hostId: null,
      });
    }
    if (clearOptions) {
      this.hostList.splice(index, 1);
    }
  }

  // 机制表单赋值
  handlePatchHightForm(key: any, data: any, index?: number): void {
    const formGroup = this.serverResources.at(index) as FormGroup;
    formGroup.patchValue({
      [key]: data.value,
    });
  }

  goToCloudService() {
    const language = this.languageService?.currentLanguage || 'zh_CN';
    const token = this.userService.getUser('fxToken');
    window.open(
      `${this.developerPortalUrl}/sso-login?userToken=${token}&dwLang=${language}&routerLink=/search`,
      '_blank',
    );
  }
}
