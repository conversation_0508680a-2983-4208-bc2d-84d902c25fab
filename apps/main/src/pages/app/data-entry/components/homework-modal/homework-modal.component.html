<!--作业-->
<ad-modal
  nzClassName="homework-modal-wrapper"
  [nzTitle]="'dj-新增作业' | translate"
  [nzContent]="modalContent"
  [nzFooter]="null"
  [nzWidth]="'780px'"
  [(nzVisible)]="infoModalVisible"
  [nzMaskClosable]="false"
  [nzClosable]="true"
  (nzOnCancel)="handleCancel()"
>
  <ng-template #modalContent>
    <div class="homework-wrapper">
      <div class="left-wrapper">
        <div class="worktype-list">
          <div class="worktype-item" *ngFor="let item of homeworkTypeLists" (click)="handleWorkTypeClick(item)">
            <img [class.active]="dataForm.get('addPageDesignType')?.value === item.key" [src]="item.imageUrl" />
            <span>
              {{ item.title | translate }}
            </span>
            <span *ngIf="item.subTitle" class="subtitle">
              {{ item.subTitle | translate }}
            </span>
          </div>
        </div>
      </div>
      <form nz-form [formGroup]="dataForm">
        <div class="right-wrapper">
          <div class="form-content">
            <img *ngIf="!!demoUrl" class="demo-img" [src]="demoUrl" />
            <!-- 作业名称 -->
            <nz-form-item class="form-item">
              <nz-form-label [nzRequired]="true">{{ 'dj-作业名称' | translate }}</nz-form-label>
              <nz-form-control [nzErrorTip]="'dj-请输入' | translate">
                <div
                  nz-tooltip
                  [nzTooltipTitle]="'dj-作业名称tips' | translate"
                  nzTooltipTrigger="click"
                  nzTooltipPlacement="bottom"
                >
                  <app-component-input
                    formControlName="name"
                    [attr]="{
                      code: 'name',
                      needLang: true,
                      lang: { value: this.formLang?.name },
                      placeholder: 'dj-请输入' | translate,
                      maxlength: 50,
                      required: true
                    }"
                    [value]="this.formLang?.name?.[('dj-LANG' | translate)]"
                    ngDefaultControl
                    (callBack)="handlePatchLang('name', $event)"
                  >
                  </app-component-input>
                </div>
              </nz-form-control>
            </nz-form-item>
            <!-- 关联模型 -->
            <nz-form-item class="form-item" *ngIf="dataForm.get('workType')?.value === GenerateFromType.MODEL">
              <nz-form-label [nzRequired]="true">
                <span class="label-with-tip">{{ 'dj-关联模型' | translate }}</span>
                <span
                  *ngIf="
                    [GenerateBusinessType.CATEGORY_TABLE, GenerateBusinessType.CATEGORY_TABLE_EDITABLE].includes(
                      dataForm.get('addPageDesignType')?.value
                    )
                  "
                  adIcon
                  iconfont="iconqipaotishi-wenhao"
                  class="question"
                  nz-tooltip
                  [nzTooltipTitle]="'dj-关联模型tips' | translate"
                ></span>
              </nz-form-label>
              <nz-form-control [nzErrorTip]="'dj-请选择' | translate">
                <ad-select formControlName="modelId" (ngModelChange)="handleAssociationModelChange($event)">
                  <ad-option
                    *ngFor="let item of associationModels; let i = index"
                    [nzValue]="item.code"
                    [nzLabel]="item.code + ' (' + item.lang?.name?.[('dj-LANG' | translate)] + ')'"
                  >
                  </ad-option>
                </ad-select>
              </nz-form-control>
            </nz-form-item>
            <!-- 关联API -->
            <!-- <nz-form-item class="form-item" *ngIf="dataForm.get('workType')?.value === GenerateFromType.API"> -->
              <!-- <nz-form-label [nzRequired]="true">{{ 'dj-关联API' | translate }}</nz-form-label>
              <nz-form-control [nzErrorTip]="'dj-请选择' | translate"> -->
                <!-- <nz-input-group [required]="true" [nzSuffix]="suffixIconAPI">
                  <input readonly nz-input formControlName="actionId" [placeholder]="'dj-请选择' | translate" />
                </nz-input-group>
                <ng-template #suffixIconAPI>
                  <i
                    adIcon
                    iconfont="iconkaichuang"
                    aria-hidden="true"
                    class="open-modal-icon window-icon iconfont"
                    (click)="handleOpenAction()"
                  ></i>
                </ng-template> -->
                <!--action开窗组件-->
                <!-- <app-action-modal
                  *ngIf="isActionModalShow"
                  [transferModal]="isActionModalShow"
                  [transferData]="actionData"
                  labelType="EspAction"
                  (callBack)="handleConfirmAction($event)"
                  (closeModal)="isActionModalShow = false"
                >
                </app-action-modal> -->
              <!-- </nz-form-control> -->
            <!-- </nz-form-item> -->
            <!-- 分类导航模型 -->
            <nz-form-item
              class="form-item"
              *ngIf="
                [GenerateBusinessType.CATEGORY_TABLE, GenerateBusinessType.CATEGORY_TABLE_EDITABLE].includes(
                  dataForm.get('addPageDesignType')?.value
                )
              "
            >
              <nz-form-label [nzRequired]="true">
                <span class="label-with-tip">{{ 'dj-分类导航模型' | translate }}</span>
                <span
                  adIcon
                  iconfont="iconqipaotishi-wenhao"
                  class="question"
                  nz-tooltip
                  [nzTooltipTitle]="'dj-分类导航模型tips' | translate"
                ></span>
              </nz-form-label>
              <nz-form-control [nzErrorTip]="'dj-请选择' | translate">
                <ad-select
                  formControlName="navigateModelCode"
                  [nzPlaceHolder]="'dj-请选择' | translate"
                  (ngModelChange)="handleNavigationModelChange($event)"
                >
                  <ad-option
                    *ngFor="let item of navigationModels; let i = index"
                    [nzValue]="item.code"
                    [nzLabel]="item.lang?.name?.[('dj-LANG' | translate)] ?? item.name"
                  >
                  </ad-option>
                </ad-select>
              </nz-form-control>
            </nz-form-item>
            <!-- 查询方案 -->
            <nz-form-item
              class="form-item"
              *ngIf="
                [GenerateBusinessType.QUERY_PLAN_TABLE, GenerateBusinessType.QUERY_PLAN_VERTICAL_TABLE].includes(
                  dataForm.get('addPageDesignType').value
                )
              "
            >
              <nz-form-label [nzRequired]="true">{{ 'dj-查询方案' | translate }}</nz-form-label>
              <nz-form-control [nzErrorTip]="'dj-请选择' | translate">
                <div class="dataview-form-wrapper">
                  <ad-select
                    formControlName="dataViewCodes"
                    [nzMode]="'tags'"
                    [nzPlaceHolder]="'dj-请选择' | translate"
                  >
                    <ad-option
                      *ngFor="let item of queryPlanLists; let i = index"
                      [nzValue]="item.code"
                      [nzLabel]="item.lang?.name?.[('dj-LANG' | translate)] ?? item.name"
                    >
                    </ad-option>
                  </ad-select>
                  <button
                    *ngIf="!queryPlanLists || queryPlanLists.length === 0"
                    ad-button
                    [nzLoading]="generateDataViewLoading"
                    adType="link"
                    (click)="handleFastGenerateDataView()"
                  >
                    {{ 'dj-快速生成查询方案' | translate }}
                  </button>
                </div>
              </nz-form-control>
            </nz-form-item>
            <!-- <nz-form-item class="form-item-custom">
              <label nz-checkbox formControlName="dependOnGroundEnd">
                {{ 'dj-依赖地端' | translate }}
              </label>
              <label nz-checkbox formControlName="authorityPrefix">
                {{ 'dj-权限控制' | translate }}
              </label>
            </nz-form-item> -->
            <nz-form-item
              class="form-item"
              *ngIf="dataForm.get('workType')?.value === GenerateFromType.API && dataForm.get('authorityPrefix')?.value"
            >
              <nz-form-label>
                <span class="label-with-tip">{{ 'dj-关联模型' | translate }}</span>
                <span
                  adIcon
                  iconfont="iconqipaotishi-wenhao"
                  class="question"
                  nz-tooltip
                  [nzTooltipTitle]="'dj-模型驱动code描述' | translate"
                ></span>
              </nz-form-label>
              <nz-form-control [nzErrorTip]="'dj-validateModelDrivenCode' | translate">
                <input nz-input formControlName="simpleModelCode" [placeholder]="'dj-关联模型' | translate" />
              </nz-form-control>
            </nz-form-item>
          </div>
          <div class="modal-footer">
            <div class="operate-wrapper">
              <nz-form-item *ngIf="dataForm.get('workType')?.value === GenerateFromType.MODEL">
                <nz-form-control>
                  <ad-select
                    formControlName="operateType"
                    [nzBorderless]="true"
                    [nzAllowClear]="false"
                    class="query-select"
                    (ngModelChange)="handleOperateTypeChange($event)"
                  >
                    <ad-option
                      [nzValue]="GenerateBusinessOperateType.APPEND"
                      [nzLabel]="'dj-生成新作业' | translate"
                    ></ad-option>
                    <ad-option
                      [nzValue]="GenerateBusinessOperateType.COVERAGE"
                      [nzLabel]="'dj-覆盖历史作业' | translate"
                    ></ad-option>
                  </ad-select>
                </nz-form-control>
              </nz-form-item>
              <nz-form-item
                *ngIf="
                  dataForm.get('workType')?.value !== GenerateFromType.API &&
                  dataForm.get('operateType')?.value === GenerateBusinessOperateType.COVERAGE
                "
              >
                <nz-form-control [nzErrorTip]="'dj-请选择' | translate">
                  <ad-select
                    formControlName="coveragePageCode"
                    [nzBorderless]="true"
                    [nzAllowClear]="false"
                    [nzPlaceHolder]="'dj-请选择' | translate"
                    class="coverage-select"
                  >
                    <ad-option
                      *ngFor="let item of coverageBusinessLists; let i = index"
                      [nzValue]="item.code"
                      [nzLabel]="item.lang?.name?.[('dj-LANG' | translate)] ?? item.name"
                    >
                    </ad-option>
                  </ad-select>
                </nz-form-control>
              </nz-form-item>
            </div>
            <div class="btn-wrapper">
              <button ad-button adType="default" (click)="handleCancel()">
                {{ 'dj-取消' | translate }}
              </button>
              <ng-container *operateAuth="{ prefix: 'update' }">
                <button ad-button adType="primary" [nzLoading]="confirmLoading" (click)="handleConfirm()">
                  {{ 'dj-确定' | translate }}
                </button>
              </ng-container>
            </div>
          </div>
        </div>
      </form>
    </div>
  </ng-template>
</ad-modal>
