<!--// TODO新增作业-->
<form nz-form [formGroup]="dataForm" [nzNoColon]="true" class="form-info login-form">
  <!-- 租户 -->
  <nz-form-item *ngIf="currentBasic?.tenantId && currentBasic?.tenantId !== 'SYSTEM'">
    <nz-form-control nzFlex="400px" [nzErrorTip]="codeErrorTpl" class="data-form-item">
      <div nz-tooltip [nzTooltipTitle]="'dj-租户' | translate" nzTooltipTrigger="click" nzTooltipPlacement="bottom">
        <nz-input-group [adInnerLabel]="'dj-租户' | translate" [required]="true">
          <input
            nz-input
            formControlName="tenantId"
            type="text"
            (keyup)="inputChange($event, 'tenantId')"
            [placeholder]="'dj-请输入' | translate"
          />
        </nz-input-group>
      </div>
    </nz-form-control>
    <ng-template #codeErrorTpl let-control>
      <ng-container *ngIf="control.hasError('required')">{{ 'dj-租户必填' | translate }}</ng-container>
    </ng-template>
  </nz-form-item>
  <!-- 关联模型 -->
  <nz-form-item *ngIf="!resourePerspective">
    <nz-form-control nzFlex="400px" [nzErrorTip]="simpleErrorTpl" class="data-form-item" style="max-width: unset">
      <div class="data-form-item-layout">
        <nz-input-group
          style="width: 400px"
          [nzSuffix]="suffixCode"
          [adInnerLabel]="'dj-关联模型' | translate"
          [required]="true"
        >
          <input
            readonly
            nz-input
            formControlName="simpleModelCodeAndServiceCodeLabel"
            [placeholder]="'dj-请选择' | translate"
          />
        </nz-input-group>
        <ng-template #suffixCode>
          <i
            adIcon
            iconfont="iconkaichuang"
            aria-hidden="true"
            [ngClass]="{ 'window-icon': true, iconfont: true, 'not-iconfont-pointer': type === 'edit' }"
            (click)="handleOpenSimpleModelListModal()"
          >
          </i>
        </ng-template>
        <span
          *ngIf="dataForm.get('supportNavigateFlag')?.value"
          nz-tooltip
          [nzTooltipTitle]="'dj-关联模型tips' | translate"
          nzTooltipTrigger="hover"
          class="icon-tips"
        >
          <i adIcon [iconfont]="'iconexplain'"></i>
        </span>
        <button *ngIf="showGotoDesignBtn" ad-button adType="text" (click)="handleGotoDesign($event, 'simple')">
          {{ 'dj-前往设计' | translate }}
        </button>
      </div>
    </nz-form-control>
    <ng-template #simpleErrorTpl let-control>
      <ng-container *ngIf="control.hasError('required')">{{ 'dj-请选择' | translate }}</ng-container>
      <ng-container *ngIf="control.hasError('duplicated')">{{
        'dj-simpleModelCodeDuplicated' | translate
      }}</ng-container>
    </ng-template>
  </nz-form-item>
  <!-- 关联查询视图 -->
  <nz-form-item *ngIf="resourePerspective">
    <nz-form-control nzFlex="400px" [nzErrorTip]="dateViewErrorTpl" class="data-form-item" style="max-width: unset">
      <div class="data-form-item-layout">
        <ad-select
          nzShowSearch
          style="width: 400px"
          formControlName="simpleModelCode"
          isInsideLabel
          [required]="true"
          [label]="'dj-关联查询方案' | translate"
          [nzDisabled]="type === 'edit'"
          (ngModelChange)="handleQueryPlainCodeChange($event)"
        >
          <ng-container *ngFor="let item of queryPlainList">
            <ad-option
              [nzValue]="item.code"
              [nzLabel]="(item.lang?.name[('dj-LANG' | translate)] ?? item.name) + '（' + item.code + '）'"
            ></ad-option>
          </ng-container>
        </ad-select>
      </div>
    </nz-form-control>
    <ng-template #dateViewErrorTpl let-control>
      <ng-container *ngIf="control.hasError('required')">{{ 'dj-请选择' | translate }}</ng-container>
      <ng-container *ngIf="control.hasError('duplicated')">{{
        'dj-simpleModelCodeDuplicated' | translate
      }}</ng-container>
    </ng-template>
  </nz-form-item>
  <!--编码  作业代号去除 -->
  <nz-form-item *ngIf="type !== 'create'">
    <nz-form-control nzFlex="400px" [nzErrorTip]="codeErrorTpl" class="data-form-item">
      <div
        nz-tooltip
        [nzTooltipTitle]="'dj-作业代号tips' | translate"
        nzTooltipTrigger="click"
        nzTooltipPlacement="bottom"
      >
        <!--这里特殊处理 已选模型 先屏蔽掉label,已避免palace的label把模型code给覆盖显示-->
        <nz-input-group
          [adInnerLabel]="'dj-作业代号' | translate"
          [required]="true"
          [nzPrefix]="
            type === 'create' && (prefix?.length > 0 || dataForm.get('simpleModelCode').value?.length > 0)
              ? prefixTmpl
              : null
          "
        >
          <input
            nz-input
            formControlName="code"
            type="text"
            [maxlength]="50"
            (keyup)="inputChange($event, 'code')"
            [placeholder]="'this_is_a_sample'"
          />
        </nz-input-group>
        <ng-template #prefixTmpl>
          <ng-container *ngIf="prefix?.length > 0">
            <span class="prefix_code">{{ prefix + '_' }}</span>
          </ng-container>
          <ng-container *ngIf="dataForm.get('simpleModelCode').value?.length > 0">
            <span
              nz-tooltip
              [nzTooltipTitle]="getTooltip(prefix, dataForm.get('simpleModelCode').value)"
              class="prefix_code"
            >
              {{ dataForm.get('simpleModelCode').value + '__' }}
            </span>
          </ng-container>
        </ng-template>
      </div>
    </nz-form-control>
    <ng-template #codeErrorTpl let-control>
      <ng-container *ngIf="control.hasError('required')">{{ 'dj-请输入作业代号' | translate }}</ng-container>
      <ng-container *ngIf="control.hasError('workDesignCode')">{{
        'dj-请输入正确的作业代号' | translate
      }}</ng-container>
      <ng-container *ngIf="control.hasError('codeRepeat')">{{ 'dj-作业代号重复' | translate }}</ng-container>
    </ng-template>
  </nz-form-item>
  <!-- 作业名称 -->
  <nz-form-item>
    <nz-form-control nzFlex="400px" [nzErrorTip]="nameErrorTpl" class="data-form-item">
      <div
        nz-tooltip
        [nzTooltipTitle]="'dj-作业名称tips' | translate"
        nzTooltipTrigger="click"
        nzTooltipPlacement="bottom"
      >
        <app-component-input
          class="lang-input"
          formControlName="name"
          [attr]="{
            code: 'name',
            needLang: true,
            lang: { value: formLang?.name },
            placeholder: '这是范例',
            maxlength: 50,
            label: 'dj-作业名称',
            innerLabel: true,
            required: true
          }"
          [value]="formLang?.name?.[('dj-LANG' | translate)] || _formData?.name"
          ngDefaultControl
          (callBack)="handlePatchLang('name', $event)"
        >
        </app-component-input>
      </div>
      <ng-template #nameErrorTpl let-control>
        <ng-container *ngIf="control.hasError('required')">{{ 'dj-请输入作业名称' | translate }}</ng-container>
      </ng-template>
    </nz-form-control>
  </nz-form-item>
  <!-- 支持分类导航 流程解决方案的会有业务code,在此场景下不显示-->
  <nz-form-item *ngIf="businessCode === ''">
    <nz-form-control nzFlex="410px" class="data-form-item">
      <label
        nz-checkbox
        formControlName="supportNavigateFlag"
        [nzDisabled]="type === 'edit'"
        (ngModelChange)="hanldeNavigationChange($event)"
      >
        {{ 'dj-支持分类导航' | translate }}
      </label>
    </nz-form-control>
  </nz-form-item>
  <!-- 分类导航模型 -->
  <nz-form-item *ngIf="dataForm.get('supportNavigateFlag')?.value">
    <nz-form-control nzFlex="400px" [nzErrorTip]="navigateErrorTpl" class="data-form-item" style="max-width: unset">
      <div class="data-form-item-layout">
        <ad-select
          nzShowSearch
          style="width: 400px"
          formControlName="navigateModelId"
          isInsideLabel
          [required]="true"
          [label]="'dj-分类导航模型' | translate"
          [nzDisabled]="type === 'edit'"
          (ngModelChange)="handleNavigateModelCodeChange($event)"
        >
          <ng-container *ngFor="let item of navigateModelList">
            <ad-option
              [nzValue]="item.id"
              [nzLabel]="(item.lang?.name[('dj-LANG' | translate)] ?? item.name) + '（' + item.code + '）'"
            ></ad-option>
          </ng-container>
        </ad-select>
        <span
          nz-tooltip
          [nzTooltipTitle]="'dj-分类导航模型tips' | translate"
          nzTooltipTrigger="hover"
          class="icon-tips"
        >
          <i adIcon [iconfont]="'iconexplain'"></i>
        </span>
        <button *ngIf="showGotoDesignBtn" ad-button adType="text" (click)="handleGotoDesign($event, 'navigate')">
          {{ 'dj-前往设计' | translate }}
        </button>
      </div>
    </nz-form-control>
    <ng-template #navigateErrorTpl let-control>
      <ng-container *ngIf="control.hasError('required')">{{ 'dj-请选择' | translate }}</ng-container>
      <ng-container *ngIf="control.hasError('duplicated')">{{
        'dj-navigateModelCodeDuplicated' | translate
      }}</ng-container>
    </ng-template>
  </nz-form-item>

  <nz-form-item>
    <div class="data-form-item-layout">
      <label nz-checkbox formControlName="dependOnGroundEnd">
        {{ 'dj-依赖地端' | translate }}
      </label>
      <label nz-checkbox formControlName="authorityPrefix">
        {{ 'dj-权限控制' | translate }}
      </label>
      <!-- <ng-container formGroupName="extendFields">
        <label nz-checkbox formControlName="closeNeedConfirm">
          {{ 'dj-是否卡控' | translate }}
        </label>
      </ng-container> -->
    </div>
  </nz-form-item>

  <!-- <nz-form-item *ngIf="isCommonApp">
    <nz-form-control nzFlex="430px" class="data-form-item" style="max-width: unset">
      <div class="data-form-item-layout">
        <app-custom-package-data-collection
          style="width: 100%"
          #customPackageDataCollectionComponent
          [packages]="packages"
          formType="create"
          addTip="{{ 'dj-新增定制包' | translate }}"
        >
        </app-custom-package-data-collection>
      </div>
    </nz-form-control>
  </nz-form-item> -->
</form>

<app-choose-simple-model-list-modal
  *ngIf="workDesignDetailFormService.chooseSimpleModelVisible"
  [visible]="workDesignDetailFormService.chooseSimpleModelVisible"
  [simpleModelList]="simpleModelList"
  [simpleModelCode]="dataForm.get('simpleModelCodeAndServiceCode')?.value"
  [navigateModelId]="dataForm.get('navigateModelId')?.value"
  (cancel)="handleChooseSimpleModelCancel()"
  (confirm)="handleSimpleModelCodeChange($event)"
></app-choose-simple-model-list-modal>
