export interface ILang {
  en_US: string;
  zh_CN: string;
  zh_TW: string;
}

export interface ICustomSetting {
  application: string;
  adpTenantId?: string;
  adpCode: string;
  name: string;
  description: string;
  lang: {
    description: ILang;
  };
  path: string;
  chunkName: string;
  isMF: boolean;
  remoteName: string;
  exposedModule: string;
  relateActivityNames: string[];
  customControl: any[];
  shareTenantIdList: ICustomShare[] & { nonExistent?: boolean };
}

export interface ICustomShare {
  shareTenantId: string;
  editBy: string;
  editDate: string;
  // status: 'effect' | 'invalid';
}

export interface ILang {
  zh_CN: string;
  zh_TW: string;
  en_US: string;
}

export interface ICustomComponent {
  type: string;
  title: string;
  icon: string;
  lang: {
    title: ILang;
  };
  isPlaceholderActive: boolean;
  references: any[];
  properties: IPropertyModel[];
  extendInfo: any;
  hooks: IEventModel[];
}

/**
 * Event Model
 */
export interface IEventModel {
  id: string;
  name: string;
  description: string;
  params: string[];
  lang: {
    description: ILang;
  };
  defaultValue: string;
}

export interface IPropertyModel {
  id?: string;
  /** 是否是系统的 */
  system?: boolean;
  /** 数据节点 */
  dataKey: string;
  /** 标题 / 组件名称 */
  title: string;
  /** 组件描述 */
  description: string;
  /** 是否必填 */
  required: boolean;
  /** 是否禁用 */
  disabled: boolean;
  /** 默认值 */
  defaultValue: string | string[];
  /** 组件类型 */
  componentType: string;
  /** 选项值 */
  options: {
    label: string;
    value: string;
    lang?: ILang;
  }[];
  lang: {
    title: ILang;
    description: ILang;
    defaultValue?: ILang;
  };
}

export interface CustomQueryParams {
  appCode: string;
  targetCode: string;
  uiKey: string;
  branch: string;
  dataViewQueryCode?: string;
}
