import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { SystemConfigService } from 'common/service/system-config.service';
import { AppService } from 'pages/apps/app.service';
import { Observable, Subject } from 'rxjs';
import { ReportStoreService } from './store.service';
import { ABIReportEnum, ReportCategoryEnum, ReportTypeEnum } from '../types/typings';
import { cloneDeep } from 'lodash';
import { AdUserService } from 'pages/login/service/user.service';
import { LocaleService } from 'common/service/locale.service';
import { tbbPermission } from 'common/utils/auth';
import { PageModel } from 'components/page-design/config/types';
import { to } from 'common/utils/core.utils';
import { AuthOperate } from 'common/types/auth.types';
import { AuthService } from 'common/service/auth.service';
import { getReportType } from 'components/bussiness-components/print-template/report/tools/tools';
import { TranslateService } from '@ngx-translate/core';
import { IndividualService } from 'pages/individual/individual.service';

@Injectable()
export class ReportService {
  // 报表列表原数据，(基于该数据，state.reportList用于渲染)
  reportListData: any[] = [];
  adesignerUrl: string;
  abiReportUrl: any; // abi报表设计器url
  tbbReportUrl: any; // tbb报表设计器url
  reportDesignData: any; // 报表设计数据
  isv: string = 'lowCode';
  workData: any; // 界面设计数据

  // 用于添加报表
  addInfo = {
    // 凭证类
    'abi-prpttpl': {
      category: ReportCategoryEnum.ABI_STATEMENT,
      categoryAbi: ABIReportEnum.PRPTTPL,
      title: 'dj-凭证类报表',
    },
    // 数据查询报表
    'abi-ebibase': {
      category: ReportCategoryEnum.ABI_STATEMENT,
      categoryAbi: ABIReportEnum.EBIBASE,
      title: 'dj-数据查询报表',
    },
    // 数据分析
    'tbb-statement': { category: ReportCategoryEnum.STATEMENT, title: 'dj-数据分析' },
    // 定制报表
    'custom-statement': {
      category: ReportCategoryEnum.CUSTOM_STATEMENT_DETAIL,
      title: 'dj-定制报表',
    },
  };

  get state() {
    return this.store.state;
  }

  // 是否公共解决方案
  get isCommonApp() {
    return this.appService.isCommonApp;
  }

  constructor(
    protected http: HttpClient,
    protected configService: SystemConfigService,
    public appService: AppService,
    private store: ReportStoreService,
    protected userService: AdUserService,
    private languageService: LocaleService,
    private newAuthService: AuthService,
    private translate: TranslateService,
    private individualService: IndividualService,
  ) {
    this.configService.get('adesignerUrl').subscribe((url) => {
      this.adesignerUrl = url;
    });
    this.configService.get('abiReportUrl').subscribe((abiReportUrl) => {
      this.abiReportUrl = abiReportUrl;
    });
    this.configService.get('tbbReportUrl').subscribe((tbbReportUrl) => {
      this.tbbReportUrl = tbbReportUrl;
    });
  }

  createReportListMap(reportList) {
    const listTypeMap = {
      'abi-prpttpl': {
        type: 'abi-prpttpl',
        label: this.translate.instant('dj-凭证打印报表'),
        lists: [],
      },
      'abi-ebibase': {
        type: 'abi-ebibase',
        label: this.translate.instant('dj-数据查询明细表'),
        lists: [],
      },
      'tbb-statement': {
        type: 'tbb-statement',
        label: this.translate.instant('dj-图表分析报表'),
        lists: [],
      },
      'custom-statement': {
        type: 'custom-statement',
        label: this.translate.instant('dj-定制报表'),
        lists: [],
      },
      'print-template': {
        type: 'print-template',
        label: this.translate.instant('dj-通用打印模板'),
        lists: [],
      },
    };
    const lists = reportList ?? [];
    lists?.forEach((info) => {
      const type = getReportType(info);
      listTypeMap[type].lists.push(info);
    });
    return listTypeMap;
  }

  /**
   * 从接口获取报表列表数据
   * @param isDefaultFirst 是否默认选中第一个
   * @param callBack
   */
  async getReportList(isDefaultFirst = true, callBack?) {
    this.store.setState({ loading: true });
    const [err, res] = await to(this.loadAppBasicReport().toPromise());
    const currentCode = this.store.state?.selectedRow?.code;
    if (res?.code === 0) {
      this.reportListData = res.data;
      this.store.setState((state) => {
        state.reportList = res.data;
        let data;
        if (isDefaultFirst) {
          const listTypeMap = this.createReportListMap(res?.data ?? []);
          const existKey = Object.keys(listTypeMap)?.find((key) => listTypeMap[key]?.lists?.length > 0);
          if (existKey) {
            data = listTypeMap[existKey].lists[0];
            this.store.setState((state) => {
              state.selectedRow = data;
            });
          }
        } else if (!!currentCode) {
          data = res.data?.find((s) => s.code === currentCode) || null;
        }
        state.selectedRow = data;
        state.editReportType = getReportType(data);
        state.loading = false;
      });
      callBack?.();
    }
  }

  /**
   * 根据搜索内容查列表
   * @param searchValue
   */
  filterReport(searchValue) {
    const list = this.reportListData.filter(
      ({ name, code }) => name.includes(searchValue) || code.includes(searchValue),
    );

    this.store.setState({
      reportList: list,
    });
  }

  /**
   * 表单数据与业务数据互转
   * @param type
   * @param data
   */
  transformInOut(type: 'in' | 'out', data) {
    const newData = cloneDeep(data);

    if (type === 'in') {
      if ('authorityPrefix' in data) {
        newData.authorityPrefix = !!data.authorityPrefix;
      }

      if ('pageFlag' in data) {
        const transform = { N: false, Y: true };
        newData.pageFlag = transform[data.pageFlag];
      }

      return newData;
    }

    if ('authorityPrefix' in data) {
      const authorityPrefix = data.authorityPrefix
        ? this.isCommonApp
          ? `${
              this.individualService?.individualCaseApp
                ? this.individualService.sourceApplicationCode
                : this.appService?.selectedApp?.code
            }:${data.code}`
          : `${
              this.individualService?.individualCaseApp
                ? this.individualService.sourceApplicationCode
                : this.appService?.selectedApp?.code
            }:report`
        : '';
      newData.authorityPrefix = authorityPrefix;
    }

    if ('pageFlag' in data) {
      newData.pageFlag = data.pageFlag ? 'Y' : 'N';
    }
    return newData;
  }

  // 串接报表：abi（億信）、tbb
  handleDesign(abiData?, reportData?): void {
    const currentReport = cloneDeep(this.state.selectedRow);
    const reportDataTemp = cloneDeep(reportData ? reportData : currentReport);

    const language = this.languageService?.currentLanguage || 'zh_CN';
    const { code, lang, name, resCode, pageFlag } = currentReport;
    const appCode = this.appService?.selectedApp?.code;
    // 避免名称多语系无数据报错修改
    // const appName = this.appService?.selectedApp?.lang?.name ? this.appService?.selectedApp?.lang?.name[abiData?.rptlocale || language] : this.appService?.selectedApp?.name;
    const title = lang?.name;
    // const token = 'd13be251-93e0-4fcd-9c4f-ece8a9e3d3d4';
    const token = this.userService.getUser('fxToken');

    const allow = this.newAuthService.getAuth(AuthOperate.UPDATE);

    const baseUrl = abiData
      ? `${
          this.abiReportUrl
        }/abi/ebipro/editrpttpl.do?action=edit&@token=${token}&@program_code=${code}&@app_id=${appCode}&@routerKey=${this.userService.getUser(
          'tenantId',
        )}&pageFlag=${pageFlag}&datasourceRefreshFlag=${reportData?.needFlag || false}`
      : `${this.tbbReportUrl}/index.html?#/sso-login?dwLang=${language}&appCode=${appCode}&dashboardId=${resCode}&designMode=${allow}&isv=${this.isv}`;

    if (reportDataTemp._pageModel === PageModel.NEW) {
      reportDataTemp._pageModel = 'dsl';
    }

    this.reportDesignData = {
      token,
      baseUrl,
      title,
      code,
      appCode,
      data: {
        ...reportDataTemp,
        resid: abiData?.resid,
        isvcode: abiData?.isvcode,
      },
    };
    this.store.setState({
      reportDesignVisible: true,
    });
  }

  handleMenuPageDesign(data: any): void {
    if (data.category === ReportCategoryEnum.ABI_STATEMENT) {
      this.handleABIDesign(data);
      return;
    }
    if (data.category === ReportCategoryEnum.STATEMENT) {
      this.handleDesign();
      let { data } = this.reportDesignData;
      '_pageModel' in data || Object.assign(data, { _pageModel: 'pageView' });
      return;
    }
    if (
      data.category === ReportCategoryEnum.CUSTOM_STATEMENT_DETAIL &&
      [PageModel.DSL, PageModel.NEW].includes(data._pageModel)
    ) {
      this.handleCustomDesign(data);
      return;
    }

    this.handlePageDesign(data);
  }

  handleABIDesign(reportData?): void {
    const language = this.languageService?.currentLanguage || 'zh_CN';
    const param = `application=${this.appService?.selectedApp?.code}&code=${this.state.selectedRow.code}&locale=${language}`;
    this.getReportResid(param).subscribe((res) => {
      if (res?.code === 0) {
        this.handleDesign({ resid: res.data?.resid, isvcode: res.data?.isvCode }, reportData);
      }
    });
  }

  // 半定制的界面设计(DSL模式下)
  handleCustomDesign(reportData) {
    const { code, lang } = reportData;
    const appCode = this.appService?.selectedApp?.code;
    const title = lang?.name;
    this.reportDesignData = {
      title,
      code,
      appCode,
      data: {
        ...reportData,
      },
    };

    this.store.setState({
      reportDesignVisible: true,
    });
  }

  /**
   * 点击界面设计回调逻辑
   * @param data
   * @returns
   */
  handlePage(data) {
    const { _pageModel } = data;
    if (_pageModel && _pageModel === PageModel.NEW) {
      this.handleMenuPageDesign(this.state.selectedRow);
      return;
    }
    this.handleMenuPageDesign('_pageModel' in data ? data : { ...data, _pageModel: 'pageView' });
  }

  // 界面设计
  handlePageDesign(data: any): void {
    const app = this.appService?.selectedApp;
    const title = {
      en_US: `${app?.lang?.name?.['en_US'] ?? app?.name}-${data.lang?.name?.['en_US'] ?? data.name}`,
      zh_CN: `${app?.lang?.name?.['zh_CN'] ?? app?.name}-${data.lang?.name?.['zh_CN'] ?? data.name}`,
      zh_TW: `${app?.lang?.name?.['zh_TW'] ?? app?.name}-${data.lang?.name?.['zh_TW'] ?? data.name}`,
    };
    this.workData = {
      title, // 解决方案名称 + 作业名
      code: data?.code,
      category: 'Statement',
      authorityPrefix: data.authorityPrefix || '',
      dependOnGroundEnd: !!data?.dependOnGroundEnd,
    };
    this.store.setState({
      workVisible: true,
    });
  }

  /**
   * 界面设计关闭
   */
  designClose() {
    this.workData = null;
    this.store.setState({
      workVisible: false,
    });
  }

  reportDesignClose() {
    this.reportDesignData = null;
    this.store.setState({
      reportDesignVisible: false,
    });
  }

  /**
   * 查询当前解决方案下的基础资料与报表数据
   * @returns
   */
  loadAppBasicReport(): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/activityConfigs/getActivityListByPatternAndApplication`;
    return this.http.get(url, { params: { pattern: 'STATEMENT', application: this.appService?.selectedApp?.code } });
  }

  /**
   * 添加或更新报表数据
   * @param flag
   * @param param
   * @returns
   */
  addBasicReport(flag: any, param: any): Observable<any> {
    const urlFlag = flag === 'edit' ? 'modifyBaseInfo' : 'addByPattern';
    const url = `${this.adesignerUrl}/athena-designer/activityConfigs/${urlFlag}`;
    return this.http.post(url, param);
  }

  // 获取abi报表resid
  getReportResid(param: any): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/activityConfigs/getResidByCodeAndLocale?${param}`;
    return this.http.get(url);
  }

  // 修改界面设计渲染模式
  setPageModel(param: any): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/task/setPageModel`;
    return this.http.post(url, param);
  }

  /**
   * 新增报表个案
   * @param params
   * @returns
   */
  copyReportRule(params): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/activityConfigs/copyReport`;
    return this.http.post(url, params);
  }

  // 复制
  copyBasicRport(param: any): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/activityConfigs/copyStatement`;
    return this.http.post(url, param);
  }

  /**
   * 更新打印模版信息
   */
  updatePrintTemplate(param: any): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/activityConfigs/modify/print/abiStatement`;
    return this.http.post(url, {
      ...param,
      application: this.appService?.selectedApp?.code,
    });
  }

  // 删除
  deleteBasicReport(params: { code: string; resCode: string }): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/activityConfigs/deleteByCode`;
    return this.http.delete(url, { params: { pattern: 'STATEMENT', ...params } });
  }

  // TAG转DSL, 回溯DSL为TAG, recover为true时表示回溯dsl为tag
  transformTag(params): Observable<any> {
    const url = !params.recover
      ? `${this.adesignerUrl}/athena-designer/tagTransform/reportTagTransformDsl`
      : `${this.adesignerUrl}/athena-designer/tagTransform/reportUiBootRecover`;
    return this.http.get(url, { params: { code: params.code } });
  }
}
