import {
  Component,
  EventEmitter,
  Input,
  OnInit,
  Output,
  AfterViewInit,
  ChangeDetectorRef,
  AfterViewChecked,
} from '@angular/core';
import { AppService } from 'pages/apps/app.service';
import { TranslateService } from '@ngx-translate/core';
import { NzMessageService } from 'ng-zorro-antd/message';
import { DtdService } from 'pages/app/dtd/dtd.service';
import { ProjectSetService } from '../service/project-set.service';
import { IndividualService } from 'pages/individual/individual.service';
import { AppTypes } from 'pages/app/typings';

@Component({
  selector: 'data-detection-project-mainline',
  templateUrl: './project-mainline.component.html',
  styleUrls: ['./project-mainline.component.less'],
})
export class DataDetectionProjectMainlineComponent implements OnInit, AfterViewInit, AfterViewChecked {
  @Input() extra: any;
  @Input() projectLoading: boolean;
  @Input() modalFlag: any;
  @Input() isMainPro: any;
  @Input() activeControl: any;
  @Input() currentProject: any;
  @Output() modalClose: EventEmitter<any> = new EventEmitter();

  anchorContainer: any;

  historyModalProps: HistoryModalProps = {
    transferModal: false,
    code: '',
    collection: 'project',
  };

  // 是否公共解决方案
  get isCommonApp() {
    return this.appService.isCommonApp;
  }

  effectAdpVersion: string;

  constructor(
    private translateService: TranslateService,
    private message: NzMessageService,
    private appService: AppService,
    private cdr: ChangeDetectorRef,
    private projectSetService: ProjectSetService,
    public dtdService: DtdService,
    private individualService: IndividualService,
  ) {}

  ngOnInit(): void {
    this.findAppEffectAdpVersion();
  }

  /**
   * 获取应用生效的数据驱动中项目的版本
   */
  findAppEffectAdpVersion() {
    if (this.appService?.selectedApp?.appType === AppTypes.MODEL_DRIVEN) {
      this.dtdService.findAppEffectAdpVersion(this.appService?.selectedApp?.code).subscribe((res: any) => {
        this.effectAdpVersion = res.data;
      });
    }
  }

  ngAfterViewInit(): void {
    this.anchorContainer = document.getElementById('project-add-modal');
  }

  ngAfterViewChecked(): void {
    //Called after every check of the component's view. Applies to components only.
    //Add 'implements AfterViewChecked' to the class.
    this.cdr.detectChanges();
  }

  // 获取按钮是否可点击
  handleValid(): boolean {
    let validate = true;
    if (this.currentProject.basicForm.invalid) {
      validate = false;
    }
    return validate;
  }

  // 校验
  handleValidate(): boolean {
    let validate = true;
    for (const i of Object.keys(this.currentProject.basicForm?.controls)) {
      this.currentProject.basicForm.controls[i].markAsDirty();
      this.currentProject.basicForm.controls[i].updateValueAndValidity();
    }
    if (this.currentProject.basicForm.invalid) {
      validate = false;
    }
    return validate;
  }

  // 保存
  handleSaveProject(): void {
    this.currentProject.formDirty = true;
    if (!this.handleValidate()) {
      return;
    }
    const flag = this.modalFlag;
    const { basicForm, selectedTask = [], lang } = this.currentProject;
    const { primaryProjectCode, ...param } = basicForm.getRawValue();
    const init = this.extra.allDataStatus.find((s) => s.code === param['init']);
    param['init'] = {
      code: init.code,
      dataCode: init.dataCode,
    };
    const end = this.extra.allDataStatus.find((s) => s.code === param['end']);
    param['end'] = {
      code: end.code,
      dataCode: end.dataCode,
    };
    if (param['executeType'] !== 'mainline') {
      param['primaryProjectCode'] = primaryProjectCode;
    }
    param['lang'] = {
      name: lang.name,
      description: lang.description,
    };

    if (param['manualAble'] && !!param['authorityPrefix']) {
      if (this.isCommonApp) {
        param['authorityPrefix'] = `${
          this.individualService?.individualCaseApp
            ? this.individualService.sourceApplicationCode
            : this.appService?.selectedApp?.code
        }:${param.code}`;
      } else {
        param['authorityPrefix'] = `${
          this.individualService?.individualCaseApp
            ? this.individualService.sourceApplicationCode
            : this.appService?.selectedApp?.code
        }:startProject`;
      }
    } else {
      param['authorityPrefix'] = '';
    }
    param['application'] = this.appService?.selectedApp?.code;
    param['subTasks'] = selectedTask;
    this.projectLoading = true;
    if (this.appService?.selectedApp?.appType === AppTypes.MODEL_DRIVEN) {
      param.projectFrom = 'combination';
      param.httpHeader = {
        adpVersion: this.effectAdpVersion,
        adpStatus: 'effect',
      };
    }
    this.dtdService.saveProject(flag, param).subscribe(
      (res) => {
        if (res.code === 0) {
          this.projectLoading = false;
          this.message.success(this.translateService.instant('dj-保存成功！'));
          this.modalClose.emit({ data: param, flag });
          this.projectSetService.projectUpdate$.next(param.code);
          // 如果是手动发起的项目，则通知任务列表更新
          if (param['manualAble'] === true) {
            this.dtdService.updateTaskList$.next(true);
          }
        }
      },
      () => {
        this.projectLoading = false;
      },
    );
  }

  openModifyHistoryModal() {
    this.historyModalProps.code = this.currentProject['basicForm'].get('code')?.value;
    this.historyModalProps.transferModal = true;
  }
}
