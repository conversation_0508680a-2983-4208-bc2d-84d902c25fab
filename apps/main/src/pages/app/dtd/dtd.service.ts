import { HttpClient } from '@angular/common/http';
import { Injectable, OnDestroy } from '@angular/core';
import { SystemConfigService } from 'common/service/system-config.service';
import { AuthService } from 'common/service/auth.service';
import { AuthOperate } from 'common/types/auth.types';
import { AdAuthService } from 'pages/login/service/auth.service';
import { BehaviorSubject, Observable, Subject } from 'rxjs';
import { GlobalService } from 'common/service/global.service';

@Injectable()
export class DtdService implements OnDestroy {
  adesignerUrl: string;

  flowVisibleSbuject$ = new BehaviorSubject<any>(null);
  taskStatusSubject$ = new BehaviorSubject<any>(null);

  orignProBaseInfo: any; // 用于保存 项目的基础信息
  currProTaskInfo: any = null; // 当前项目的任务数据 ，从下一步返回时 的缓存
  updateTaskList$ = new Subject(); // 更新任务列表
  updateTaskData$ = new Subject(); // 更新某一条任务
  updateProjectList$ = new Subject(); // 更新项目列表

  /* 以下用于模板管理： */
  headerCofig = {}; // 用于保存请求头
  applicationCodeProxy = null; // 解决方案code

  constructor(
    protected http: HttpClient,
    protected configService: SystemConfigService,
    private authService: AuthService,
    private globalService: GlobalService,
  ) {
    this.configService.get('adesignerUrl').subscribe((url) => {
      this.adesignerUrl = url;
    });
  }

  get dataDrivenAuth(): boolean {
    return this.authService.getAuth(AuthOperate.UPDATE);
  }

  ngOnDestroy(): void {
    this.flowVisibleSbuject$.unsubscribe();
    this.taskStatusSubject$.unsubscribe();
  }

  setFlowVisible(state: boolean): void {
    this.flowVisibleSbuject$.next(state);
  }

  loadActionInfo(param: { label: string; actionId: string }): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/action/findActionByActionId?label=${param.label}&actionId=${param.actionId}`;
    return this.http.get(url);
  }

  loadTaskData(param: any): Observable<any> {
    // 模板管理替换application并在头信息中添加tempalteId
    if (this.applicationCodeProxy) {
      param = `?applicationCode=${this.applicationCodeProxy}`;
    }
    const url = `${this.adesignerUrl}/athena-designer/task/taskTree${param}`;
    return this.http.get(url, { headers: this.headerCofig });
  }

  loadCurrentTaskData(param: any): Observable<any> {
    // 模板管理替换application并在头信息中添加tempalteId

    const url = `${this.adesignerUrl}/athena-designer/task/getTask/${param}`;
    return this.http.get(url, { headers: this.headerCofig });
  }

  deleteTaskTree(param: any): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/task/deleteTask/${param}`;
    return this.http.get(url, { headers: this.headerCofig });
  }

  //获取任务场景
  loadTaskScenes(param: any): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/action/queryPatternTypePages`;
    return this.http.post(url, param);
  }

  // 获取任务
  loadAllTask(param: any): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/task/getSimpleTasksByApplication/${param}`;
    return this.http.get(url);
  }

  saveTask(flag: any, param: any): Observable<any> {
    const url = {
      add: `${this.adesignerUrl}/athena-designer/task/saveTask`, // 保存
      edit: `${this.adesignerUrl}/athena-designer/task/updateTask`, // 编辑
    };
    if (this.applicationCodeProxy) {
      param.application = this.applicationCodeProxy;
    }
    return this.http.post(url[flag], param, { headers: this.headerCofig });
  }

  // 查询未设定数据状态的任务和已设定数据状态任务之间的关系
  getTaskAndDataState(params: any): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/task/getTaskAndDataState`;
    // 模板管理替换application并在头信息中添加tempalteId
    if (this.applicationCodeProxy) {
      params.application = this.applicationCodeProxy;
    }
    return this.http.get(url, { params, headers: this.headerCofig });
  }

  // 获取task数据
  loadLcdpDataEntryData(param: any): Observable<any> {
    // 模板管理替换application并在头信息中添加tempalteId
    if (this.applicationCodeProxy) {
      param = this.applicationCodeProxy;
    }
    const url = `${this.adesignerUrl}/athena-designer/pageDesign/lcdpDataEntry/${param}`;
    return this.http.get(url);
  }

  // 删除当前project
  deleteAppTree(param: any): Observable<any> {
    console.log('this.globalService.adpVersionHeaders======');
    const url = `${this.adesignerUrl}/athena-designer/project/deleteProject/${param}`;
    return this.http.get(url, { headers: {} });
  }

  saveProject(flag: any, data: any): Observable<any> {
    const { httpHeader, adpVersionHeaders, ...param } = data;
    const url = {
      add: `${this.adesignerUrl}/athena-designer/project/saveProject`, // 保存
      edit: `${this.adesignerUrl}/athena-designer/project/updateProject`, // 编辑
    };

    return this.http.post(url[flag], param, { headers: httpHeader || adpVersionHeaders });
  }

  // 获取appData
  loadAppData(param: any): Observable<any> {
    console.log('this.globalService.adpVersionHeaders======');
    const url = `${this.adesignerUrl}/athena-designer/project/projectTree/${param}`;
    return this.http.get(url, { headers: {} });
  }

  // 根据数据状态查询关联任务
  loadTasksByDataState(param): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/task/getTaskList`;
    return this.http.post(url, param);
  }

  // 获取当前app对应数据
  loadCurrentAppData(param: any): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/project/getProject/${param}`;
    return this.http.get(url);
  }

  loadDataGroupsByApplication(param: any): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/data/dataGroup/listByApplication${param}`;
    return this.http.get(url);
  }

  getOrderChain(param: any): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/task/getOrderChain`;
    return this.http.post(url, param);
  }

  // 获取任務狀態关联关系by群落
  loadGraphByGroup(param: any): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/task/filterGraphVo`;
    return this.http.post(url, param);
  }

  // 保存数据群落-数据状态-任务的图形数据
  saveGroupGraph(param: any): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/task/saveStateRelation`;
    return this.http.post(url, param);
  }

  loadDataStates(param: any): Observable<any> {
    // 模板管理替换application并在头信息中添加tempalteId
    if (this.applicationCodeProxy) {
      param.application = this.applicationCodeProxy;
    }
    const url = `${this.adesignerUrl}/athena-designer/data/findDataStatesByApplication`;
    return this.http.get(url, { params: param, headers: this.headerCofig });
  }

  saveDataState(param: any): Observable<any> {
    if (this.applicationCodeProxy) {
      param.application = this.applicationCodeProxy;
    }
    const url = `${this.adesignerUrl}/athena-designer/data/saveDataState`;
    return this.http.post(url, param, { headers: this.headerCofig });
  }

  // 更新数据状态
  saveTaskDataState(param: any): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/task/saveTaskDataState`;
    return this.http.post(url, param, { headers: this.headerCofig });
  }

  // 保存数据描述和数据状态
  saveDataDescriptionAndState(param: any): Observable<any> {
    if (this.applicationCodeProxy) {
      param.dataDescription.application = this.applicationCodeProxy;
      param.dataStates.forEach((item) => (item.application = this.applicationCodeProxy));
    }
    const url = `${this.adesignerUrl}/athena-designer/data/saveDataDescriptionAndState`;
    return this.http.post(url, param, { headers: this.headerCofig });
  }

  // 根据任务code获取数据特征
  getDataFeatures(code): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/data/dataFeatures/${code}`;
    return this.http.get(url, { headers: this.headerCofig });
  }

  // 获取dataDescription
  loadLevelData(flag: any, param: any): Observable<any> {
    const reflect = {
      dataDescription: `data/findDataDescriptionByCode/${param}`,
      dataState: `data/findDataStateByCode/${param}`,
    };
    const url = `${this.adesignerUrl}/athena-designer/${reflect[flag]}`;
    return this.http.get(url);
  }

  // 删除dataGroup
  deleteLevelData(flag: any, param: any): Observable<any> {
    const reflect = {
      dataGroup: `data/deleteDataGroupByCode/${param}`,
      dataDescription: `data/deleteDataDescriptionByCode/${param}`,
      dataState: `data/deleteDataStateByCode/${param}`,
    };
    const url = `${this.adesignerUrl}/athena-designer/${reflect[flag]}`;
    return this.http.get(url, { headers: this.headerCofig });
  }

  dtdToolBatchSave(param: any): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/dtdTool/batchSave`;
    return this.http.post(url, param);
  }

  // 获取DTD视图数据
  loadProDtdData(param: any): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/project/graph`;
    return this.http.post(url, param);
  }

  loadFlow(param: any): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/activity${param}`;
    return this.http.get(url, { headers: this.headerCofig });
  }

  saveFlowData(param: any): Observable<any> {
    if (this.applicationCodeProxy) {
      param.application = this.applicationCodeProxy;
    }
    const url = `${this.adesignerUrl}/athena-designer/activity`;
    return this.http.post(url, param, { headers: this.headerCofig });
  }

  // 修改界面设计渲染模式
  setPageModel(param: any): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/task/setPageModel`;
    return this.http.post(url, param, { headers: this.headerCofig });
  }

  // 据字段查pageView
  getPageView(code): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/task/pageView/getPageView/${code}`;
    return this.http.get(url);
  }

  savePageView(param: any): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/project/savePageView`;
    return this.http.post(url, param);
  }

  copyMonitorRule(param: any): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/project/copyProject`;
    return this.http.post(url, param);
  }

  copyTaskRule(param: any): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/task/copyTask`;
    return this.http.post(url, param, { headers: this.headerCofig });
  }

  // 保存默认状态
  saveDefaultState(param: any): Observable<any> {
    if (this.applicationCodeProxy) {
      param.dataDescription.application = this.applicationCodeProxy;
      param.dataStates.forEach((item) => (item.application = this.applicationCodeProxy));
    }
    const url = `${this.adesignerUrl}/athena-designer/data/saveDataDescriptionAndSingleState`;
    return this.http.post(url, param, { headers: this.headerCofig });
  }

  // dtd粘贴
  dtdToolPaste(param: any): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/dtdTool/paste`;
    return this.http.post(url, param, { headers: this.headerCofig });
  }

  // dtd收藏
  saveFavouriteDTD(param: any): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/favourite/saveFavouriteDTD`;
    return this.http.post(url, param);
  }

  // flow收藏
  saveFavourite(param: any): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/favourite/saveFavourite`;
    return this.http.post(url, param);
  }

  // DTD完整性校验
  completeDTDValidate(param: any): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/dtd/completeDTDValidate`;
    return this.http.post(url, param, { headers: this.headerCofig });
  }

  /**
   * 保存模板
   * @param params
   * @returns
   */
  saveTemplate(params): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/favourite/saveFavouriteContent`;
    return this.http.post(url, params);
  }

  getActionInfo(param): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/action/findActionByActionId?label=${param.label}&actionId=${param.actionId}`;
    return this.http.get(url);
  }

  findAppEffectAdpVersion(appCode): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/groupHistory/findAppEffectAdpVersion`;
    return this.http.get(url, { params: { application: appCode } });
  }
}
