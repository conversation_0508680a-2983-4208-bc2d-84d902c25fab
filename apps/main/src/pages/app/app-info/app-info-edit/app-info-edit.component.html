<ad-modal
  [nzTitle]="'dj-修改解决方案' | translate"
  [nzWidth]="'520px'"
  [(nzVisible)]="visible"
  [nzMaskClosable]="false"
  [nzFooter]="null"
  [nzClosable]="true"
  (nzOnCancel)="handleCancel()"
>
  <ng-container *adModalContent>
    <form nz-form [formGroup]="appForm" [nzNoColon]="true" class="form-info">
      <div class="row-wrap">
        <nz-form-item>
          <nz-form-label [nzNoColon]="true" [nzRequired]="true">
            {{ 'dj-图标' | translate }}
          </nz-form-label>
          <nz-form-control
            *ngIf="!isAgQuestion"
            [nzErrorTip]="'dj-必填！' | translate"
            [nzValidateStatus]="!appForm.get('iconName').value && appForm.get('iconName').dirty ? 'error' : ''"
          >
            <app-add-app-icon
              [required]="true"
              [label]="'dj-解决方案图标' | translate"
              [value]="value"
              (change)="onAppIconChange($event)"
            ></app-add-app-icon>
          </nz-form-control>
          <nz-form-control *ngIf="isAgQuestion">
            <app-add-app-upload-image [value]="value" (change)="onAppImageChange($event)"></app-add-app-upload-image>
          </nz-form-control>
        </nz-form-item>
        <nz-form-item>
          <nz-form-label [nzNoColon]="true" [nzRequired]="true">
            {{ 'dj-名称' | translate }}
            <i
              adIcon
              iconfont="iconexplain"
              aria-hidden="true"
              nzTooltipTrigger="click"
              class="question-icon"
              nz-tooltip
              nzTooltipTitle="name"
            >
            </i>
          </nz-form-label>
          <nz-form-control
            class="center-control"
            [nzErrorTip]="
              'dj-请输入解决方案名称，且字符长度不超过' + (isAgQuestion ? '8' : '40')
                | translate: { m: isAgQuestion ? 8 : 40 }
            "
          >
            <app-modal-input-agile
              class="lang-input"
              formControlName="name"
              [attr]="{
                name: '名称',
                code: 'name',
                needLang: true,
                maxLength: isAgQuestion ? 8 : 40,
                lang: { value: this.lang?.name }
              }"
              ngDefaultControl
              [innerLabel]="false"
              (callBack)="handlePatchLangApp('name', $event)"
            >
            </app-modal-input-agile>
          </nz-form-control>
        </nz-form-item>
        <nz-form-item>
          <nz-form-label [nzNoColon]="true" [nzRequired]="true">
            {{ 'dj-代号' | translate }}
            <i
              adIcon
              iconfont="iconexplain"
              aria-hidden="true"
              nzTooltipTrigger="click"
              class="question-icon"
              nz-tooltip
              nzTooltipTitle="code"
            >
            </i>
          </nz-form-label>
          <nz-form-control [nzErrorTip]="translateService.instant('dj-必填！')">
            <input type="text" nz-input formControlName="code" [placeholder]="'dj-编号' | translate" />
          </nz-form-control>
        </nz-form-item>
        <nz-form-item *ngIf="isAgApp && !isAgQuestion">
          <nz-form-label [nzNoColon]="true" [nzRequired]="true">
            {{ 'dj-连接系统类型' | translate }}
            <i
              adIcon
              iconfont="iconexplain"
              aria-hidden="true"
              nzTooltipTrigger="click"
              class="question-icon"
              nz-tooltip
              nzTooltipTitle="source"
            >
            </i>
          </nz-form-label>
          <nz-form-control [nzErrorTip]="translateService.instant('dj-必填！')">
            <nz-radio-group formControlName="source" ngDefaultControl>
              <label nz-radio nzValue="dcp">
                {{ 'dj-连接地端系统' | translate }}
              </label>
              <label nz-radio nzValue="bmd">
                {{ 'dj-连接云端系统' | translate }}
              </label>
            </nz-radio-group>
          </nz-form-control>
        </nz-form-item>
        <nz-form-item *ngIf="isAgApp && !isAgQuestion">
          <nz-form-label [nzNoColon]="true" [nzRequired]="true">
            {{ 'dj-连接应用体系' | translate }}
            <i
              adIcon
              iconfont="iconexplain"
              aria-hidden="true"
              nzTooltipTrigger="click"
              class="question-icon"
              nz-tooltip
              nzTooltipTitle="appSystem"
            >
            </i>
          </nz-form-label>
          <nz-form-control [nzErrorTip]="translateService.instant('dj-必填！')">
            <nz-radio-group [nzDisabled]="true" formControlName="appSystem" ngDefaultControl>
              <label nz-radio nzValue="dataset">
                {{ 'dj-数据集体系' | translate }}
              </label>
              <label nz-radio nzValue="metric">
                {{ 'dj-指标场景体系' | translate }}
              </label>
            </nz-radio-group>
          </nz-form-control>
        </nz-form-item>
        <nz-form-item *ngIf="isShowCommomApp">
          <nz-form-label [nzNoColon]="true" [nzRequired]="true">
            {{ 'dj-设置为公共解决方案' | translate }}
            <i
              adIcon
              iconfont="iconexplain"
              aria-hidden="true"
              nzTooltipTrigger="click"
              class="question-icon"
              nz-tooltip
              [nzTooltipTitle]="'dj-公共解决方案的内容会被其他解决方案选择，并在发版时有特殊处理' | translate"
            >
            </i>
          </nz-form-label>
          <nz-form-control>
            {{ (appForm?.get('commonApp')?.value ? 'dj-是' : 'dj-否') | translate }}
            <!--            <nz-switch style="pointer-events: none" formControlName="commonApp"></nz-switch>-->
          </nz-form-control>
        </nz-form-item>
        <nz-form-item>
          <nz-form-label [nzNoColon]="true" [nzRequired]="true">
            {{ 'dj-描述' | translate }}
            <i
              adIcon
              iconfont="iconexplain"
              aria-hidden="true"
              nzTooltipTrigger="click"
              class="question-icon"
              nz-tooltip
              nzTooltipTitle="description"
            >
            </i>
          </nz-form-label>
          <nz-form-control class="center-control" [nzErrorTip]="translateService.instant('dj-必填！')">
            <app-textarea-input-agile
              class="lang-input"
              formControlName="description"
              [attr]="{
                code: 'description',
                needLang: true,
                rows: 3,
                lang: { value: this.lang?.description },
                placeholder: 'dj-请输入' | translate
              }"
              ngDefaultControl
              (callBack)="handlePatchLangApp('description', $event)"
            >
            </app-textarea-input-agile>
          </nz-form-control>
        </nz-form-item>
        <nz-form-item *ngIf="isMdApp && !tenantService.isEduAndisExperience()">
          <nz-form-label [nzNoColon]="true" [nzRequired]="true">
            {{ 'dj-设置' | translate }}
            <i
              adIcon
              iconfont="iconexplain"
              aria-hidden="true"
              nzTooltipTrigger="click"
              class="question-icon-serviceCode"
              nz-tooltip
              [nzTooltipTitle]="'dj-设置解决方案后端' | translate"
            >
            </i>
          </nz-form-label>
          <nz-form-control class="center-control serviceCode-control">
            <div class="serviceCode-set">
              <span (click)="handleOpenModel()" class="data-form-setting">
                {{ 'dj-设置解决方案后端' | translate }}
              </span>
              <div class="target-list-container">
                <ng-container *ngFor="let item of targetList">
                  <li class="target-list">{{ item.serviceCode }}</li>
                </ng-container>
              </div>
            </div>
          </nz-form-control>
        </nz-form-item>
        <ng-container *ngIf="isAgApp">
          <nz-form-item>
            <nz-form-label [nzNoColon]="true" [nzRequired]="true">
              {{ 'dj-计费方式' | translate }}
              <i
                adIcon
                iconfont="iconexplain"
                aria-hidden="true"
                nzTooltipTrigger="click"
                class="question-icon"
                nz-tooltip
                [nzTooltipTitle]="'dj-payType.tip' | translate"
              >
              </i>
            </nz-form-label>
            <nz-form-control [nzErrorTip]="translateService.instant('dj-必填！')">
              <nz-radio-group formControlName="payType" ngDefaultControl (ngModelChange)="handlePayType($event)">
                <label nz-radio nzValue="none">
                  {{ 'dj-无' | translate }}
                </label>
                <label nz-radio nzValue="payPerView">
                  {{ 'dj-按次计费' | translate }}
                </label>
              </nz-radio-group>
            </nz-form-control>
          </nz-form-item>
          <ng-container *ngIf="appForm?.get('payType')?.value !== 'none'">
            <nz-form-item>
              <nz-form-label [nzNoColon]="true" [nzRequired]="true">
                {{ 'dj-计费商品ID' | translate }}
                <i
                  adIcon
                  iconfont="iconexplain"
                  aria-hidden="true"
                  nzTooltipTrigger="click"
                  class="question-icon"
                  nz-tooltip
                  [nzTooltipTitle]="'dj-billingGoodsId.tip' | translate"
                >
                </i>
              </nz-form-label>
              <nz-form-control [nzErrorTip]="translateService.instant('dj-必填！')">
                <input
                  type="text"
                  nz-input
                  formControlName="billingGoodsId"
                  [placeholder]="'dj-计费商品ID' | translate"
                />
              </nz-form-control>
            </nz-form-item>
          </ng-container>
          <ng-container *ngIf="isAgQuestion">
            <nz-form-item>
              <nz-form-label [nzNoColon]="true">
                {{ 'dj-开场白' | translate }}
                <i
                  adIcon
                  iconfont="iconexplain"
                  aria-hidden="true"
                  nzTooltipTrigger="click"
                  class="question-icon"
                  nz-tooltip
                  [nzTooltipTitle]="'dj-用于该应用发布后运行态对话开场白，引导用户开启对话' | translate"
                >
                </i>
              </nz-form-label>
              <nz-form-control class="center-control">
                <app-textarea-input-agile
                  class="lang-input"
                  formControlName="introduction"
                  [attr]="{
                    code: 'introduction',
                    needLang: true,
                    type: 'textarea',
                    maxCharacterCount: 38,
                    maxLength: 38,
                    lang: { value: this.lang?.introduction },
                    placeholder: '用于该应用发布后运行态对话开场白，引导用户开启对话'
                  }"
                  ngDefaultControl
                  (callBack)="handlePatchLangApp('introduction', $event)"
                >
                </app-textarea-input-agile>
              </nz-form-control>
            </nz-form-item>

            <nz-form-item>
              <nz-form-label [nzNoColon]="true">
                {{ 'dj-输入框文案' | translate }}
                <i
                  adIcon
                  iconfont="iconexplain"
                  aria-hidden="true"
                  nzTooltipTrigger="click"
                  class="question-icon"
                  nz-tooltip
                  [nzTooltipTitle]="'dj-用户端输入框提示语' | translate"
                >
                </i>
              </nz-form-label>
              <nz-form-control class="center-control" [nzErrorTip]="'dj-最多输入20个文字' | translate">
                <app-modal-input-agile
                  class="lang-input"
                  formControlName="prompt"
                  [attr]="{
                    name: '输入框文案',
                    code: 'prompt',
                    needLang: true,
                    maxLength: 20,
                    lang: { value: this.lang?.prompt },
                    placeholder: '例：请直接像我提问，最多输入20个文字'
                  }"
                  [innerLabel]="false"
                  ngDefaultControl
                  (callBack)="handlePatchLangApp('prompt', $event)"
                >
                </app-modal-input-agile>
              </nz-form-control>
            </nz-form-item>
          </ng-container>
        </ng-container>
        <!-- 后端通过接口设置的动态表单 -->
        <ng-container *ngIf="solutionCardService.isDynamicsApp(formData?.appType)">
          <ng-container *ngFor="let attr of dynamicsAttributes">
            <ng-container [ngSwitch]="attr.componentType">
              <ng-container *ngSwitchCase="'CHECKBOX'">
                <nz-form-item>
                  <nz-form-label [nzNoColon]="true">
                    {{ attr?.lang?.label?.[('dj-LANG' | translate)] }}
                    <!-- <i
                      adIcon
                      iconfont="iconexplain"
                      aria-hidden="true"
                      nzTooltipTrigger="click"
                      class="question-icon"
                      nz-tooltip
                      [nzTooltipTitle]="'dj-公共解决方案的内容会被其他解决方案选择，并在发版时有特殊处理' | translate"
                    >
                    </i> -->
                  </nz-form-label>
                  <nz-form-control>
                    <nz-switch *ngIf="!attr.disabled" [formControlName]="attr.formControlName"></nz-switch>
                    <ng-container *ngIf="attr.disabled">
                      {{ (appForm?.get(attr.formControlName)?.value ? 'dj-是' : 'dj-否') | translate }}
                    </ng-container>
                  </nz-form-control>
                </nz-form-item>
              </ng-container>
            </ng-container>
          </ng-container>
        </ng-container>
      </div>
    </form>
    <div class="modal-footer">
      <button ad-button adType="default" (click)="handleCancel()">
        {{ 'dj-取消' | translate }}
      </button>
      <button
        [nzLoading]="submitLoading"
        ad-button
        adType="primary"
        (click)="handleSaveApp()"
        style="margin-left: 24px"
      >
        {{ 'dj-确定' | translate }}
      </button>
    </div>
  </ng-container>
</ad-modal>

<app-data-target-modal
  *ngIf="serviceVisible"
  [visible]="serviceVisible"
  [tableData]="targetList"
  [newApplication]="formData?.code"
  (close)="handleCloseModel($event)"
></app-data-target-modal>
