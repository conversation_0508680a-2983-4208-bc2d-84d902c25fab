export enum TenantType {
  PARTNER = '4', // 开发伙伴
  INNERISV = '1', // 内部ISV
  OUTERISV = '2', // 外部ISV
  COMPANYINNER = '3', // 企业内部开发
}

export interface MenuItem {
  title: string;
  parentPath?: string;
  path?: string;
  activePathKeyword?: string[];
  condition?: string[]; // 根据租户类型条件显示菜单
  children?: MenuItem[];
  isLandholderShow?: boolean; // 下地端租户才能看到
  hiddenMenuByEnv?: boolean; // 部分菜单 根据config配置项 不显示
  isLandholderHidden?: boolean; // 下地端租户隐藏
}

export const routes: MenuItem[] = [
  /* 发布管理 */
  {
    title: 'dj-低代码发布',
    children: [
      {
        parentPath: '/devOps-center/',
        path: 'deploy/apps-publish',
        title: 'dj-流水线部署',
      },
      {
        parentPath: '/devOps-center/',
        path: 'deploy/batch-release',
        title: 'dj-批量发版',
      },
      {
        parentPath: '/devOps-center/',
        path: 'deploy/batch-checkout-version',
        title: 'dj-批量切版',
      },
      {
        parentPath: '/devOps-center/',
        path: 'deploy/compile-record',
        title: 'dj-编译记录',
      },
      {
        parentPath: '/devOps-center/',
        path: 'deploy/release-record',
        title: 'dj-发布记录',
      },
      {
        parentPath: '/devOps-center/',
        path: 'deploy/version-query',
        title: 'dj-租户环境',
      },
    ],
  },
  {
    title: 'dj-高代码发布',
    children: [
      {
        parentPath: '/devOps-center/',
        path: 'deploy-project',
        title: 'dj-部署专案',
      },
      {
        parentPath: '/devOps-center/',
        path: 'build-center/build-task/task-list',
        activePathKeyword: ['build-center/build-task'],
        title: 'dj-构建镜像',
      },
    ],
  },
  /* 部署管理 */
  {
    title: 'dj-部署管理',
    hiddenMenuByEnv: true,
    children: [
      {
        parentPath: '/devOps-center/',
        path: 'resourcesMng',
        title: 'dj-资源管理',
      },
      {
        parentPath: '/devOps-center/',
        path: 'harbor-management',
        title: 'dj-仓库管理',
      },
      {
        parentPath: '/devOps-center/',
        path: 'local-config/deploy-mng/deploy-list',
        title: 'dj-部署专案开通',
        activePathKeyword: ['local-config/deploy-mng'],
        isLandholderShow: true, // 只有下地端租户才能看到
      },
      {
        parentPath: '/devOps-center/',
        path: 'docker-image-management',
        title: 'dj-镜像管理',
      },
      {
        parentPath: '/devOps-center/',
        path: 'deploy-version',
        title: 'dj-部署记录',
        condition: [TenantType.PARTNER, TenantType.INNERISV],
      },
      {
        parentPath: '/cdm/',
        path: 'deployment/apollo-config-merge',
        title: 'dj-合并专案Apollo设定',
        condition: [TenantType.INNERISV],
      },
      {
        parentPath: '/cdm/',
        path: 'deployment/redirect-ingress',
        title: 'dj-API转导设定',
        condition: [TenantType.INNERISV],
      },
      {
        title: 'dj-群体部署',
        condition: [TenantType.PARTNER, TenantType.INNERISV],
        children: [
          {
            parentPath: '/cdm/',
            path: 'deployment/group-switch',
            title: 'dj-预区群体转换',
          },
          {
            parentPath: '/cdm/',
            path: 'deployment/switch-record',
            title: 'dj-切换记录查询',
          },
        ],
      },
      {
        title: 'dj-预区设定',
        condition: [TenantType.PARTNER, TenantType.INNERISV],
        children: [
          {
            parentPath: '/cdm/',
            path: 'deployment/app-sale',
            title: 'dj-部署专案-销售品相',
          },
          {
            parentPath: '/cdm/',
            path: 'deployment/ingress-tenant',
            title: 'dj-预区路由设定',
          },
        ],
      },
      {
        title: 'dj-部署参数设定',
        condition: [TenantType.PARTNER, TenantType.INNERISV],
        children: [
          // {
          //   parentPath: '/cdm/',
          //   path: 'deployment/param-manage',
          //   title: 'dj-配置中心管理',
          // },
          {
            parentPath: '/cdm/',
            path: 'parameter/app-config',
            title: 'dj-应用共用参数设定',
          },
          // {
          //   parentPath: '/cdm/',
          //   path: 'deploy-config/ingress-path',
          //   title: 'dj-內外网路径设定',
          // },
          {
            parentPath: '/cdm/',
            path: 'deploy-config/deploy-param',
            title: 'dj-批量设定部署参数',
          },
        ],
      },
      {
        title: 'dj-高代码个案部署',
        condition: [TenantType.PARTNER, TenantType.INNERISV],
        children: [
          {
            parentPath: '/cdm/',
            path: 'deployment/cus-params',
            title: 'dj-个案配置设定',
          },
          {
            parentPath: '/cdm/',
            path: 'deployment/cus-ingress',
            title: 'dj-个案路由设定',
          },
        ],
      },
      {
        title: 'dj-SQL审核',
        isLandholderHidden: true,
        children: [
          {
            parentPath: '/cdm/',
            path: 'work-order/approve-config',
            title: 'dj-审批流管理',
          },
          {
            parentPath: '/cdm/',
            path: 'work-order/work-order-template',
            title: 'dj-工单模板管理',
          },
          {
            parentPath: '/cdm/',
            path: 'work-order/work-order-mng/list',
            title: 'dj-工单管理',
          },
          {
            parentPath: '/cdm/',
            path: 'db/db-manage',
            title: 'dj-数据库实例管理',
            condition: [TenantType.INNERISV, TenantType.PARTNER],
          },
        ],
      },
      {
        title: 'dj-管制版流水线',
        condition: [TenantType.INNERISV],
        children: [
          {
            parentPath: '/cdm/',
            path: 'parameter/user-manager',
            title: 'dj-使用者管理',
          },
          {
            parentPath: '/cdm/',
            path: 'deployment/custom-checklist',
            title: 'dj-自定义检查项',
          },
          {
            parentPath: '/cdm/',
            path: 'deployment/release-check',
            title: 'dj-发版检查设定',
          },
          {
            parentPath: '/cdm/',
            path: 'deployment/control-flow',
            title: 'dj-管制版流水线',
          },
          {
            parentPath: '/cdm/',
            path: 'deployment/control-group-switch',
            title: 'dj-管制版群体切换',
          },
        ],
      },
      {
        title: 'dj-私有化',
        condition: [TenantType.INNERISV],
        children: [
          {
            parentPath: '/prvm/',
            path: 'private/private-proj',
            title: 'dj-私有化专案管理',
          },
          {
            parentPath: '/prvm/',
            path: 'private/private-default-option',
            title: 'dj-版本默认存储项设定',
          },
          {
            parentPath: '/prvm/',
            path: 'private/private-version',
            title: 'dj-私有化版本管理',
          },
          {
            parentPath: '/prvm/',
            path: 'private/private-param',
            title: 'dj-私有化共用参数设定',
          },
          {
            parentPath: '/prvm/',
            path: 'private/package-download',
            title: 'dj-安装包下载',
          },
          {
            parentPath: '/prvm/',
            path: 'private/common-param',
            title: 'dj-常用参数管理',
          },
          {
            parentPath: '/prvm/',
            path: 'private/private-param-replace',
            title: 'dj-私有化项目参数替换',
          },
          {
            parentPath: '/prvm/',
            path: 'private/private-init-data',
            title: 'dj-私有化初始数据管理',
          },
        ],
      },
    ],
  },
  /* 运维管理 */
  {
    title: 'dj-运维管理',
    hiddenMenuByEnv: true,
    children: [
      {
        parentPath: '/devOps-center/',
        path: 'monitor',
        title: 'dj-监控大屏',
      },
      {
        parentPath: '/devOps-center/',
        path: 'system-logger',
        title: 'dj-系統日志',
      },
      {
        parentPath: '/cdm/',
        path: 'work-order/operate-request',
        title: 'dj-运维申请单',
        condition: [TenantType.INNERISV],
      },
      {
        path: 'resource/resource-limit',
        parentPath: '/cdm/',
        title: 'dj-资源限制设定',
        condition: [TenantType.PARTNER, TenantType.INNERISV],
      },
      {
        title: 'dj-数据管理',
        condition: [TenantType.PARTNER, TenantType.INNERISV],
        children: [
          {
            parentPath: '/cdm/',
            path: 'db/backup-manage',
            title: 'dj-测试数据备份管理',
          },
          {
            parentPath: '/cdm/',
            path: 'db/backup-normal',
            title: 'dj-测试数据下载',
          },
          {
            parentPath: '/cdm/',
            path: 'db/log-download',
            title: 'dj-NFS下载',
          },
        ],
      },
    ],
  },
  /* 质量管理 */
  {
    title: 'dj-质量管理',
    hiddenMenuByEnv: true,
    children: [
      {
        parentPath: '/devOps-center/',
        path: 'test-management',
        title: 'dj-测试管理',
      },
      {
        parentPath: '/devOps-center/',
        path: 'quality-dashboard',
        title: 'dj-质量看板',
      },
      {
        parentPath: '/devOps-center/',
        path: 'content-audit/image-audit',
        title: 'dj-镜像审查',
        condition: [TenantType.OUTERISV],
      },
      {
        parentPath: '/devOps-center/',
        path: 'stress-test',
        title: 'dj-压测服务',
        condition: [TenantType.OUTERISV],
      },
    ],
  },
  /* 发版管理 */
  {
    title: 'dj-发版管理',
    hiddenMenuByEnv: true,
    condition: [TenantType.INNERISV],
    children: [
      {
        parentPath: '/devOps-center/',
        path: 'release-management/application-team/application-team-list',
        activePathKeyword: ['release-management/application-team'],
        title: 'dj-应用团队',
      },
      {
        parentPath: '/devOps-center/',
        path: 'release-management/release-project/release-project-list',
        activePathKeyword: ['release-management/release-project'],
        title: 'dj-发版项目',
      },
      {
        parentPath: '/devOps-center/',
        path: 'release-management/release-plan/plan-list',
        activePathKeyword: ['release-management/release-plan'],
        title: 'dj-发版计划',
      },
      {
        parentPath: '/devOps-center/',
        path: 'release-management/sprint-management/sprint-list',
        activePathKeyword: ['release-management/sprint-management'],
        title: 'dj-冲刺管制',
      },
    ],
  },
  /* 基础作业 */
  {
    title: 'dj-基础作业',
    hiddenMenuByEnv: true,
    children: [
      {
        parentPath: '/devOps-center/',
        path: 'OAuthRegist',
        title: 'dj-OAuth注冊',
      },
      {
        parentPath: '/devOps-center/',
        path: 'service-resources-center/apply-credential',
        isLandholderHidden: true,
        title: 'dj-访问凭证申请',
      },
      {
        parentPath: '/devOps-center/',
        path: 'mang-log-center/operation-log',
        activePathKeyword: ['mang-log-center'],
        title: 'dj-操作日志',
      },
    ],
  },
];
