/*
 * @Description: 描述
 * @Author: 庄泽宇
 * @Date: 2023-04-18 08:19:59
 * @LastEditors: 庄泽宇
 * @LastEditTime: 2024-02-26 19:44:10
 */
import { AfterViewInit, Component, Input, OnDestroy, ViewChild } from '@angular/core';
import { AppService } from 'pages/apps/app.service';
import { Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { AppTypes } from 'pages/app/typings';
import { HomeService } from '../service/home.service';
import { AdUserService } from 'pages/login/service/user.service';
import { TenantService } from 'pages/login/service/tenant.service';
import { LocaleService } from 'common/service/locale.service';
import { SystemConfigService } from 'common/service/system-config.service';
import { SolutionBaseInfoFormComponent } from 'components/bussiness-components/solution-base-info-form/solution-base-info-form.component';
import { validatorForm } from 'common/utils/core.utils';
import { FormGroup } from '@angular/forms';
import { NzMessageService } from 'ng-zorro-antd/message';
import { GlobalService } from 'common/service/global.service';
import { NzModalService } from 'ng-zorro-antd/modal';
import { AdModalService } from 'components/ad-ui-components/ad-modal/ad-modal.service';
import { SolutionCardService } from 'app/service/solution-card.service';

@Component({
  selector: 'home-create-blank',
  templateUrl: './create-blank.component.html',
  styleUrls: ['./create-blank.component.less'],
})
export class CreateBlankComponent implements OnDestroy, AfterViewInit {
  @ViewChild('solutionInfoForm') solutionInfoForm: SolutionBaseInfoFormComponent;

  addAppVisible: boolean = false;
  addAgileDataAppVisible: boolean = false;
  addNanaAssistantAppVisible: boolean = false; // 娜娜
  type: string;
  params: any = {};

  timer: any = 0;
  // 浮层的位置
  position: { x: number; y: number } | undefined = undefined;
  // 模板列表是否可见
  templateVisible: boolean = false;
  // 新建解决方案开窗
  createSolutionVisible: boolean = false;
  // 新建解决方案loading
  createSolutionLoading: boolean = false;
  // 默认的可创建的类型的解决方案
  defaultItems: any[] = [
    // {
    //   icon: 'assets/img/home/<USER>',
    //   name: 'dj-数据驱动2.0解决方案',
    //   description: 'dj-通过数据驱动来完成业务流',
    //   type: 'empty',
    //   category: 1,
    //   appType: 5,
    //   detailUrl: 'https://z0lxpczot6u.feishu.cn/wiki/Wu08wIGsjiztb3kr1XwccppxnKg?from=from_copylink',
    // },
    {
      icon: 'assets/img/home/<USER>',
      name: 'dj-敏捷问数1.0',
      description: 'dj-数据获取更加敏捷，言出数至',
      appType: 6,
      detailUrl: 'https://ksd.apps.digiwincloud.com.cn/public/1OKtXWx54',
    },
    {
      icon: 'assets/img/home/<USER>',
      name: 'dj-娜娜助理',
      description: 'dj-通过助理设计器创建娜娜助理',
      type: 'empty',
      appType: 7,
      detailUrl: 'https://z0lxpczot6u.feishu.cn/wiki/IvdVwI9xniUcQ0kDCyuchncLnOh',
    },
    {
      icon: 'assets/img/home/<USER>',
      name: 'dj-AI模型服务',
      description: 'dj-面向开发者提供丰富且标准化的AI能力接口',
      type: 'empty',
      category: 2,
      appType: 8,
      detailUrl: 'https://z0lxpczot6u.feishu.cn/wiki/J9h8wvqifiCdBrk00NJc5SojnYg?from=from_copylink',
    },

    {
      icon: 'assets/img/home/<USER>',
      name: 'dj-敏捷问数2.0',
      description: 'dj-AI助力，零基础数据分析助手',
      appType: 12,
      detailUrl: 'https://ksd.apps.digiwincloud.com.cn/public/1OKtXWx54',
    },
  ];

  createItems: any[];
  dataCenterUrl; //数据中台链接

  @Input() rookieTask: any; // 新手任务
  nanaDesignerUrl = null;
  platformCategory: any;
  currentSolutionCardData: any; // 点击热门卡片的数据
  consoleUrl: string; // 控制台链接
  developerPortalUrl: string; // 控制台链接
  solutionCardLoading = {}; // 创建入口的卡片，点击时需请求权限验证接口，这里记录loading状态

  constructor(
    private app: AppService,
    private router: Router,
    private homeService: HomeService,
    private t: TranslateService,
    private tenantService: TenantService,
    private userService: AdUserService,
    private languageService: LocaleService,
    private configService: SystemConfigService,
    private message: NzMessageService,
    private globalService: GlobalService,
    private modalService: AdModalService,
    private solutionCardService: SolutionCardService,
  ) {
    this.configService.getConfig().subscribe((config: any) => {
      this.dataCenterUrl = config.dataCenterUrl;
      this.platformCategory = config.platformCategory;
    });
  }

  ngOnInit() {}

  /**
   * 获取卡片数据
   * @returns
   */
  public getCardsData() {
    return this.createItems;
  }

  async ngAfterViewInit(): Promise<void> {
    // 获取热门卡片
    try {
      // TODO:暂时注掉，后面可能需要(热门卡片可能需要走接口获取，本期TBB暂时隐藏)
      await this.queryHotCards();
    } catch (error) {}
    if (this.tenantService.isEduAndisExperience()) {
      await this.queryPermission();
    } else {
      this.createItems = this.defaultItems;
    }
  }

  ngOnDestroy(): void {}

  /**
   * 查询可以使用的解决方案类型
   */
  private async queryPermission(): Promise<void> {
    const param = {
      userId: this.userService.getUser('userId'),
      sysId: this.platformCategory === 'TENANT' ? 'athena_tenant_designer' : 'athena-lcdp',
    };
    const types = await this.homeService.queryIamPermission(param);
    // 体验用户正常显示
    types?.concat([-1, -2]);
    this.createItems = this.defaultItems.filter((item) => {
      return types.includes(item.appType);
    });
  }

  async handleAddApp({ event, data }) {
    // 权限检测：是否购买或授权
    // const isContiue = await this.checkCardAuth(data);
    // if (!isContiue) return;

    if (data.appType < 0) {
      this.handleLinkDetail({ event, data });
      return;
    }
    this.type = data.type;
    this.params = { appType: data.appType };
    this.currentSolutionCardData = data;
    if (this.tenantService.isEduAndisExperience()) {
      const target = event.currentTarget as HTMLDivElement;
      const { x, y, height, width } = target.getBoundingClientRect();
      this.position = {
        x: ((x + width / 2) << 0) + 10,
        y: ((y + height / 2) << 0) - 20,
      };
    } else {
      if (data.appType === AppTypes.DATA_VIEW) {
        // 新建解决方案
        this.createSolutionVisible = true;
      } else if ([AppTypes.AGILE_DATA, AppTypes.AGILE_QUESTIONS].includes(data.appType)) {
        this.addAgileDataAppVisible = true;
      } else if (data.appType === AppTypes.NANA_ASSISTANT) {
        this.addNanaAssistantAppVisible = true;
      } else {
        this.addAppVisible = true;
      }
    }
  }

  /**
   * 权限检测：是否购买或授权
   */
  async checkCardAuth(data): Promise<boolean> {
    const { appType } = data;

    if (appType === AppTypes.MODEL_DRIVEN) {
      try {
        this.solutionCardLoading[appType] = true;
        const res = await this.homeService.verifySolutionPermission({ appType }).toPromise();
        console.log('queryIamPermission--->', res);
        // 请求接口获取权限

        return true;
      } catch (error) {
        return false;
      } finally {
        this.solutionCardLoading[appType] = false;
      }
    }
    return true;
  }

  /**
   * 选择了模板
   */
  handleSelectTemplate(template: any): void {
    this.params.sourceCode = template.application;
    this.params = Object.assign({}, this.params, {
      sourceCode: template.application,
      lang: template.lang,
      name: template.name,
      description: template.description,
    });
    this.showCreateAppModal();
  }

  /**
   * 关闭蒙层
   */
  handleCloseMask(): void {
    this.position = undefined;
    this.type = undefined;
    this.params = undefined;
  }

  /**
   * 关闭模板弹框
   */
  handleCloseTemplates(): void {
    this.templateVisible = false;
    this.handleCloseMask();
  }

  /**
   * 选择类别，空白创建还是模板创建
   * @param e
   * @param type
   */
  handleSelectType(e: MouseEvent, type: 'blank' | 'template'): void {
    e.stopPropagation();
    this.position = undefined;
    if (type === 'blank') {
      this.showCreateAppModal();
    } else {
      this.templateVisible = true;
    }
  }

  /**
   * 显示创建App的modal
   */
  private showCreateAppModal() {
    if (this.params?.appType === AppTypes.AGILE_DATA) {
      this.addAgileDataAppVisible = true;
    } else if (this.params?.appType === AppTypes.NANA_ASSISTANT) {
      this.addNanaAssistantAppVisible = true;
    } else {
      this.addAppVisible = true;
    }
  }

  /**
   * 解决方案保存成功回调
   */
  onAfterCreated(app) {
    this.handleCloseTemplates();
    const t = setTimeout(() => {
      clearTimeout(t);
      this.handleSelectApp(app);
      this.homeService.appRefresh$.next(true);
    }, 1500);
  }

  handleRefreshAppList() {
    this.homeService.appRefresh$.next(true);
  }

  onVisibleChange(visible) {
    this.addAppVisible = visible;
  }

  // 选择APP
  handleSelectApp(app: any): void {
    // this.app.selectedApp = app;
    // sessionStorage.setItem('selectedApp', JSON.stringify(app));
    this.app.setAppAuthUser({ code: app.code }).subscribe(
      () => {},
      () => {},
    );

    // this.router.navigateByUrl('app-info');
    // 新开页面
    const { appType } = app;
    let url = '';
    switch (appType) {
      case AppTypes.SCENARIO_KIT:
        url = this.router.serializeUrl(
          this.router.createUrlTree(['app/kit/task-control'], { queryParams: { appCode: app.code } }),
        );
        break;
      case AppTypes.NANA_ASSISTANT: {
        url = this.router.serializeUrl(
          this.router.createUrlTree(['asa-designer-web'], { queryParams: { appCode: app.code } }),
        );
        break;
      }

      case AppTypes.DATA_VIEW:
        // tbb解决方案,获取注册信息->拿到第一个menu后再跳转
        const designerInfo = this.globalService.standaloneDesigners.find((s) => s.appType === appType);
        // 处理hash路由
        const indexUrl = designerInfo.designer.routeMode === 'hash' ? '#/' : '';
        url = decodeURIComponent(
          this.router.serializeUrl(
            this.router.createUrlTree([`standalone-solution${indexUrl}`], { queryParams: { appCode: app.code } }),
          ),
        );
        break;
      case AppTypes.BUSINESS_DOMAIN_CENTER:
        url = this.router.serializeUrl(
          this.router.createUrlTree(['app/business-domain-center/model-design'], {
            queryParams: { appCode: app.code },
          }),
        );
        break;
      default:
        url = this.router.serializeUrl(this.router.createUrlTree(['app'], { queryParams: { appCode: app.code } }));
        break;
    }
    window.open(url, '_blank');
  }

  handleLinkAthena() {
    window.open(`https://z0lxpczot6u.feishu.cn/wiki/HrlqwFmvgipkxVkJGRCcLow6n6k`, '_blank');
  }

  handleLinkDetail({ event, data }) {
    const { detailUrl, params } = data;
    event.stopPropagation();
    // 知识中台入口，支持sso特殊处理
    const token = this.userService.getUser('fxToken');
    let url: string = detailUrl;
    if (params?.appType === -2) {
      const language = this.languageService?.currentLanguage || 'zh_CN';
      url = `${detailUrl}?userToken=${token}&customLang=${language}`;
    }
    if (params?.appType === -1) {
      url = `${this.dataCenterUrl}?userToken=${token}`;
    }
    window.open(url, '_blank');
  }

  /**
   * 创建解决方案-保存
   */
  async handleOk() {
    const form: FormGroup = this.solutionInfoForm.getForm();
    const formValue = this.solutionInfoForm.getFormValue();

    // 触发验证
    validatorForm(form);

    if (form.valid) {
      try {
        this.createSolutionLoading = true;
        const res = await this.homeService.createSolution(formValue).toPromise();
        if (res.code !== 0) {
          return;
        }
        this.message.success(this.t.instant('dj-创建成功'));
        this.createSolutionLoading = false;
        this.homeService.appRefresh$.next(true);
        this.handleSelectApp(formValue);
        this.createSolutionVisible = false;
      } catch (error) {
        this.createSolutionLoading = false;
      }
    }
  }

  /**
   * 查询热门解决方案入口卡片
   * */
  async queryHotCards() {
    const res = await this.solutionCardService.queryHotSolutionCards().toPromise();
    if (res.code !== 0) return;
    res.data.forEach((item) => {
      const { codeRule, enter, auth } = item;
      this.defaultItems.push({
        ...codeRule,
        ...enter,
        auth,
        type: 'empty',
      });
    });
  }
}
