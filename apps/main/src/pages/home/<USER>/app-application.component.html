<section class="my-app-wrapper">
  <h1 class="home-item-title">
    {{ 'dj-我的解决方案' | translate }}
    <i
      adIcon
      iconfont="iconexplain"
      aria-hidden="true"
      class="question-icon"
      nzTooltipTrigger="hover"
      nz-tooltip
      [nzTooltipTitle]="'dj-您可以快速打开最近创建的或最近访问的解决方案' | translate"
    >
    </i>
    <span class="app-search-button">
      <span
        [ngClass]="{ 'app-search-button-item-active': currentKey === item.key, 'app-search-button-item': true }"
        *ngFor="let item of tabList"
        (click)="changeTab(item.key)"
      >
        {{ item.title | translate }}
      </span>
    </span>
    <!-- <span class="app-center-link" (click)="handleAppCenter()"> {{ 'dj-解决方案中心' | translate }} > </span> -->
  </h1>

  <nz-spin [nzSpinning]="loading" *ngIf="loading" class="spin-container"> </nz-spin>

  <div class="content-box" *ngIf="!loading && appList?.length > 0">
    <div *ngFor="let app of appList" class="app-card-container" nz-col>
      <app-card
        [card]="app"
        [showAuth]="false"
        (delFn)="handleDelete($event)"
        (selectAppBack)="handleSelectApp($event, app)"
        (copyFinish)="handleFinishCopy($event)"
      ></app-card>
    </div>
  </div>
  <div class="app-empty-container" *ngIf="!loading && appList?.length === 0">
    <ad-empty [nzNotFoundContent]="contentTpl">
      <ng-template #contentTpl>
        <span *ngIf="currentKey === 'recentCreated'">
          {{ 'dj-开始创建您的第一个解决方案吧' | translate }}
        </span>
        <!-- <span *ngIf="currentKey === 'recentVisited'">
          {{ 'dj-去' | translate
          }}<span class="ath-empty-link" (click)="handleAppCenter()"> {{ 'dj-解决方案中心' | translate }} > </span
          >{{ 'dj-看看吧' | translate }}
        </span> -->
      </ng-template>
    </ad-empty>
  </div>
</section>
