import { Component, OnInit } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { SystemConfigService } from 'common/service/system-config.service';
import { AppService } from 'pages/apps/app.service';

@Component({
  selector: 'home-beginner-tutorial',
  templateUrl: './beginner-tutorial.component.html',
  styleUrls: ['../home.component.less', './beginner-tutorial.component.less'],
})
export class BeginnerTutorialComponent implements OnInit {
  elUrl: string;
  developerPortalUrl: string;
  tutorialItems: any[] = [
    // {
    //   img: 'assets/img/home/<USER>',
    //   title: 'dj-智客中心',
    //   description: 'dj-开发者学习、交流、认证、开发的一站式平台',
    //   type: 'ZhiKeCenter',
    // },
    {
      img: 'assets/img/home/<USER>',
      title: 'dj-文档中心',
      description: 'dj-整体介绍鼎捷雅典娜PaaS平台的核心优势和技术特点',
      type: 'documentCenter',
    },
    // {
    //   img: 'assets/img/home/<USER>',
    //   title: 'dj-开发社区',
    //   description: 'dj-开发者交流阵地、分享技术干货、精选优质文章',
    //   type: 'developmentCommunity',
    // },
    // {
    //   img: 'assets/img/home/<USER>',
    //   title: 'dj-知识学院',
    //   description: 'dj-全面了解雅典娜PaaS平台的相关知识',
    //   type: 'knowledge',
    // },
  ];
  constructor(
    private configService: SystemConfigService,
    private translateService: TranslateService,
    private appService: AppService,
  ) {
    this.configService.get('elUrl').subscribe((url) => {
      this.elUrl = url;
    });
    this.configService.get('developerPortalUrl').subscribe((url) => {
      this.developerPortalUrl = url;
    });
  }

  ngOnInit(): void {}

  handleJumpTo(item: any): void {
    const userInfo = JSON.parse(sessionStorage.getItem('AdUserInfo'));
    const { fxToken = '' } = userInfo;
    switch (item.type) {
      case 'ZhiKeCenter':
        // 跳转智客中心
        window.open(`${this.developerPortalUrl}/sso-login?userToken=${fxToken}`, '_blank');
        break;
      case 'documentCenter':
        // 跳转使用手册
        window.open(`https://z0lxpczot6u.feishu.cn/wiki/HrlqwFmvgipkxVkJGRCcLow6n6k?from=from_copylink`, '_blank');
        break;
      case 'developmentCommunity':
        // 跳转开发社区
        window.open(
          `${this.developerPortalUrl}/sso-login?userToken=${fxToken}&routerLink=${encodeURIComponent(
            'community/list/forum',
          )}`,
          '_blank',
        );
        break;
      case 'knowledge':
        // 跳转知识学院
        window.open(
          `${this.developerPortalUrl}/sso-login?userToken=${fxToken}&routerLink=${encodeURIComponent(
            'knowledge-base/content?documentId=ATHENA&fileId=D000000084',
          )}`,
          '_blank',
        );
        break;

      default:
        break;
    }
  }
}
