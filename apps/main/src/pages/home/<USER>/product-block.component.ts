import { Component, OnInit } from '@angular/core';
import { AdUserService } from 'pages/login/service/user.service';
import { SystemConfigService } from 'common/service/system-config.service';
import { LocaleService } from 'common/service/locale.service';

@Component({
  selector: 'home-product-block',
  templateUrl: './product-block.component.html',
  styleUrls: ['../home.component.less', './product-block.component.less'],
})
export class ProductBlockComponent implements OnInit {
  tbbLoginUrl: string;
  tutorialItems: any[] = [
    {
      img: 'assets/img/home/<USER>',
      title: '敏捷BI-Tip Biu BI',
      description:
        '提供图形化、视觉化的数据分析与数据自助式服务。它能快速对接E10/易飞的指标/报表，透过简单的拖曳即可轻松完成，提供中高阶主管更直觉、更智能的数据分析体验！',
    },
  ];
  constructor(
    protected userService: AdUserService,
    private configService: SystemConfigService,
    private languageService: LocaleService,
  ) {
    this.configService.get('tbbLoginUrl').subscribe((url) => {
      this.tbbLoginUrl = url;
    });
  }

  async ngOnInit() {}

  handleJumpTo(item: any): void {
    const language = this.languageService?.currentLanguage || 'zh_CN';
    const token = this.userService.getUser('fxToken');
    const tenantId = this.userService.getUser('tenantSid');
    window.open(
      `${this.tbbLoginUrl}/index.html#/sso-login?userToken=${token}&dwLang=${language}&routerKey=${tenantId}&platform=lcdp`,
      '_blank',
    );
  }
}
