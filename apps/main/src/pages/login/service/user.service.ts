import { Injectable } from '@angular/core';
import { AdUserStorage } from './user-storage';
import { Subject } from 'rxjs';
import { isEmpty } from 'lodash';

@Injectable()
export class AdUserService {
  /**
   * 取用即時更改內容(存入名稱 = 取出鍵名)
   */
  public static userInfo = {};
  get userInfo(): any {
    return AdUserService.userInfo;
  }
  /**
   * 判断用户是否登陆，如果以后修改登录方式或者影响userInfo里面的数据
   * 需要关注修改此方法
   */
  get isLogin(): any {
    const { userId, token, fxToken } = (AdUserService.userInfo as any) ?? {};
    return !isEmpty(userId) && !isEmpty(token) && !isEmpty(fxToken);
  }

  isLoggedIn$: Subject<any> = new Subject(); // 是否登陆

  constructor(protected userStorage: AdUserStorage) {
    // reload 時, 可以取出已經儲存的資料.
    const userInfo = this.userStorage.get('AdUserInfo');
    if (userInfo) {
      AdUserService.userInfo = JSON.parse(userInfo);
    }
  }

  /**
   * 儲存內容
   * string 須用 {id: key, value: data}傳入
   */
  public setUser(userInfo: any): void {
    if (!userInfo.id) {
      return;
    }
    AdUserService.userInfo[userInfo.id] = userInfo.value;
    this.userStorage.set('AdUserInfo', JSON.stringify(this.userInfo));
  }

  /**
   * 獲取儲存內容(對應id下資料)
   */
  public getUser(key: string): any {
    if (!key) {
      return;
    }

    return AdUserService.userInfo[key];
  }

  /**
   * 清除所有儲存內容(只能清除屬於user的資料).
   */
  public clearUser(): void {
    this.userStorage.remove('AdUserInfo');
    AdUserService.userInfo = {};
  }

  /**
   * 批次儲存使用者資訊.
   * param {*} userInfo
   */
  public setUserInfo(userInfo: any): void {
    for (const field of Object.keys(userInfo)) {
      this.setUser({ id: field, value: userInfo[field] });
    }
  }

  /**
   * 获取用户信息
   * @returns
   */
  getUserInfo(): any {
    return AdUserService.userInfo;
  }
}
