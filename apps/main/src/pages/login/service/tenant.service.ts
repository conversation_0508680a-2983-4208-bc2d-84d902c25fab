import { HttpClient } from '@angular/common/http';
import { Inject, Injectable } from '@angular/core';
import { SystemConfigService } from 'common/service/system-config.service';
import { BehaviorSubject, Observable } from 'rxjs';
import { map, tap } from 'rxjs/operators';
import { AdAuthService, AD_AUTH_TOKEN } from './auth.service';
import { AdUserService } from './user.service';
import { isInternalTenant } from '../../../common/utils/core.utils';

@Injectable({
  providedIn: 'root',
})
export class TenantService {
  iamUrl: string;
  url: string;
  // token [有效或無效] 狀態.
  tokenValidSubject: BehaviorSubject<boolean>;
  tokenValid =
    typeof this.userService.getUser('isLoggedin') === 'boolean' ? this.userService.getUser('isLoggedin') : null;
  dwAppId;
  // 租戶清單.
  tenantSubject: BehaviorSubject<any[]>;
  currTenantList = this.userService.getUser('currTenantList') ? this.userService.getUser('currTenantList') : [];
  // 个案租戶清單.
  individualTenantSubject: BehaviorSubject<any[]>;
  currIndividualTenantList = this.userService.getUser('currIndividualTenantList')
    ? this.userService.getUser('currIndividualTenantList')
    : [];

  // 配置
  private config: { [key: string]: string } = {};

  constructor(
    private http: HttpClient,
    private configService: SystemConfigService,
    @Inject(AD_AUTH_TOKEN) private authToken: any,
    private authService: AdAuthService,
    private userService: AdUserService,
  ) {
    this.configService.getConfig().subscribe((config: any) => {
      this.config = config;
    });
    this.configService.get('iamUrl').subscribe((url) => {
      this.iamUrl = url;
    });
    this.configService.get('adesignerUrl').subscribe((url) => {
      this.url = url;
    });
    this.configService.get('dwAppId').subscribe((dwAppId) => {
      this.dwAppId = dwAppId;
    });
    this.tokenValidSubject = new BehaviorSubject<boolean>(this.tokenValid);
    this.tenantSubject = new BehaviorSubject<any[]>(this.currTenantList);
    this.individualTenantSubject = new BehaviorSubject<any[]>(this.currIndividualTenantList);
  }

  /**
   * 是不是体验环境
   * @param evnAlias
   * @returns
   */
  public isEduEnv() {
    return this.config.envAlias === 'EDU';
  }

  /**
   * 不同的云返回不同的前缀
   * @returns
   */
  public cloudPrefix() {
    if (this.config.cloud === 'MICROSOFT') return 'TW';
    if (this.config.cloud === 'HUAWEI') return 'CN';
    return 'CN';
  }

  /**
   * 是不是体验环境 + 体验用户
   * @returns
   */
  public isEduAndisExperience() {
    return this.isEduEnv() && this.userService.getUser('experience');
  }

  /**
   * 获取体验环境的后端服务
   * @returns
   */
  public getExperienceServiceCode() {
    return this.config.experienceServiceCode;
  }

  /**
   * 获取体验权限到期提示
   * @returns
   */
  getExperienceExpires(): Observable<any> {
    return this.http.get(`${this.url}/athena-designer/user/queryExpirationReminder`);
  }

  /**
   * appId為空, 返回用戶加入的所有企業，傳入appId時, 返回用戶加入並且授權該解决方案的所有企業.
   *
   * returns {Observable<any>}
   */
  getTenantList(): Observable<any> {
    if (this.dwAppId) {
      return this.http.post(`${this.iamUrl}/api/iam/v2/tenant?appId=${this.dwAppId}`, {}).pipe(
        map((tenants: Array<{}>) => {
          // tenants會是一個array.
          return tenants.map((info: {}) => {
            return Object.assign(info, {
              tenantSid: info['sid'],
              tenantId: info['id'],
              tenantName: info['name'],
            });
          });
        }),
      );
    } else {
      return Observable.create((observer: any) => {
        observer.next([]);
        observer.complete();
      });
    }
  }

  getUserTenantList(): Observable<any> {
    if (this.dwAppId) {
      return this.http.get(`${this.url}/athena-designer/tenant/queryUserTenants`).pipe(
        map((res: any) => {
          const tenants: Array<{}> = res?.data ?? [];
          // tenants會是一個array.
          return tenants.map((info: {}) => {
            return Object.assign(info, {
              tenantSid: info['sid'],
              tenantId: info['id'],
              tenantName: info['name'],
            });
          });
        }),
      );
    } else {
      return Observable.create((observer: any) => {
        observer.next([]);
        observer.complete();
      });
    }
  }

  /**
   * 获取个案租户列表
   */
  getIndividualCaseUserTenantList(): Observable<any> {
    if (this.dwAppId) {
      return this.http.get(`${this.url}/athena-designer/tenant/queryIndividualCaseUserTenants`).pipe(
        map((res: any) => {
          const tenants: Array<{}> = res?.data ?? [];
          // tenants會是一個array.
          return tenants.map((info: {}) => {
            return Object.assign(info, {
              tenantSid: info['sid'],
              tenantId: info['id'],
              tenantName: info['name'],
            });
          });
        }),
      );
    } else {
      return Observable.create((observer: any) => {
        observer.next([]);
        observer.complete();
      });
    }
  }

  /**
   * 获取有權限的租户清單.
   */
  getTenants(): Observable<any> {
    return this.getUserTenantList().pipe(
      tap((currTenantList) => {
        this.setTenantList(currTenantList);
      }),
    );
  }

  /**
   * 获取个案中有权限的租户清单
   * @returns
   */
  getIndividualCaseTenants(): Observable<any> {
    return this.getIndividualCaseUserTenantList().pipe(
      tap((currTenantList) => {
        this.setIndividualCaseTenantList(currTenantList);
      }),
    );
  }

  /**
   * 設定租户清單並發送.
   *
   * returns void.
   */
  setTenantList(currTenantList: any): void {
    this.userService.setUserInfo({
      currTenantList: currTenantList,
    });
    this.currTenantList = currTenantList;
    this.tenantSubject.next(currTenantList);
  }

  /**
   * 设定个案租户清单并发送
   * @param currTenantList
   */
  setIndividualCaseTenantList(currTenantList: any): void {
    this.userService.setUserInfo({
      currIndividualTenantList: currTenantList,
    });
    this.currIndividualTenantList = currTenantList;
    this.individualTenantSubject.next(currTenantList);
  }

  // 廣播租戶清單.
  get currTenantList$(): Observable<any[]> {
    return this.tenantSubject;
  }

  /**
   * 广播个案租户清单
   */
  get currIndividualTenantList$(): Observable<any[]> {
    return this.individualTenantSubject;
  }

  /**
   * 切換租戶，重新刷新UserToken.
   *
   * returns {Observable<any>}
   */
  tokenRefreshTenant(
    name: string,
    tenantId: string,
    tenantSid: number,
    testTenant: boolean,
    experience: string,
  ): Observable<any> {
    return this.http
      .post(this.url + '/athena-designer/user/changeTenantId', {
        name: name,
        tenantId: tenantId,
        testTenant,
        experience,
      })
      .pipe(
        tap((res) => {
          const data = res.data;
          data.tenantId = tenantId;
          data.tenantName = name;
          data.tenantSid = tenantSid;
          data.experience = experience;
          // 當新舊 userToken 都一樣時, userToken為有效.
          if (this.authToken.token !== data.token) {
            this.setTokenValid(false);
          }

          // 如果是在[isLoggedIn$:true 已登入]狀態下, 並不會重發登入通知.
          this.authService.setLogined(data);
          this.setTokenValid(true);

          // 判断选择的租户是不是内外部租户
          this.userService.setUserInfo({
            isInternalTenant: isInternalTenant(data.teamId),
          });
        }),
      );
  }

  /**
   * 設定token [有效或無效] 狀態並發送.
   *
   * returns void.
   */
  setTokenValid(status: boolean): void {
    this.tokenValid = status;
    this.tokenValidSubject.next(this.tokenValid);
  }
}
