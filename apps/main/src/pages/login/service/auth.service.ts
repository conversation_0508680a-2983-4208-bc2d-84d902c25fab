import { HttpClient } from '@angular/common/http';
import { Inject, Injectable, InjectionToken, Input } from '@angular/core';
import { Router, RouterStateSnapshot } from '@angular/router';
import { SystemConfigService } from 'common/service/system-config.service';
import { Observable, BehaviorSubject, forkJoin } from 'rxjs';
import { map, switchMap } from 'rxjs/operators';

import { AdUserService } from './user.service';
import { processingUserPermission } from './auth';

import * as crypto from 'crypto-js';
import { IndividualService } from 'pages/individual/individual.service';
declare const JSEncrypt: any;

export const AD_AUTH_TOKEN = new InjectionToken<AdAuthToken>('AD_AUTH_TOKEN');

@Injectable({
  providedIn: 'root',
})
export class AdAuthService {
  iamUrl: string;
  adesignerUrl: string;
  emcUrl: string;
  _userPermission: any[];
  permissionUserId: string;
  userPermissionMap: Map<string, any>;
  authId: string = null;
  defaultLogin;
  dataStandardAuth$ = new BehaviorSubject<boolean>(false); // 默认非授权用户
  loginSuccess =
    typeof this.userService.getUser('isLoggedin') === 'boolean' ? this.userService.getUser('isLoggedin') : null;

  prodEnvs = [];
  /**
   * TODO 跳转登录页面逻辑待调整
   * 登陆地址，适配个案环境
   */
  get loginUrl(): string {
    if (this.individualService?.individualCase) {
      return '/individual/login';
    }
    return this.defaultLogin;
    // return "/login"
  }

  /**
   * 注入Angular Router及AD_AUTH_TOKEN做為保存token訊息用
   * param router Angular Router
   * param authToken AD_AUTH_TOKEN
   */
  constructor(
    protected router: Router,
    private http: HttpClient,
    protected userService: AdUserService,
    private configService: SystemConfigService,
    @Inject(AD_AUTH_TOKEN) protected authToken: any,
    public individualService: IndividualService,
  ) {
    // 設定預設值, 當 F5 reload 時, this.authToken.token 會消失, 必需重新指定.
    this.setAuthToken({});

    this.configService.get('iamUrl').subscribe((url) => {
      this.iamUrl = url;
    });

    this.configService.get('adesignerUrl').subscribe((url) => {
      this.adesignerUrl = url;
    });

    this.configService.get('emcUrl').subscribe((url) => {
      this.emcUrl = url;
    });

    this.configService.get('defaultLogin').subscribe((defaultLogin) => {
      this.defaultLogin = defaultLogin;
    });
  }

  get isLoggedIn(): boolean {
    return this.loginSuccess;
  }

  get prodEnvData(): any[] {
    return this.prodEnvs;
  }

  /**
   * 是否已通過驗證, 提供給AdAuthGuardService驗證用.
   *
   * param  currentUrl 當下的URL，如果驗證不通過，將會導向至登入頁，登入完成後會再導回currentUrl
   * return  是否已驗證
   */
  isAuthenticated(state: RouterStateSnapshot | any): boolean {
    const currentUrl = state.url;
    const isLoggedIn =
      typeof this.userService.getUser('isLoggedin') === 'boolean' ? this.userService.getUser('isLoggedin') : null;

    // 未登入時轉跳登入頁
    if (!isLoggedIn) {
      // this.router.navigate(
      //   [this.loginUrl], // 如果使用this.defaultApp, 會造成無窮迴圈.
      //   {
      //     queryParams: {
      //       returnUrl: currentUrl,
      //     },
      //   },
      // );
      location.href = this.loginUrl
    }
    return isLoggedIn;
  }

  /**
   * 登出並清除儲存紀錄(session storage).
   * 登出時, 先廣播再清值.
   *
   */
  logout(isforward: boolean = true, isTenantActive: boolean = false): void {
    if (this.userService.getUser('token') === undefined) {
      // 在非多頁籤下, 直接貼 url 會出現空白的 layout, 然後點登出沒有反應.
      // this.router.navigateByUrl(this.loginUrl);
      location.href = this.loginUrl
      return;
    }

    this.loginSuccess = false;
    this.userService.clearUser();
    this.authToken.token = '';
    this.authToken.fxToken = '';
    this._userPermission = null; // 登出时 _userPermission 置空

    if (isforward === true) {
      // this.router.navigateByUrl(this.loginUrl); // 如果使用this.defaultApp, 當在 / 進行登出時, 因為 url 一樣, 會無法跳轉.
      location.href = this.loginUrl
    }
    this.userService.isLoggedIn$.next(false);
  }

  /**
   * 設定 AD_AUTH_TOKEN 的值提供給外部使用
   *
   */
  setAuthToken(datas: any): void {
    if (Object.keys(datas).length > 0) {
      this.userService.setUserInfo(datas);
    }
    this.authToken.token = this.userService.getUser('token') ? this.userService.getUser('token') : '';
    this.authToken.fxToken = this.userService.getUser('fxToken') ? this.userService.getUser('fxToken') : '';
  }

  /**
   * 登入成功後, 設定登入的資訊.
   * 登入時, 先設定值再廣播.
   *
   * param {*} loginInfo
   */
  setLogined(loginInfo: any): void {
    this.userService.setUserInfo({ isLoggedin: true });
    // 設定 AD_AUTH_TOKEN.
    this.setAuthToken(loginInfo);

    // 廣播登入狀態
    this.loginSuccess = true;
  }

  /**
   * 取得 API 的 body
   *
   * param {*} userConfig
   * returns {object}
   */
  getLoginApiBody(userConfig: any): Observable<any> {
    return forkJoin([this.getUserSalt(userConfig), this.getClientEncrypt(userConfig)]).pipe(
      map((res) => {
        const body = {
          name: userConfig.name,
          deviceId: userConfig.deviceId,
          identityType: userConfig.identityType,
        };

        if (userConfig.identityType === 'query') {
          Object.assign(body, {
            passwordHash: res[1].passwordHash,
            clientEncryptPublicKey: res[1].clientEncryptPublicKey,
          });
        } else {
          Object.assign(body, {
            verificationCode: userConfig.verificationCode,
            clientEncryptPublicKey: res[1].clientEncryptPublicKey,
          });
        }

        // 如果有服務雲帳號特有的salt則需加傳 passwordHash1
        if (res[0]['salt'] && userConfig.identityType === 'query') {
          Object.assign(body, {
            passwordHash1: crypto.MD5(userConfig.password + res[0]['salt']).toString(),
          });
        }

        return body;
      }),
    );
  }

  /**
   * 取得服務雲用戶的salt值
   *
   */
  protected getUserSalt(userConfig: any): Observable<any> {
    return this.getUserSaltHttp(userConfig.name);
  }

  /**
   * 取得加密過的密碼與 public key
   *
   */
  protected getClientEncrypt(userConfig: any): Observable<any> {
    return this.getAESKey().pipe(
      // 使用 AES key 進行加密密碼
      map((aesKey) => {
        const key = crypto.enc.Utf8.parse(aesKey.aeskey);
        const iv = crypto.enc.Utf8.parse('ghUb#er57HBh(u%g');
        const keys: { clientEncryptPublicKey: string; passwordHash?: string } = {
          clientEncryptPublicKey: aesKey.clientEncryptPublicKey,
        };
        if (userConfig.identityType === 'query') {
          const passwordHash = crypto.AES.encrypt(userConfig.password, key, {
            iv: iv,
            mode: crypto.mode.CBC,
            padding: crypto.pad.Pkcs7,
          }).toString();
          keys.passwordHash = passwordHash;
        }

        return keys;
      }),
    );
  }

  /**
   * 取得編碼用的金鑰
   *
   */
  getAESKey(): Observable<any> {
    return this.getIdentityPublickey().pipe(
      switchMap((publicKey) => {
        const crypt = new JSEncrypt({
          default_key_size: 1024,
        });
        let rsaPublicKey = crypt.getPublicKey();
        rsaPublicKey = rsaPublicKey.replace('-----BEGIN PUBLIC KEY-----\n', '');
        rsaPublicKey = rsaPublicKey.replace('\n-----END PUBLIC KEY-----', '');
        const cryptSrv = new JSEncrypt({ default_key_size: 2048 });
        const publicKeyStr = publicKey['publicKey'];
        cryptSrv.setPublicKey(publicKeyStr);

        const clientEncryptPublicKey = cryptSrv.encrypt(rsaPublicKey);
        return this.getIdentityAeskey(clientEncryptPublicKey).pipe(
          map((encryptAesKey) => {
            const aesKey = crypt.decrypt(encryptAesKey['encryptAesKey']);
            return {
              aeskey: aesKey,
              clientEncryptPublicKey: clientEncryptPublicKey,
            };
          }),
        );
      }),
    );
  }

  /** 获取当前租户的自动切版值 */
  getUserProdEnvs(): Observable<any> {
    return this.getProdEnvs().pipe(
      map((response) => {
        this.prodEnvs = response.data;
        return response?.data;
      }),
    );
  }
  /**
   * 取得服務雲用戶的salt值
   */
  getUserSaltHttp(userId: string): Observable<any> {
    return this.http.post(`${this.iamUrl}/api/iam/v2/user/salt`, {
      id: userId,
    });
  }

  /**
   * 獲取rsa公鑰
   *
   */
  getIdentityPublickey(): Observable<any> {
    return this.http.get(`${this.iamUrl}/api/iam/v2/identity/publickey`);
  }

  /**
   * 獲取加密後的aes的key值
   *
   */
  getIdentityAeskey(publicKey: any): Observable<any> {
    return this.http.post(`${this.iamUrl}/api/iam/v2/identity/aeskey`, {
      clientEncryptPublicKey: publicKey,
    });
  }

  /**
   * 解决方案权限：取得当前租户下，指定用户解决方案的全部功能权限
   *
   */
  loadUserPermission(param: any): Observable<any> {
    const url = `${this.iamUrl}/api/iam/v2/permission/user`;
    return this.http.post(url, param);
  }

  /**
   * 数据标准权限：取得当前租户数据标准模块权限
   *
   */
  loadUserDataStandardPermission(): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/dataStandard/auth/query`;
    return this.http.post(url, {});
  }
  /** 查询正式区发版环境接口 */
  getProdEnvs(): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/agile/autoSwitch/getProdEnvs `;
    return this.http.get(url);
  }
  /**
   * 发送验证码
   */
  sendSmsCodeService(info: any): Observable<any> {
    const url = `${this.emcUrl}/api/emc/v1/verificationCode/mobilephone/${info.userId}/verificationCodelogin`;
    const acceptLanguage = info.currentPhoneArea === '886' ? 'zh-TW,zh;q=0.8' : 'zh-CN,zh;q=0.9';
    return this.http.post(
      url,
      {},
      {
        headers: {
          'Accept-Language': acceptLanguage,
        },
      },
    );
  }

  @Input() set userPermission(userPermission: any[]) {
    this._userPermission = processingUserPermission(userPermission ?? []);
    this.userPermissionMap = new Map(this._userPermission.map((p) => [p.id, p]));
  }

  get userPermission(): any[] {
    return this._userPermission;
  }
}

export interface AdAuthToken {
  token: string;
  fxToken: string;
  expiredDate: any;
}
