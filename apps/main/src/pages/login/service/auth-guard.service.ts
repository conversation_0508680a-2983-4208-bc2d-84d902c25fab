import { Injectable } from '@angular/core';
import {
  ActivatedRouteSnapshot,
  CanActivate,
  CanActivateChild,
  CanLoad,
  Route,
  Router,
  RouterStateSnapshot,
} from '@angular/router';
import { Observable } from 'rxjs';

import { AdAuthService } from './auth.service';
import { AdUserService } from './user.service';

import { isEmpty } from 'common/utils/core.utils';
import { routerPreconditions } from './config/router-guard.config';
import { SystemConfigService } from 'common/service/system-config.service';
import { IndividualService } from 'pages/individual/individual.service';
import { GlobalService } from 'common/service/global.service';

@Injectable()
export class AdAuthGuardService implements CanActivate, CanLoad, CanActivateChild {
  platformCategory: string;
  constructor(
    private userService: AdUserService,
    private authService: AdAuthService,
    private router: Router,
    private configService: SystemConfigService,
    private individualService: IndividualService,
    private globalService: GlobalService,
  ) {
    this.configService.getConfig().subscribe((config) => {
      this.platformCategory = config.platformCategory;
    });
  }

  canLoad(route: Route): boolean | Observable<boolean> | Promise<boolean> {
    return true;
  }

  canActivateChild(
    childRoute: ActivatedRouteSnapshot,
    state: RouterStateSnapshot,
  ): Observable<boolean> | Promise<boolean> | boolean {
    if (this.auth(childRoute, state) && this.checkRouterPreconditions(childRoute, state)) {
      if (this.individualService.individualCase) {
        // 个案设计器无需租户级鉴权
        return true;
      } else {
        const userId = this.userService.getUser('userId');
        if (
          (!isEmpty(userId) && this.authService.permissionUserId !== userId) ||
          this.authService.userPermission === null ||
          this.authService.userPermission === undefined
        ) {
          // 获取用户是否已授权
          this.loadUserDataStandardPermission();

          return true;
        }
        return false;
      }
    }
    return false;
  }

  /**
   * 判断进入路由的前置条件是否满足
   */
  checkRouterPreconditions(childRoute: ActivatedRouteSnapshot, state: RouterStateSnapshot): boolean {
    return routerPreconditions.every((routerPrecondition) => {
      const isReject =
        routerPrecondition.pathRegExp.test(state.url) &&
        routerPrecondition.preconditionList.some((preconditionItem) => {
          if (preconditionItem.type === 'session') {
            return isEmpty(sessionStorage.getItem(preconditionItem.key));
          }
          if (preconditionItem.type === 'params') {
            return !Reflect.has(childRoute.queryParams, preconditionItem.key);
          }
        });

      if (isReject) this.router.navigateByUrl(routerPrecondition.redirectTo);
      return !isReject;
    });
  }

  /**
   * 解决方案权限：获取权限
   */
  loadUserPermission(url: string, userId: string): Observable<any> {
    return new Observable((subscriber) => {
      const param = {
        userId,
        sysId: this.platformCategory === 'TENANT' ? 'athena_tenant_designer' : 'athena-lcdp',
      };
      this.authService.permissionUserId = userId;
      // 之前的权限仍需要保留，给租户级开发平台用，新的权限没有涉及到租户级开发平台的权限
      this.authService.loadUserPermission(param).subscribe(
        (res) => {
          if (res.code === 200) {
            this.authService.userPermission = res.data;
            subscriber.next(true);
          } else {
            this.authService.userPermission = [];
            this.router.navigateByUrl('/');
            subscriber.next(false);
          }
        },
        () => {
          this.authService.userPermission = [];
          this.router.navigateByUrl('/');
          subscriber.next(false);
        },
      );
    });
  }

  /**
   * 数据标准权限：取得当前租户数据标准模块权限
   */
  loadUserDataStandardPermission() {
    this.authService.loadUserDataStandardPermission().subscribe(
      (res) => {
        if (res.code === 0) {
          const { authUserJudge } = res?.data || {};
          this.authService.dataStandardAuth$.next(!!authUserJudge);
        } else {
          this.authService.dataStandardAuth$.next(false);
        }
      },
      () => {
        this.authService.dataStandardAuth$.next(false);
      },
    );
  }

  /**
   * 解决方案权限：匹配条件
   */
  enterCondition(url: string, path: string, query: boolean) {
    if (path === '/app' && query === false && (url === '/app' || url.startsWith('/app?') || url.startsWith('/app/'))) {
      return true;
    }
    if (
      (path === '/' && url === '/') ||
      (path === '/app' && (url === '/app' || url.startsWith('/app?'))) ||
      (path !== '/' && path !== '/app' && url.startsWith(path))
    ) {
      return true;
    }
    return false;
  }

  /**
   * 解决方案权限：当前模块存储
   */
  setAuthId(url: string, path: string, id: string): void {
    this.authService.authId = id;
  }

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot,
  ): Observable<boolean> | Promise<boolean> | boolean {
    if (this.auth(route, state) && this.checkRouterPreconditions(route, state)) {
      if (this.individualService.individualCase) {
        // 个案设计器无需租户级鉴权
        return true;
      } else {
        const userId = this.userService.getUser('userId');
        if (
          (!isEmpty(userId) && this.authService.permissionUserId !== userId) ||
          this.authService.userPermission === null ||
          this.authService.userPermission === undefined
        ) {
          // 获取用户是否已授权
          this.loadUserDataStandardPermission();

          return true;
        }
        return false;
      }
    }
    return false;

    // 是否登入及使用權限
    // return this.auth(route, state);
  }

  private auth(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot,
  ): Observable<boolean> | Promise<boolean> | boolean {
    // return this.authService.isAuthenticated(state) && this.authorizedService.canActivate(route);
    if (this.authService.isAuthenticated(state)) {
      return true;
    }
    return false;
  }
}
