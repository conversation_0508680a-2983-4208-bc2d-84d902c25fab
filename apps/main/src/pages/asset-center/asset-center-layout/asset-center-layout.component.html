<nz-layout class="asset-center-layout-content">
  <!-- 左侧menu -->
  <nz-sider
    *ngIf="menus.length"
    nzCollapsible
    [nzCollapsedWidth]="0"
    [nzWidth]="'180px'"
    [ngClass]="{ 'expend-panel': !isMenuzCollapsed }"
    [nzZeroTrigger]="expendBtnTmpl"
    [(nzCollapsed)]="isMenuzCollapsed"
    class="intro-app-step-1"
  >
    <div class="menu-title cooperate-enter-wrapper">
      {{ 'dj-API管理' | translate }}
      <app-auth-cooperate-enter
        class="cooperate-enter-box"
        [enterType]="AuthResources.RESULT"
      ></app-auth-cooperate-enter>
    </div>

    <ul nz-menu nzMode="inline">
      <!-- 渲染menus -->
      <ng-container *ngFor="let menu of individualService?.individualCase ? individualMenus : menus">
        <!-- 有children则渲染一二级菜单,否则渲染一级link-->
        <ng-container *ngIf="isRenderSecond(menu)">
          <ng-container *ngTemplateOutlet="secondaryMenuTpl; context: { menu }"></ng-container>
        </ng-container>

        <!-- 如果没有children则渲染一级link -->
        <ng-container *ngIf="!menu.children">
          <ng-container *ngTemplateOutlet="menuTpl; context: { menu, isSecondary: false }"></ng-container>
        </ng-container>
      </ng-container>

      <!-- Link菜单模板 -->
      <ng-template #menuTpl let-menu="menu" let-isSecondary="isSecondary">
        <ng-container>
          <li
            nz-menu-item
            routerLinkActive
            [nzDisabled]="menu.disabled"
            [nzSelected]="rla.isActive"
            #rla="routerLinkActive"
            [routerLinkActiveOptions]="menu.path.includes('api-tool') ? { exact: false } : isActiveMatchOptions"
            [nzPaddingLeft]="isSecondary ? 24 : 8"
          >
            <span
              class="program-title"
              *ngIf="menu.id === 'release' || menu.id === 'compile-release'; else normalLinkTpl"
            >
              {{ menu.id | translate }}
            </span>
            <ng-template #normalLinkTpl>
              <a [routerLink]="[menu.path]">
                <i adIcon [type]="menu.icon" *ngIf="menu.icon"></i>
                <span class="program-title"> {{ menu.id | translate }} </span>
              </a>
            </ng-template>
          </li>
        </ng-container>
      </ng-template>

      <!-- 一二级菜单模板 -->
      <ng-template #secondaryMenuTpl let-menu="menu">
        <li
          nz-submenu
          nzTitle="{{ menu.id | translate }}"
          class="submenu-title"
          [nzDisabled]="menu.disabled"
          [nzOpen]="menu.open ?? true"
          routerLinkActive="parent-selected"
          [routerLinkActiveOptions]="{ exact: false }"
          [nzPaddingLeft]="8"
        >
          <!-- routerLink设置在空span上用于匹配一级菜单激活状态，如果放在li上会导致点击跳转 -->
          <span [routerLink]="menu.path"></span>
          <ul>
            <!-- 渲染Link菜单 -->
            <ng-container *ngFor="let item of menu.children">
              <ng-container *ngTemplateOutlet="menuTpl; context: { menu: item, isSecondary: true }"></ng-container>
            </ng-container>
          </ul>
        </li>
      </ng-template>
    </ul>
  </nz-sider>

  <nz-content class="content-box" [ngClass]="{ 'expend-menu-panel': !isMenuzCollapsed, 'is-api-manage': false }">
    <router-outlet></router-outlet>
  </nz-content>
</nz-layout>

<ng-template #expendBtnTmpl>
  <i
    adIcon
    [iconfont]="isMenuzCollapsed ? 'iconcaidanzhankai1' : 'iconcaidanshouqiicon1'"
    class="expend-btn iconfont"
  ></i>
</ng-template>
