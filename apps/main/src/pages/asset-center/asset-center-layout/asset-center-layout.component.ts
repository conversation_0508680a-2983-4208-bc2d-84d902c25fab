import { Component, OnInit } from '@angular/core';
import { IsActiveMatchOptions, Router } from '@angular/router';
import { GlobalService } from 'common/service/global.service';
import { AuthResources } from 'common/types/auth.types';
import { isNone } from 'common/utils/core.utils';
import { AppService } from 'pages/apps/app.service';
import { IndividualService } from 'pages/individual/individual.service';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-asset-center-layout',
  templateUrl: './asset-center-layout.component.html',
  styleUrls: ['./asset-center-layout.component.less'],
})
export class AssetCenterLayoutComponent implements OnInit {
  AuthResources = AuthResources;
  menus: any[] = [
    {
      id: 'asset-center/api-tool',
      path: 'api-tool',
      type: 'program',
      programId: 'asset-center/api-tool',
    },
    // {
    //   id: 'asset-center/custom-control-asset',
    //   path: 'custom-control-asset',
    //   type: 'program',
    //   programId: 'asset-center/custom-control-asset',
    // },
    // {
    //   id: 'asset-center/dtd-asset',
    //   path: 'dtd-asset',
    //   type: 'program',
    //   programId: 'asset-center/dtd-asset',
    // },
    // {
    //   id: 'asset-center/paradigm-share',
    //   path: 'paradigm-share',
    //   type: 'program',
    //   programId: 'asset-center/paradigm-share',
    // },
  ];

  individualMenus: any[] = [
    {
      id: 'asset-center/api-tool',
      path: 'api-tool',
      type: 'program',
      programId: 'asset-center/api-tool',
    },
    // {
    //   id: 'asset-center/custom-control-asset',
    //   path: 'custom-control-asset',
    //   type: 'program',
    //   programId: 'asset-center/custom-control-asset',
    // },
  ];

  isMenuzCollapsed: boolean = false;
  isActiveMatchOptions: IsActiveMatchOptions = {
    matrixParams: 'ignored',
    queryParams: 'ignored',
    paths: 'exact',
    fragment: 'ignored',
  };

  constructor(
    public globalService: GlobalService,
    public appService: AppService,
    private router: Router,
    public individualService: IndividualService,
  ) {}

  ngOnInit(): void {}

  isRenderSecond(menu) {
    if (menu.children?.length > 0 && (isNone(menu.hideBranch) || !menu.hideBranch?.includes('basic'))) {
      return true;
    }
  }
}
