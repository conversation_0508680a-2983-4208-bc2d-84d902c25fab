import { Component, OnInit } from '@angular/core';
import { AppService } from '../../../apps/app.service';
import { integratedRoutes } from '../integrated-automation';
import { IsActiveMatchOptions, Router } from '@angular/router'
import { TenantService } from 'pages/login/service/tenant.service';

@Component({
  selector: 'app-integrated-automation-layout',
  templateUrl: './integrated-automation-layout.component.html',
  styleUrls: ['./integrated-automation-layout.component.scss'],
})
export class IntegratedAutomationLayoutComponent implements OnInit {
  isMenuzCollapsed: boolean;
  menus = integratedRoutes;
  isActiveMatchOptions: IsActiveMatchOptions = {
    matrixParams: 'ignored',
    queryParams: 'ignored',
    paths: 'exact',
    fragment: 'ignored',
  };
  isActiveMatchOptionsSubset: IsActiveMatchOptions = {
    matrixParams: 'ignored',
    queryParams: 'ignored',
    paths: 'subset',
    fragment: 'ignored',
  };

  isDefault: boolean;

  constructor(public appService: AppService, private router: Router, private tenantService: TenantService) {
    this.isDefault = window.location.pathname === '/app/integrated-automation';
  }

  ngOnInit() {
    const userIsExperience = this.tenantService.isEduAndisExperience();
    if (userIsExperience) {
      this.menus = this.menus.filter((e) => e.path !== 'extend-info');
      // 体验用户不允许访问扩展信息页面，如果当前已经砸扩展信息页面，就要重定向到默认的页面
      if (window.location.pathname.includes('extend-info')) {
        this.router.navigate(['/app/integrated-automation'], {
          queryParams: { appCode: this.appService.selectedApp.code },
        });
      }
    }
  }
}
