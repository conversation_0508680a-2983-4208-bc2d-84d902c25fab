import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormBuilder, FormGroup, Validators, ValidatorFn, AbstractControl } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { FlowListService } from '../flow-list.service';
import { cloneDeep } from 'lodash';
import { AppService } from 'pages/apps/app.service';

@Component({
  selector: 'add-tenant-process',
  templateUrl: './add-tenant-process.component.html',
  styleUrls: ['./add-tenant-process.component.less'],
  providers: [FlowListService],
})
export class AddTenantProcessComponent implements OnInit {
  addForm: FormGroup;
  lang: any;
  loading = false;
  @Input() isVisible: boolean = false; // 展示是否显示   //按钮的
  @Input() originData: any = null; // 原始数据
  @Input() addTenantProcessType: 'add' | 'edit' = 'add'; // 新建租户级流程开窗的类型，由于需求变更，现在新建租户级流程开窗 复用 编辑逻辑

  @Output() ok: EventEmitter<any> = new EventEmitter(); //确定
  @Output() cancel: EventEmitter<any> = new EventEmitter(); //取消

  constructor(
    private fb: FormBuilder,
    private translateService: TranslateService,
    private flowListService: FlowListService,
    private appService: AppService,
  ) {}

  ngOnInit(): void {
    this.initFormAndInfo();
  }

  initLang(title?, description?): void {
    const baseLang = {
      en_US: '',
      zh_CN: '',
      zh_TW: '',
    };
    this.lang = {
      title: title ?? cloneDeep(baseLang),
      description: description ?? cloneDeep(baseLang),
    };
  }

  initFormAndInfo() {
    const title = (this.addTenantProcessType === 'edit' && this.originData?.lang?.processName) || null;
    const description = (this.addTenantProcessType === 'edit' && this.originData?.lang?.description) || null;
    const titleStr = (this.addTenantProcessType === 'edit' && this.originData?.processName) || null;
    const descriptionStr = (this.addTenantProcessType === 'edit' && this.originData?.description) || null;

    this.addForm = this.fb.group({
      title: [titleStr, [Validators.required, Validators.maxLength(50)]],
      description: [descriptionStr, [Validators.minLength(0), Validators.maxLength(100)]],
    });
    this.initLang(title, description);
  }

  handlePatch(key: any, data: any): void {
    this.addForm.patchValue({ [key]: data?.value });
    if (data.needLang) {
      this.lang = {
        ...(this.lang || {}),
        [key]: data.lang?.value || data.lang,
      };
    }
  }

  handleOk(): void {
    for (const i of Object.keys(this.addForm?.controls)) {
      this.addForm.controls[i].markAsDirty();
      this.addForm.controls[i].updateValueAndValidity();
    }
    if (this.addForm.valid) {
      const title = this.addForm.controls.title.value;
      const description = this.addForm.controls.description.value;

      this.loading = true;

      let params: any = {
        processName: title,
        description: description,
        lang: {
          processName: this.lang.title,
          description: this.lang.description,
        },
        processId: this.originData.processId,
        objectId: this.originData.objectId,
        application: this.appService?.selectedApp?.code,
      };

      let headers = null;

      if (this.addTenantProcessType === 'edit') {
        headers = {
          adpVersion: this.originData.adpVersion,
          adpStatus: this.originData.adpStatus,
          tenantProcessId: this.originData.tenantProcessId,
          level: 'tenant',
        };
      }

      const api = this.addTenantProcessType === 'edit' ? 'editTenantProcess' : 'addTenantProcess';

      this.flowListService[api]?.(params, headers).subscribe(
        (res) => {
          this.loading = false;
          if (res.code === 0) {
            this.ok.emit({ originData: this.originData });
          }
        },
        () => {
          this.loading = false;
        },
      );
    }
  }

  handleCancel(): void {
    this.addForm.reset();
    this.initLang();
    for (const key in this.addForm.controls) {
      if (this.addForm.controls.hasOwnProperty(key)) {
        this.addForm.controls[key].markAsPristine();
        this.addForm.controls[key].updateValueAndValidity();
      }
    }
    this.cancel.emit();
  }
}
