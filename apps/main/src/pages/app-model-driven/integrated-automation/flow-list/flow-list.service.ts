import { HttpClient } from '@angular/common/http';
import { Injectable, OnDestroy } from '@angular/core';
import { NzMessageService } from 'ng-zorro-antd/message';
import { SystemConfigService } from 'common/service/system-config.service';
import { TranslateService } from '@ngx-translate/core';
import { Observable } from 'rxjs';
import { AppService } from 'pages/apps/app.service';
import dayjs from 'dayjs';
import { UUID } from 'angular2-uuid';

@Injectable()
export class FlowListService implements OnDestroy {
  serviceUrl: string;
  paradigmData: any;
  currentParadigm: any;
  appCode: any;
  constructor(
    protected http: HttpClient,
    protected configService: SystemConfigService,
    private message: NzMessageService,
    private translate: TranslateService,
    private appService: AppService,
  ) {
    this.configService.get('adesignerUrl').subscribe((url) => {
      this.serviceUrl = url;
    });
    this.appCode = this.appService?.selectedApp?.code;
  }

  ngOnDestroy(): void {}

  // 查询流程列表
  getFindProcessList(params: any): Observable<any> {
    const url = `${this.serviceUrl}/athena-designer/process/findProcessPagination`;
    return this.http.get(url, { params: { ...params, application: this.appCode } });
  }

  getProjectDetail(code) {
    const url = `${this.serviceUrl}/athena-designer/project/getProject/${code}`

    return this.http.get<any>(url)
  }

  /**
   * @description 根据触发类型、搜索内容、场景查询项目列表
   * @param params 分页和查询数据
   * @returns
   */
  postProjectList(params: {
    projectFrom: 'single' | 'combination' | '';
    pageNum: number;
    pageSize: number;
    triggerType: string;
    searchValue?: string;
  }) {
    const url = `${this.serviceUrl}/athena-designer/project/v2/queryProjectList`;

    return this.http.post<{ code: number; data?: any }>(url, { ...params, application: this.appCode });
  }

  // 根据触发类型获取对应流程数量
  getFindProcessCountByTriggerType(params: any): Observable<any> {
    const url = `${this.serviceUrl}/athena-designer/process/findProcessCountByTriggerType`;
    return this.http.get(url, { params: { ...params, application: this.appCode } });
  }

  // 删除流程
  postRemoveProcess(params: any): Observable<any> {
    const url = `${this.serviceUrl}/athena-designer/process/removeProcess`;
    return this.http.post(url, params);
  }

  // 删除项目
  deletProjectById(id: any): Observable<any> {
    const url = `${this.serviceUrl}/athena-designer/project/deleteProject/${id}`;
    return this.http.get(url);
  }

  // 查询流程列表(租户级)
  getFindProcessListTenant(params: any): Observable<any> {
    const url = `${this.serviceUrl}/athena-designer/process/tenant/findProcessPagination`;
    return this.http.get(url, { params: { ...params, application: this.appCode } });
  }

  // 新增租户级流程
  addTenantProcess(params: any): Observable<any> {
    const formattedTime = dayjs().format('YYYYMMDDHHmmssSSS');

    const url = `${this.serviceUrl}/athena-designer/process/tenant/addTenantProcess`;
    return this.http.post(url, params, {
      headers: {
        adpVersion: formattedTime,
        adpStatus: 'effect',
        tenantProcessId: `PC_${UUID.UUID()}`,
        level: 'tenant',
      },
    });
  }

  // 设置租户级流程
  editTenantProcess(params: any, headers?: any): Observable<any> {
    const url = `${this.serviceUrl}/athena-designer/process/tenant/editTenantProcess`;
    return this.http.post(url, params, { headers });
  }

  // 删除流程（租户级）
  postRemoveProcessTenant(params: any, headers?: any): Observable<any> {
    const url = `${this.serviceUrl}/athena-designer/process/tenant/removeProcess`;
    return this.http.post(url, params, { headers });
  }

  // 撤回流程（租户级）
  postRevokeProcessTenant(params: any, headers?: any): Observable<any> {
    const url = `${this.serviceUrl}/athena-designer/process/tenant/revokeDeployTenant`;
    return this.http.post(url, params, { headers });
  }
}
