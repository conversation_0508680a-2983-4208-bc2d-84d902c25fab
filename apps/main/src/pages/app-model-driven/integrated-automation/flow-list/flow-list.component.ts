import { Component, OnInit, ViewChild, ChangeDetectorRef, OnD<PERSON>roy } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { LocaleService } from 'common/service/locale.service';
import { NzMessageService } from 'ng-zorro-antd/message';
import { AdModalService } from 'components/ad-ui-components/ad-modal/ad-modal.service';
import { AppService } from 'pages/apps/app.service';
import { TriggerType } from '../view-designer/config/typings';
import { FlowListService } from './flow-list.service';
import { DetectService } from 'pages/app/dtd/detect/detect.service';
import { CreateDetectInfo } from '../view-designer-detect/type';
import { actionTypeShowObject, categoryShowObject } from '../view-designer-detect/config';
import { AdUserService } from 'pages/login/service/user.service';
import { takeUntil } from 'rxjs/operators';
import { Subject } from 'rxjs';

@Component({
  selector: 'app-md-flow-list',
  templateUrl: './flow-list.component.html',
  styleUrls: ['./flow-list.component.less'],
  providers: [FlowListService],
})
export class FlowListComponent implements OnInit, OnDestroy {
  // tab 信息
  TriggerType = TriggerType;
  tab: string = TriggerType.PROCESS_MONITOR;
  modules = [
    {
      key: TriggerType.PROCESS_MONITOR,
      title: 'dj-数据侦测',
      count: 0,
    },
    {
      key: TriggerType.PAGE,
      title: 'dj-人工触发',
      count: 0,
    },
    {
      key: TriggerType.EVENT,
      title: 'dj-事件触发',
      count: 0,
    },
    {
      key: TriggerType.MONITOR_RULE,
      title: 'dj-定时触发',
      count: 0,
    },
  ];

  get modulesLeft() {
    return this.modules.filter((item) => item.key === TriggerType.PROCESS_MONITOR);
  }

  get modulesRight() {
    return this.modules.filter((item) => item.key !== TriggerType.PROCESS_MONITOR);
  }

  currentLanguage: string;
  tableHeight;

  // 查询
  searchValue: string = '';
  pageSize: number = 10;
  pageNum: number = 1;
  total: number = 0;
  loading: boolean = false; // 数据加载
  currentPageData: any = [];
  listOfColumn = [
    {
      title: 'dj-名称',
      ellipsis: true,
    },
    {
      title: 'dj-业务对象',
      ellipsis: true,
    },
    // {
    //   title: 'dj-发布状态',
    //   width: '100px',
    // },
    // {
    //   title: 'dj-生效状态',
    //   width: '150px',
    // },
    {
      title: 'dj-修改人',
      ellipsis: true,
    },
    {
      title: 'dj-操作',
      width: '150px',
    },
  ];

  listOfColumnDetect = [
    {
      title: 'dj-名称',
      ellipsis: true,
    },
    {
      title: 'dj-侦测类型',
      ellipsis: true,
    },
    {
      title: 'dj-侦测执行',
      ellipsis: true,
    },
    {
      title: 'dj-执行内容',
      ellipsis: true,
    },
    {
      title: 'dj-修改人',
      ellipsis: true,
    },
    {
      title: 'dj-操作',
      width: '150px',
    },
  ];

  // 租户级解决方案 内嵌表格列表
  listOfColumnInnerTable = [
    {
      title: 'dj-名称',
      ellipsis: true,
    },
    {
      title: 'dj-描述',
      ellipsis: true,
    },
    {
      title: 'dj-发布租户',
      ellipsis: true,
    },
    {
      title: 'dj-状态',
      width: '100px',
    },
    {
      title: 'dj-操作',
      width: '200px',
    },
  ];

  actionTypeShowObject = actionTypeShowObject;
  categoryShowObject = categoryShowObject;

  // 操作 loading
  validLoading: boolean = false;
  deleteLoading: boolean = false;

  // 新建流程开窗
  modalVisible: boolean = false;

  // 新建租户级流程开窗
  addTenantProcessVisible: boolean = false;
  addTenantProcessType: 'add' | 'edit' = 'add'; // 新建租户级流程开窗的类型，由于需求变更，现在新建租户级流程开窗 复用 编辑逻辑
  addTenantProcessOriginData = null;

  isTenantActive = false; // 租户级 开发平台 是否激活

  tenantProcessPublishObject: {
    processId: string;
    pkValue: string;
    tenantUserId: string;
  } = null; // 租户级流程发布对象

  destroy$ = new Subject();

  @ViewChild('publishButton', { static: false }) publishButtonRef: any;

  constructor(
    private router: Router,
    public appService: AppService,
    private translate: TranslateService,
    private message: NzMessageService,
    private modal: AdModalService,
    private languageService: LocaleService,
    public flowListService: FlowListService,
    public detectService: DetectService,
    public adUserService: AdUserService,
    private cd: ChangeDetectorRef,
    private route: ActivatedRoute,
  ) {
    this.currentLanguage = this.languageService?.currentLanguage || 'zh_CN';
    this.tableHeight = document.body.clientHeight - 400 + 'px';
    this.route.queryParams.pipe(takeUntil(this.destroy$)).subscribe((params) => {
      if (params['active']) this.tab = params['active'];
    });
  }

  ngOnInit() {
    this.updateTenant();
    this.updateTab();
    this.handleLoadData();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  // 初始化流程数量数据
  handleProcessCountData(): void {
    const params = {
      triggerTypes: [
        TriggerType.PROCESS_MONITOR,
        TriggerType.PAGE,
        TriggerType.PROJECT,
        TriggerType.EVENT,
        TriggerType.MONITOR_RULE,
      ].join(','),
    };
    this.flowListService.getFindProcessCountByTriggerType(params).subscribe(
      (res) => {
        if (res.code === 0) {
          this.modules.forEach((item) => {
            item.count = res.data?.[item.key];
          });
        }
      },
      () => {},
    );
  }

  updateTenant() {
    this.isTenantActive = !!this.adUserService.getUser('isTenantActive');
  }

  updateTab() {
    if (this.isTenantActive) {
      this.tab = TriggerType.PAGE;
      this.modules = this.modules.filter((module) => module.key !== TriggerType.PROCESS_MONITOR);
    }
  }

  // 初始化列表数据
  handleLoadData(expandProcessId: any[] = []): void {
    // 获取流程数量
    this.handleProcessCountData();
    // 获取流程列表
    const module: any = this.modules.find((item) => item.key === this.tab);
    const params = {
      triggerType: this.tab,
      name: this.searchValue,
      pageNum: module.pageNum || 1,
      pageSize: module.pageSize || 10,
    };
    this.loading = true;

    // 租户级
    if (this.isTenantActive) {
      this.flowListService.getFindProcessListTenant(params).subscribe(
        (res) => {
          if (res.code === 0) {
            this.loading = false;
            this.total = res.data?.total;
            this.currentPageData = res.data?.data || [];
            this.currentPageData = this.currentPageData.map((pageData) => {
              return {
                ...pageData.appProcess,
                expand: expandProcessId.includes(pageData?.appProcess?.processId),
                tenantProcess: pageData.tenantProcess,
              };
            });
          }
        },
        () => {
          this.loading = false;
        },
      );
      return;
    }

    this.flowListService.getFindProcessList(params).subscribe(
      (res) => {
        if (res.code === 0) {
          this.loading = false;
          this.total = res.data?.total;
          this.currentPageData = res.data?.data || [];
        }
      },
      () => {
        this.loading = false;
      },
    );
  }

  // 处理查询逻辑
  handleChangeTabStatus(params): void {
    const index = this.modules.findIndex((item) => item.key === this.tab);
    this.modules[index] = { ...this.modules[index], ...params };
    this.handleLoadData();
  }

  // 切换 tab 页签
  handleClickTab(module: any): void {
    if (this.tab !== module.key) {
      this.tab = module.key;
      this.pageNum = module.pageNum || 1;
      this.pageSize = module.pageSize || 10;
      this.handleLoadData();
    }
  }

  // 模糊搜索
  handleSearch(value: string): void {
    this.searchValue = value;
    this.pageNum = 1;
    this.handleChangeTabStatus({ pageNum: 1 });
  }

  // 改变表格页码
  handleChangePageIndex(index: number): void {
    this.pageNum = index;
    this.handleChangeTabStatus({ pageNum: index });
  }

  // 改变表格页数
  handleChangePageSize(size: number): void {
    this.pageSize = size;
    this.handleChangeTabStatus({ pageSize: size });
  }

  // 改变状态
  handleChangeStatus(data, status) {
    this.modal.confirm({
      nzContent: this.translate.instant(status === 'Y' ? `dj-确定执行 “生效” 操作吗` : `dj-确定执行 “失效” 操作吗`),
      nzOkLoading: this.validLoading,
      nzBodyStyle: {
        height: '160px',
      },
      nzOnOk: (): void => {
        this.message.warning('您点击了状态，待联调');
        // this.validLoading = true;
        // const param = {};
        // this.flowListService.aaa(param).subscribe((validResult) => {
        //   this.validLoading = false;
        //   if (validResult.code === 0) {
        //     this.message.create('success', this.translate.instant('dj-操作成功'));
        //     this.handleLoadData();
        //   } else {
        //     this.message.error(validResult.data);
        //   }
        // }, () => {
        //   this.validLoading = false;
        // });
      },
    });
  }

  // 编辑
  handleEdit(data): void {
    this.router.navigate([`/app/integrated-automation/view-designer/${data.processId}`], {
      queryParams: {
        appCode: this.appService?.selectedApp?.code,
      },
    });
  }

  // 编辑侦测
  handleEditDetect(data): void {
    this.router.navigate([`/app/integrated-automation/view-designer-detect/${data.code}`], {
      queryParams: {
        appCode: this.appService?.selectedApp?.code,
      },
    });
  }

  // 删除
  handleDelete(data) {
    this.modal.confirm({
      nzContent: this.translate.instant('dj-确定删除么？'),
      nzOkLoading: this.validLoading,
      nzBodyStyle: {
        height: '160px',
      },
      nzOnOk: (): void => {
        this.deleteLoading = true;
        const param = {
          processId: data?.processId,
        };
        this.flowListService.postRemoveProcess(param).subscribe(
          (res) => {
            this.deleteLoading = false;
            if (res.code === 0) {
              this.message.create('success', this.translate.instant('dj-删除成功'));
              this.handleLoadData();
            } else {
              this.message.error(res.data);
            }
          },
          () => {
            this.deleteLoading = false;
          },
        );
      },
    });
  }

  // 删除侦测
  handleDeleteDetect(data) {
    this.modal.confirm({
      nzContent: this.translate.instant('dj-确定删除么？'),
      nzOkLoading: this.validLoading,
      nzBodyStyle: {
        height: '160px',
      },
      nzOnOk: (): void => {
        this.deleteLoading = true;

        this.detectService.deleteTreeDetect(data?.code).subscribe(
          (res) => {
            this.deleteLoading = false;
            if (res.code === 0) {
              this.message.create('success', this.translate.instant('dj-删除成功'));
              this.handleLoadData();
            } else {
              this.message.error(res.data);
            }
          },
          () => {
            this.deleteLoading = false;
          },
        );
      },
    });
  }

  // 增加租户级 工作流 (后来需求变化，现在该弹窗需要支持编辑)
  handleAddTenantProcess(data: any, addTenantProcessType: 'add' | 'edit' = 'add') {
    this.addTenantProcessOriginData = data;
    this.addTenantProcessVisible = true;
    this.addTenantProcessType = addTenantProcessType;
  }

  handleAddBranchModalOk(data) {
    this.addTenantProcessOriginData = null;
    this.addTenantProcessVisible = false;
    this.handleLoadData([data?.originData.processId]);
  }

  handleAddBranchModalcancel() {
    this.addTenantProcessOriginData = null;
    this.addTenantProcessVisible = false;
    this.addTenantProcessType = 'add';
  }

  // 编辑租户级 工作流
  handleEditTenantProcess(data) {
    this.router.navigate([`/app/integrated-automation/view-designer/${data.processId}/${data.objectId}`], {
      queryParams: {
        appCode: this.appService?.selectedApp?.code,
      },
    });
  }

  // 发布租户级 工作流
  handlePublishTenantProcess(data) {
    this.tenantProcessPublishObject = {
      pkValue: data?.objectId,
      tenantUserId: data?.adpTenantList?.[0]?.tenantId,
      processId: data?.processId,
    };
    this.cd.detectChanges();
    this.publishButtonRef.startPublish();
  }

  handlePublicAction(data) {
    this.loading = data;
    if (!data && this.tenantProcessPublishObject) {
      this.handleLoadData([this.tenantProcessPublishObject?.processId]);
      this.tenantProcessPublishObject = null;
    }
  }

  handleCancelPublicAction(data) {
    this.tenantProcessPublishObject = null;
  }

  // 删除租户级 工作流
  handleDeleteTenantProcess(data) {
    const text = `${data?.lang?.processName?.[this.currentLanguage] ?? '--'}`;
    this.modal.confirm({
      nzContent: this.translate.instant('dj-确定删除text吗？', { text: text }),
      nzOkLoading: this.validLoading,
      nzBodyStyle: {
        height: '160px',
      },
      nzOnOk: (): void => {
        this.deleteLoading = true;

        this.flowListService
          .postRemoveProcessTenant(
            {
              objectId: data?.objectId,
            },
            {
              tenantProcessId: data?.tenantProcessId,
              level: 'tenant',
            },
          )
          .subscribe(
            (res) => {
              this.deleteLoading = false;
              if (res.code === 0) {
                this.message.create('success', this.translate.instant('dj-删除成功'));
                this.handleLoadData([data.processId]);
              } else {
                this.message.error(res.data);
              }
            },
            () => {
              this.deleteLoading = false;
            },
          );
      },
    });
  }

  // 撤回租户级 工作流
  handleRevokeTenantProcess(data) {
    const text = `${data?.lang?.processName?.[this.currentLanguage] ?? '--'}`;

    this.modal.confirm({
      nzContent: this.translate.instant('dj-确定撤回text吗？', { text: text }),
      nzOkLoading: this.validLoading,
      nzBodyStyle: {
        height: '160px',
      },
      nzOnOk: (): void => {
        this.deleteLoading = true;

        this.flowListService
          .postRevokeProcessTenant({
            objectId: data?.objectId,
          })
          .subscribe(
            (res) => {
              this.deleteLoading = false;
              if (res.code === 0) {
                this.message.create('success', this.translate.instant('dj-撤回成功'));
                this.handleLoadData([data.processId]);
              } else {
                this.message.error(res.data);
              }
            },
            () => {
              this.deleteLoading = false;
            },
          );
      },
    });
  }

  // 关闭新建流程开窗
  closeFlowModal(createDetectInfo?: CreateDetectInfo): void {
    this.modalVisible = false;
    if (createDetectInfo) this.openViewDesignerDetect(createDetectInfo);
  }

  // 打开新建数据侦测开窗
  openViewDesignerDetect(createDetectInfo: CreateDetectInfo): void {
    this.router.navigate([`/app/integrated-automation/view-designer-detect/add`], {
      queryParams: {
        appCode: this.appService?.selectedApp?.code,
      },
    });
  }

  // 完成新建流程回调
  finishedFlowModal(flowData) {
    const { processId } = flowData;
    this.router.navigate([`/app/integrated-automation/view-designer/${processId}`], {
      queryParams: {
        appCode: this.appService?.selectedApp?.code,
      },
    });
  }

  handleExpand(data: any) {
    data.expand = !data.expand;
  }
}
