import { Component, ElementRef, Input, OnD<PERSON>roy, OnInit, ViewChild } from '@angular/core';
import { DtdDesignerComponent } from '../dtd-designer/dtd-designer.component';
import { ModulePublishButtonComponent } from 'components/bussiness-components/module-publish-button/module-publish-button.component';
import { AppService } from 'pages/apps/app.service';
import { TranslateService } from '@ngx-translate/core';
import { NzMessageService } from 'ng-zorro-antd/message';
import { ActivatedRoute, Router } from '@angular/router';
import { isEmpty } from 'lodash';
import { Subject } from 'rxjs';
import { ViewStoreService } from '../../view-designer/service/store.service';
import { GlobalService } from 'common/service/global.service';
import { DtdDesignerViewApiService } from '../service/api.service';
import { DtdDesignerViewService } from '../service/dtd-designer-view.service';
import { DesignerTypes } from 'app/types';

@Component({
  selector: 'app-dtd-designer-view-header',
  templateUrl: './view-header.component.html',
  styleUrls: ['./view-header.component.less'],
})
export class ViewHeaderComponent implements OnInit, OnDestroy {
  @Input() dtdDesignerRef: DtdDesignerComponent;
  @ViewChild('publishButton') publishButtonRef: ModulePublishButtonComponent;
  effectFrom = DesignerTypes.DTD_WORKFLOW_V2; //因为发布按钮是一个共通的，所以这边区分传个来源去区分一下

  historyModalProps = {
    transferModal: false,
    code: '',
    collection: 'dataGroup',
  };

  versionList: any[] = [];
  manageVersionVisible: boolean = false;
  versionRemarkVisible: boolean = false;
  addVersionLoading: boolean = false;

  loadingVersion: boolean = false;

  destroy$ = new Subject();

  versionLinkTagStatusClassMap = {
    draft: 'vertion-status-draft',
    effect: 'vertion-status-effect',
  };
  versionLinkTagStatusMap = {
    draft: 'dj-草稿',
    effect: 'dj-生效',
  };

  @ViewChild('versionBtn') versionBtnRef!: any;

  get buttonWidth() {
    return this.versionBtnRef?.elementRef?.nativeElement?.offsetWidth || '152';
  }

  get effectData() {
    return {
      application: this.appService.selectedApp?.code,
      adpVersion: this.designerViewService.currentSelectedVersion?.adpVersion,
    };
  }
  get selectedProject() {
    return this.dtdDesignerRef?.getProjectComponentRef()?.getSelectedProject();
  }

  get objectId() {
    const { objectId } = this.activatedRoute.snapshot.params;
    return objectId;
  }

  // 是否是租户级流程的编辑（当前是通过路由中的objectId判断）
  get isTenant() {
    return !!this.objectId;
  }

  constructor(
    private appService: AppService,
    private translateService: TranslateService,
    private message: NzMessageService,
    private router: Router,
    private activatedRoute: ActivatedRoute,
    private athMessageService: NzMessageService,
    private apiService: DtdDesignerViewApiService,
    public designerViewService: DtdDesignerViewService,
    public viewStoreService: ViewStoreService,
  ) {}

  ngOnInit() {
    this.getVersions({
      adpVersion: this.activatedRoute.snapshot?.queryParams?.adpVersion,
      initPage: true,
    });
  }
  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  async goback() {
    // history.back();
    this.router.navigate(['app/integrated-automation/data-execution-new'], {
      queryParams: {
        appCode: this.appService.selectedApp?.code,
      },
    });
  }

  get showCreate() {
    return (
      this.designerViewService.currentSelectedVersion?.adpStatus === 'effect' &&
      this.appService.selectedApp?.tag?.sourceComponent !== 'BC'
    );
  }
  /**
   * 发布
   */
  async handlePublish() {
    // await this.dtdDesignerRef?.graphComponentRef?.saveGraph(false);
    const res = await this.handleSave(false);
    if (res === false) return;
    this.publishButtonRef.startPublish();
  }

  openModifyHistoryModal(): void {
    this.historyModalProps.code = this.appService?.selectedApp?.code;
    this.historyModalProps.collection = 'dataGroup';
    this.historyModalProps.transferModal = true;
  }

  handlePublishAction(e): void {
    const graphRef = this.dtdDesignerRef?.getGraphComponentRef();
    graphRef.loading = e;
  }

  async handleSave(tip = false) {
    // const canPass = await this.dtdService.checkPropsPanelCanContinue();
    // if (!canPass) return;

    const graphRef = this.dtdDesignerRef?.getGraphComponentRef();
    const propsRef = this.dtdDesignerRef?.propsPanelRef?.getPropsContentRef();
    if (propsRef) {
      const res = await propsRef.handleSave(false);
      if (res === false) {
        this.message.warning(this.translateService.instant('dj-配置不完善，请完善属性面板配置'));
        return false;
      }
    }
    await graphRef?.saveGraph(tip);
  }

  /**
   * isFreshDtdCanvas: 刷新部分接口
   * initPage: 初始化刷新整个页面 - 当前组建初始化的时候，入参才会是true
   * onlyRefreshVersionList: 只刷新版本列表
   * 查询应用的所有版本
   */
  getVersions(
    data: {
      adpVersion?: string;
      isFreshDtdCanvas?: boolean;
      initPage?: boolean;
      onlyRefreshVersionList?: boolean;
    } = {},
  ) {
    const { adpVersion, isFreshDtdCanvas, initPage, onlyRefreshVersionList } = data;
    this.loadingVersion = true;
    this.versionList = [];
    const { code, groupCode } = this.appService.selectedApp;
    this.apiService
      .loadDtdVersionList({
        application: code,
        code: groupCode,
      })
      .subscribe(
        (res) => {
          this.loadingVersion = false;
          let versionData: any = {};
          this.versionList = res?.data || [];
          // 查列表的时候 不需要后续操作
          if (onlyRefreshVersionList) {
            return;
          }
          // 带了版本
          if (adpVersion) {
            versionData = res?.data?.find((item) => item.adpVersion === adpVersion);
            //路由带过来的版本号被删除
            if (isEmpty(versionData)) {
              versionData = res?.data?.find((item) => item.adpStatus === 'effect');
              this.refeshDtdCanvas(versionData, true, initPage);
            } else {
              this.refeshDtdCanvas(versionData, isFreshDtdCanvas, initPage);
            }
          } else {
            versionData = res?.data?.find((item) => item.adpStatus === 'effect');
            this.refeshDtdCanvas(versionData, false, initPage);
          }
        },
        () => {
          this.versionList = [];
          this.loadingVersion = false;
        },
      );
  }
  // 改为点击后实时更新list，用来及时查看到可能有推送的版本
  loadVersionChange(visible: boolean) {
    if (visible) {
      this.getVersions({
        adpVersion: this.designerViewService.currentSelectedVersion?.adpVersion,
        onlyRefreshVersionList: true,
      });
    }
  }

  handleRefreshVersionListAndCanvas(isFreshDtdCanvas = false) {
    this.getVersions({
      adpVersion: this.designerViewService.currentSelectedVersion?.adpVersion,
      isFreshDtdCanvas: isFreshDtdCanvas,
    });
  }

  /**
   * 版本管理
   */
  handleVersionManagement() {
    this.manageVersionVisible = true;
  }

  handleUpdateRemark() {
    this.versionRemarkVisible = true;
  }

  /**
   * 修改版本备注
   */
  handleUpdateRemarkOK() {
    this.getVersions({
      adpVersion: this.designerViewService.currentSelectedVersion?.adpVersion,
    });
  }

  /**
   * 关闭版本管理弹窗
   */
  handleManageVisible() {
    this.manageVersionVisible = false;
    this.handleUpdateRemarkOK();
  }

  async handleAddVersion() {
    if (this.versionList?.length > 9) {
      this.athMessageService.error(
        this.translateService.instant('dj-当前设计版本数量已达上限，请选择已有设计版本修改'),
      );
      return;
    }
    this.designerViewService.isSaveLoading = true;
    const res = await this.handleSave(false);
    if (res === false) {
      this.designerViewService.isSaveLoading = false;
      return;
    }
    this.handleCreateNewVersion();
  }

  /**
   * 新增版本
   */
  handleCreateNewVersion() {
    this.addVersionLoading = true;
    this.apiService
      .createDtdVersion({
        application: this.appService.selectedApp?.code,
        code: this.appService.selectedApp?.groupCode,
      })
      .subscribe(
        (res) => {
          this.addVersionLoading = false;
          if (res?.data) {
            this.athMessageService.success(this.translateService.instant('dj-创建成功，将跳转到创建生成的版本'));
            this.refeshDtdCanvas(res?.data, true);
          }
          this.designerViewService.isSaveLoading = false;
        },
        () => {
          this.addVersionLoading = false;
          this.designerViewService.isSaveLoading = false;
        },
      );
  }

  handleSelectVersion(item) {
    const { adpVersion } = this.designerViewService.currentSelectedVersion;
    if (item.adpVersion === adpVersion) {
      this.athMessageService.error(this.translateService.instant('dj-该版本是当前设计器查看的版本'));
      return;
    }
    this.upDateDataByVersion(item);
  }

  async upDateDataByVersion(item) {
    this.designerViewService.isChangeVersionCanNotSave = true;
    this.refeshDtdCanvas(item, true);
  }

  async refeshDtdCanvas(item, isFreshDtdCanvas, initPage: boolean = false) {
    const check = await this.designerViewService.checkPropsPanelCanContinue();
    if (!check) {
      return;
    }

    this.designerViewService.updateAdpVersionHeaders({
      adpVersion: item?.adpVersion,
      adpStatus: item?.adpStatus,
    });

    this.designerViewService.setCurSelectedVerison(item);
    if (initPage) {
      this.handleInitPage();
      return;
    }
    if (isFreshDtdCanvas) {
      // 更新画布 调用画布
      this.dtdDesignerRef.taskComponentRef?.fetchTasks?.();
      this.dtdDesignerRef.dataStateComponentRef?.fetchDataList?.();
      // 需要依赖任务列表
      await this.dtdDesignerRef?.projectComponentRef?.fetchProjects?.();
      this.dtdDesignerRef.getGraphComponentRef().initDataByVersion();
    }
  }

  async handleInitPage() {
    // 更新画布 调用画布
    this.dtdDesignerRef.taskComponentRef?.initPage?.();
    this.dtdDesignerRef.dataStateComponentRef?.initPage?.();
    // 需要依赖任务列表
    await this.dtdDesignerRef?.projectComponentRef?.initPage?.();
    this.dtdDesignerRef.getGraphComponentRef().initPage();
  }

  // 生效后刷新列表
  handleEffectChange(effectFrom) {
    if (this.effectFrom === effectFrom) {
      this.getVersions({
        adpVersion: this.designerViewService.currentSelectedVersion?.adpVersion,
      });
    }
  }

  handleUpDateState() {
    // this.dtdDesignerRef.getGraphComponentRef().init();
  }
}
