import { Component, EventEmitter, Input, Output } from '@angular/core';

@Component({
  selector: 'app-source',
  templateUrl: './source.component.html',
  styleUrls: ['./source.component.less'],
})
export class SourceComponent {
  @Input() currentProject: any;
  @Output() validChanged = new EventEmitter<boolean>();

  transferModal: boolean; // action开窗
  transferData: any; // action开窗数据

  constructor() {}

  // source数据
  handleSourceData(key: any, data: any, index?: number): void {
    // if ([undefined, null].includes(index)) {
    //   this.currentProject.sourceDetailAction[key] = data.value;
    // } else {
    //   this.currentProject.sourceDetailAction.actionParams[index][key] = data.value;
    // }
    // if (this.currentProject.formDirty) {
    //   this.handleValidate();
    // }

    this.currentProject.sourceDetailAction.actionParams[index][key] = data.value;
    const item = this.currentProject.sourceDetailAction.actionParams[index];

    this.handleValidItem(item, key);
  }

  handleValidItem(item, key?: string) {
    const value = item[key];
    let validate = true;

    item['errorKeys'] = key ? (item['errorKeys'] ?? []).filter((s) => s !== key) : [];

    if ([undefined, null, ''].includes(value)) {
      item['errorKeys'].push(key);
      validate = false;
    }

    return validate;
  }

  // 增加 param
  handleAddSource(): void {
    this.currentProject.sourceDetailAction.actionParams.push({
      name: '',
      type: '',
      value: '',
    });
  }

  // 删除 param
  handleDeleteSource(index: any): void {
    this.currentProject.sourceDetailAction.actionParams.splice(index, 1);
  }

  // 校验
  handleValidate(): boolean {
    let validate = true;
    const { actionParams = [] } = this.currentProject.sourceDetailAction;

    actionParams.forEach((s) => {
      const { name, type, value } = s;

      s['errorKeys'] = [];

      if ([undefined, null, ''].includes(name)) {
        s['errorKeys'].push('name');
        validate = false;
      }

      if ([undefined, null, ''].includes(type)) {
        s['errorKeys'].push('type');
        validate = false;
      }

      if ([undefined, null, ''].includes(value)) {
        s['errorKeys'].push('value');
        validate = false;
      }
    });

    this.validChanged.emit(validate);

    return validate;
  }

  // 删除已选action
  handleDeleteAction(): void {
    if (!!this.currentProject.sourceDetailAction && !!this.currentProject.sourceDetailAction.actionId) {
      this.currentProject.sourceDetailAction.actionId = '';
      this.currentProject.sourceDetailAction.actionName = '';
    }
  }

  // 选择action开窗
  handleOpenAction(): void {
    this.transferModal = true;
    this.transferData = {
      actionId: this.currentProject.sourceDetailAction.actionId,
      actionName: this.currentProject.sourceDetailAction.actionName,
      useApp: 'false',
    };
  }

  // 选择action
  handleSelectAction(data: any): void {
    this.currentProject.sourceDetailAction.actionId = data['actionId'];
    this.currentProject.sourceDetailAction.actionName = data['actionName'];
    this.transferModal = false;
  }
}
