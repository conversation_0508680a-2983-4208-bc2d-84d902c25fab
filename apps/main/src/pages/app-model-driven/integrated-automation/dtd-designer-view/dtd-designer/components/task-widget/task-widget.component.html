<ad-modal
  *ngIf="!(addedTask?.driveType === 'scene' && modalFlag === 'add')"
  nzClassName="task-modal"
  [nzWidth]="'987px'"
  [(nzVisible)]="visible"
  [nzTitle]="(modalFlag === 'edit' ? 'dj-编辑任务' : 'dj-新增任务') | translate"
  [nzFooter]="null"
  [nzClosable]="true"
  [nzMaskClosable]="false"
  (nzOnCancel)="cancel.emit()"
>
  <ng-container *adModalContent>
    <section class="task-modal-wrap">
      <div class="task-widget">
        <nz-spin [nzSpinning]="loading">
          <div *ngIf="currentTask" id="taskWidget">
            <div class="left-menu">
              <nz-anchor [nzAffix]="false" [nzContainer]="anchorContainer">
                <nz-link nzHref="#basic" [nzTitle]="basic">
                  <ng-template #basic>
                    <div class="link-title">
                      <span adIcon type="file-text" theme="outline"></span>
                      {{ 'dj-基础信息' | translate }}
                    </div>
                  </ng-template>
                </nz-link>
                <nz-link nzHref="#push" [nzTitle]="push">
                  <ng-template #push>
                    <div class="link-title">
                      <span adIcon type="login" theme="outline"></span>
                      {{ 'dj-推送数据' | translate }}
                    </div>
                  </ng-template>
                </nz-link>
                <nz-link nzHref="#stateMaps" [nzTitle]="stateMaps">
                  <ng-template #stateMaps>
                    <div class="link-title">
                      <span adIcon type="line-chart" theme="outline"></span>
                      {{ 'dj-数据状态' | translate }}
                    </div>
                  </ng-template>
                </nz-link>
                <nz-link
                  *ngIf="currentTask.basicForm.get('type').value === 'approve'"
                  nzHref="#executor"
                  [nzTitle]="executor"
                >
                  <ng-template #executor>
                    <div class="link-title">
                      <span adIcon type="user" theme="outline"></span>
                      {{ 'dj-执行人' | translate }}
                    </div>
                  </ng-template>
                </nz-link>
                <nz-link nzHref="#exception" [nzTitle]="exception">
                  <ng-template #exception>
                    <div class="link-title">
                      <span adIcon type="save" theme="outline"></span>
                      {{ 'dj-方案设计' | translate }}
                    </div>
                  </ng-template>
                </nz-link>
                <nz-link
                  nzHref="#scene"
                  [nzTitle]="scene"
                  *ngIf="currentTask.basicForm.get('executeType').value !== 'auto'"
                >
                  <ng-template #scene>
                    <div class="link-title">
                      <span adIcon type="appstore" theme="outline"></span>
                      {{ 'dj-关联场景' | translate }}
                    </div>
                  </ng-template>
                </nz-link>
                <nz-link nzHref="#assign" [nzTitle]="assign">
                  <ng-template #assign>
                    <div class="link-title">
                      <span adIcon type="send" theme="outline"></span>
                      {{ 'dj-转派' | translate }}
                    </div>
                  </ng-template>
                </nz-link>
                <!--执行方式为人工的才显示评价模型的选择-->
                <nz-link
                  nzHref="#decision"
                  [nzTitle]="decision"
                  *ngIf="currentTask.basicForm.get('executeType').value !== 'auto'"
                >
                  <ng-template #decision>
                    <div class="link-title">
                      <span adIcon type="calculator" theme="outline"></span>
                      {{ 'dj-评价模型' | translate }}
                    </div>
                  </ng-template>
                </nz-link>
              </nz-anchor>
            </div>
            <section class="modal-content">
              <app-task-basic-widget *ngIf="isLoaded" id="basic" [currentTask]="currentTask" [extra]="extra">
              </app-task-basic-widget>
              <nz-divider [nzDashed]="true"></nz-divider>
              <app-task-push-widget *ngIf="isLoaded" id="push" [currentTask]="currentTask" [extra]="extra">
              </app-task-push-widget>
              <nz-divider [nzDashed]="true"></nz-divider>
              <app-task-state-widget *ngIf="isLoaded" id="stateMaps" [currentTask]="currentTask" [extra]="extra">
              </app-task-state-widget>
              <nz-divider [nzDashed]="true"></nz-divider>
              <app-task-executor-widget
                id="executor"
                *ngIf="currentTask.basicForm.get('type').value === 'approve' && isLoaded"
                [currentTask]="currentTask"
                [extra]="extra"
              >
              </app-task-executor-widget>
              <nz-divider [nzDashed]="true"></nz-divider>
              <app-task-resolve-widget *ngIf="isLoaded" id="exception" [currentTask]="currentTask" [extra]="extra">
              </app-task-resolve-widget>
              <nz-divider [nzDashed]="true"></nz-divider>
              <app-task-scene-widget
                *ngIf="isLoaded && currentTask.basicForm.get('executeType').value !== 'auto'"
                id="scene"
                [currentTask]="currentTask"
              >
              </app-task-scene-widget>
              <nz-divider [nzDashed]="true"></nz-divider>
              <app-task-assign-widget id="assign" *ngIf="isLoaded" [currentTask]="currentTask">
              </app-task-assign-widget>
              <nz-divider [nzDashed]="true"></nz-divider>
              <app-task-decision-widget
                *ngIf="isLoaded && currentTask.basicForm.get('executeType').value !== 'auto'"
                id="decision"
                [currentTask]="currentTask"
              >
              </app-task-decision-widget>
              <nz-divider [nzDashed]="true"></nz-divider>
              <app-autoAdd-paradigm-tip *ngIf="modalFlag === 'add'"></app-autoAdd-paradigm-tip>
            </section>
            <div class="footer-buttons">
              <ng-container *operateAuth="{ prefix: 'update', templateSignal: !globalService.hollowPromise }">
                <button
                  *ngIf="this.currentTask['basicForm'].get('code')?.value && !applicationCodeProxy"
                  ad-button
                  adType="default"
                  class="ant-btn-primary-border"
                  (click)="openModifyHistoryModal()"
                  style="margin-right: 10px"
                >
                  {{ 'dj-修改历史' | translate }}
                </button>
              </ng-container>
              <button ad-button adType="default" (click)="handleClose()">
                {{ 'dj-取消' | translate }}
              </button>
              <ng-container *operateAuth="{ prefix: 'update', templateSignal: !globalService.hollowPromise }">
                <button
                  [noSubmit]="!handleValid()"
                  [disabled]="!handleValid()"
                  ad-button
                  adType="primary"
                  (click)="handleSaveTask()"
                >
                  {{ 'dj-确定' | translate }}
                </button>
              </ng-container>
            </div>
          </div>
        </nz-spin>
      </div>
    </section>
  </ng-container>
</ad-modal>
<app-task-scene
  *ngIf="addedTask?.driveType === 'scene' && isLoaded && modalFlag === 'add'"
  [visible]="visible"
  [loading]="loading"
  [modalFlag]="modalFlag"
  [currentTaskData]="currentTaskData"
  [addedTask]="addedTask"
  (modalClose)="handleSaveTask($event)"
  (onCancel)="cancel.emit()"
>
</app-task-scene>

<app-modify-history-modal
  *ngIf="historyModalProps.transferModal"
  [historyModalProps]="historyModalProps"
  [extendHeader]="dtdDesignerViewService.adpVersionHeaders"
  [application]="applicationCodeProxy ?? appService?.selectedApp?.code"
  (closeModal)="historyModalProps.transferModal = false"
>
</app-modify-history-modal>
