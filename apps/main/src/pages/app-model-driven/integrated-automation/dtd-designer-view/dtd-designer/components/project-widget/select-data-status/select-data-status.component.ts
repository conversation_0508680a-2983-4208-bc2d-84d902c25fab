import { Component, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';
import { AppService } from 'pages/apps/app.service';
import { AddDataService } from '../../add-data/add-data.service';
import { ApiService } from '../service/api.service';

@Component({
  selector: 'app-select-data-status',
  templateUrl: './select-data-status.component.html',
  styleUrls: ['./select-data-status.component.less'],
})
export class SelectDataStatusComponent implements OnInit {
  showModal: boolean = false;
  hasValue: boolean = false;
  @Input() value: any;
  @Input() optionList: any;
  @Input() labelName: any;
  @Input() isRequired: boolean = false;
  @Input() innerLabel: boolean = true;
  @Input() nzDisabled: boolean = false;
  @Input() effectAdpVersion: string;
  @Output() update: EventEmitter<any> = new EventEmitter();
  @Output() callBack: EventEmitter<any> = new EventEmitter();
  @Output() beforeUpdateOperation: EventEmitter<any> = new EventEmitter();
  @ViewChild('nzSelectRef') nzSelectRef: any;
  isFocusing: boolean = false;

  constructor(private addDataService: AddDataService, private appService: AppService, private apiService: ApiService) {}

  ngOnInit(): void {
    if (this.value) this.hasValue = true;
    this.handleUpdate();
  }

  handleUpdate(beforeUpdateOpt?: boolean): void {
    this.apiService.loadDataStates({ application: this.appService?.selectedApp?.code }).subscribe((res) => {
      if (res.code === 0) {
        this.optionList = (res.data || []).map((s) => {
          return {
            ...s,
            label: s.name,
            value: s.code,
          };
        });
        this.update.emit({ list: this.optionList });
        if (beforeUpdateOpt) this.beforeUpdateOperation.emit();
      }
    });
  }

  handleChange(value): void {
    if (value) {
      this.hasValue = true;
    } else {
      this.hasValue = false;
    }
    this.callBack.emit({ value });
  }

  handleAdd(): void {
    this.showModal = true;
  }

  handleAddDataModal(): void {
    this.showModal = false;
    this.handleUpdate(true);
  }

  handleLabelClick(): void {
    this.hasValue = true;
    this.nzSelectRef.focus();
    this.nzSelectRef.nzOpen = true;
  }

  handleFocus(): void {
    this.isFocusing = true;
    this.handleUpdate();
  }

  handleBlur(): void {
    this.isFocusing = false;
    if (this.value) {
      this.hasValue = true;
    } else {
      this.hasValue = false;
    }
  }

  trackByValue(index: number, item: any) {
    return item.value;
  }
}
