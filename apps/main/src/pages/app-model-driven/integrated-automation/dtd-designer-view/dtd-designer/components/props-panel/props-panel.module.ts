import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzSpinModule } from 'ng-zorro-antd/spin';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzRadioModule } from 'ng-zorro-antd/radio';
import { AdIconModule } from 'components/ad-ui-components/ad-icon/ad-icon.module';
import { TranslateModule } from '@ngx-translate/core';
import { AdTabsModule } from 'components/ad-ui-components/ad-tabs/ad-tabs.module';
import { AdButtonModule } from 'components/ad-ui-components/ad-button/ad-button.module';
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox';
import { InputModule } from 'components/form-components/input/input.module';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzInputNumberModule } from 'ng-zorro-antd/input-number';
import { NzTreeModule } from 'ng-zorro-antd/tree';
import { AdModalModule } from 'components/ad-ui-components/ad-modal/ad-modal.module';
import { NzMessageModule } from 'ng-zorro-antd/message';
import { AdTreeSelectModule } from 'components/ad-ui-components/ad-tree-select/ad-tree-select.module';
import { NzDividerModule } from 'ng-zorro-antd/divider';
import { NzAnchorModule } from 'ng-zorro-antd/anchor';
import { DirectiveModule } from 'common/directive/directive.module';
import { ExtendedInfoModule } from 'components/bussiness-components/extended-info/extended-info.module';
import { NzCollapseModule } from 'ng-zorro-antd/collapse';
import { NzSwitchModule } from 'ng-zorro-antd/switch';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { AdSelectModule } from 'components/ad-ui-components/ad-select/ad-select.module';
import { NzTreeSelectModule } from 'ng-zorro-antd/tree-select';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzModalModule } from 'ng-zorro-antd/modal';

import { PropsPanelComponent } from './props-panel.component';
import { WorkflowTaskPropertyComponent } from './workflow-task-property/workflow-task-property.component';

import { PropsPanelApiService } from './props-panel-api.service';
import { DataStatusComponent } from './data-status/data-status.component';
import { FlowTaskPropsComponent } from './flow-task-props/flow-task-props.component';
import { PushDataComponent } from './flow-task-props/components/push-data/push-data.component';
import { StateImportJsonModule } from '../state-import-json/state-import-json.module';
import { SchemeDesignComponent } from './flow-task-props/components/scheme-design/scheme-design.component';
import { SignOffExecutorComponent } from './flow-task-props/components/sign-off-executor/sign-off-executor.component';

import { ProjectPropertyComponentModule } from './project-property/project-property.module';
import { TaskDataStatusComponent } from './flow-task-props/components/task-data-status/task-data-status.component';
import { ExtendEditorModalModule } from 'components/bussiness-components/extend-editor-modal/extend-editor-modal.module';
import { JsonEditorModalModule } from 'components/bussiness-components/json-editor-modal/json-editor-modal.module';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { ScriptEditorModule } from 'components/bussiness-components/script-editor/script-editor.module';
import { PropsVariableItemComponent } from './data-status/props-variable-item/props-variable-item.component';
import { PropsVariableModalModule } from './data-status/props-variable-modal/variable-modal.module';
import { NzDropDownModule } from 'ng-zorro-antd/dropdown';

@NgModule({
  imports: [
    CommonModule,
    NzFormModule,
    NzSpinModule,
    NzTableModule,
    NzRadioModule,
    AdIconModule,
    TranslateModule,
    AdTabsModule,
    AdButtonModule,
    NzCheckboxModule,
    NzInputModule,
    InputModule,
    NzTreeModule,
    AdModalModule,
    NzMessageModule,
    AdTreeSelectModule,
    NzDividerModule,
    NzAnchorModule,
    DirectiveModule,
    ExtendedInfoModule,
    NzCollapseModule,
    FormsModule,
    ReactiveFormsModule,
    AdSelectModule,
    NzSwitchModule,
    NzIconModule,
    StateImportJsonModule,
    ProjectPropertyComponentModule,
    NzInputNumberModule,
    NzTreeSelectModule,
    ExtendEditorModalModule,
    NzButtonModule,
    NzModalModule,
    JsonEditorModalModule,
    NzToolTipModule,
    ScriptEditorModule,
    PropsVariableModalModule,
    NzDropDownModule
  ],
  declarations: [
    PropsPanelComponent,
    WorkflowTaskPropertyComponent,
    DataStatusComponent,
    FlowTaskPropsComponent,
    PushDataComponent,
    SchemeDesignComponent,
    SignOffExecutorComponent,
    TaskDataStatusComponent,
    PropsVariableItemComponent
  ],
  exports: [
    PropsPanelComponent,
    WorkflowTaskPropertyComponent,
    DataStatusComponent,
    FlowTaskPropsComponent,
    PushDataComponent,
    SchemeDesignComponent,
    SignOffExecutorComponent,
    TaskDataStatusComponent
  ],
  providers: [PropsPanelApiService],
})
export class PropsPanelModule { }
