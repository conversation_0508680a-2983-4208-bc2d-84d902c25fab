<section class="tool-bar">
  <div class="tool-left">
    <ng-container
      *operateAuth="{ prefix: 'create', templateSignal: !globalService.hollowPromise, guards: [dtdReference] }"
    >
      <a class="option" (click)="handleNewTaskClick()">
        <i adIcon [iconfont]="'iconshulidejiahao'" aria-hidden="true"></i>
        {{ 'dj-任务节点' | translate }}
        <span class="new-tag">new</span>
      </a>
    </ng-container>

    <ng-container
      *operateAuth="{ prefix: 'create', templateSignal: !globalService.hollowPromise, guards: [dtdReference] }"
    >
      <a class="option" nz-dropdown nzTrigger="click" [nzDropdownMenu]="dropdownMenu">
        <i adIcon [iconfont]="'iconshulidejiahao'" aria-hidden="true"></i>
        {{ 'dj-任务节点' | translate }}
      </a>
      <!-- 添加任务的下拉 -->
      <nz-dropdown-menu #dropdownMenu="nzDropdownMenu">
        <task-item-menu [position]="taskItemMenuPosition" (select)="handleAddTask($event)"></task-item-menu>
      </nz-dropdown-menu>
    </ng-container>
    <ng-container
      *operateAuth="{ prefix: 'create', templateSignal: !globalService.hollowPromise, guards: [dtdReference] }"
    >
      <a *ngIf="globalService.hollowPromise" class="option" (click)="handleAddDataState()">
        <i adIcon [iconfont]="'iconshulidejiahao'" aria-hidden="true"></i>
        {{ 'dj-数据节点' | translate }}
      </a>
    </ng-container>
    <!-- <ng-container *operateAuth="{ prefix: 'create', templateSignal: !globalService.hollowPromise }">
      <a
        *ngIf="!isFromTemplate"
        class="option"
        nz-dropdown
        nzTrigger="click"
        [(nzVisible)]="popoverVisible"
        [nzDropdownMenu]="popover"
      >
        <i adIcon [iconfont]="'iconshulidejiahao'" aria-hidden="true"></i>
        {{ 'dj-复用模板' | translate }}
      </a>
      <div *ngIf="globalService.hollowPromise" class="divider"></div>
    </ng-container> -->

    <nz-dropdown-menu #popover="nzDropdownMenu">
      <app-add-template-popover #templatePopover (pasteDTD)="handlePasteDtd($event)"></app-add-template-popover>
    </nz-dropdown-menu>
    <a class="option" (click)="handleSetScaleDefault()">100%</a>
    <a class="option" (click)="handleZoomUp()">
      <i adIcon [iconfont]="'iconfangda2'" nz-tooltip [nzTooltipTitle]="'dj-放大' | translate"></i>
    </a>
    <a class="option" (click)="handleZoomDown()">
      <i adIcon [iconfont]="'iconsuoxiao2'" nz-tooltip [nzTooltipTitle]="'dj-缩小' | translate" aria-hidden="true"></i>
    </a>
    <!-- <ng-container *operateAuth="{ prefix: 'update', templateSignal: !globalService.hollowPromise }">
      <div *ngIf="globalService.hollowPromise" class="divider"></div>
      <a *ngIf="!isFromTemplate" class="option" (click)="handleCopy()">
        <i adIcon [iconfont]="'iconfuzhi11'" nz-tooltip [nzTooltipTitle]="'dj-复制' | translate"></i>
      </a>
    </ng-container>

    <ng-container *operateAuth="{ prefix: 'update', templateSignal: !globalService.hollowPromise }">
      <a *ngIf="!isFromTemplate" class="option" (click)="handlePaste()">
        <i adIcon [iconfont]="'iconpaste1'" nz-tooltip [nzTooltipTitle]="'dj-粘贴' | translate"></i>
      </a>
    </ng-container> -->
    <ng-container
      *operateAuth="{ prefix: 'delete', templateSignal: !globalService.hollowPromise, guards: [dtdReference] }"
    >
      <a *ngIf="globalService.hollowPromise" class="option" (click)="handleRemove()">
        <i adIcon [iconfont]="'iconxiaoliebiaoshanchu'" nz-tooltip [nzTooltipTitle]="'dj-删除' | translate"></i>
      </a>
    </ng-container>
    <!-- <ng-container *operateAuth="{ prefix: 'update', templateSignal: !globalService.hollowPromise }">
      <a *ngIf="!isFromTemplate" class="option" (click)="handleCollect()">
        <i adIcon [iconfont]="'iconshoucang4'" nz-tooltip [nzTooltipTitle]="'dj-收藏' | translate"></i>
      </a>
    </ng-container> -->
    <!-- <ng-container>
      <button ad-button adType="default">{{ 'dj-对比生效版本' | translate }}</button>
    </ng-container> -->

    <a class="option" *ngIf="isDraftVersion" (click)="showCompareDialog()">
      <i adIcon [iconfont]="'iconbidui-xian'" aria-hidden="true" style="scale: 1.2"></i>
      {{ 'dj-对比生效版本' | translate }}
    </a>
    <a class="option" *ngIf="!dtdReference" (click)="onShowError()">
      {{ 'dj-报错信息' | translate }}
    </a>
  </div>
  <!-- <div class="tool-right">
    <a *ngIf="globalService.hollowPromise" class="option" (click)="openModifyHistoryModal()">
      <i adIcon [iconfont]="'iconshixiaokongzhi'"></i>
      {{ 'dj-修改历史' | translate }}
    </a>

    <ng-container *operateAuth="{ prefix: 'update', templateSignal: !globalService.hollowPromise }">
      <app-module-publish-button
        *ngIf="!isFromTemplate"
        #publishButton
        [module]="'dtd'"
        [needTenant]="true"
        [needSave]="true"
        [disabled]="isDisabled"
        (clickPublicAction)="handlePublish()"
        (publicAction)="handlePublishAction($event)"
      ></app-module-publish-button>
    </ng-container>
    <ng-container *operateAuth="{ prefix: 'update', templateSignal: !globalService.hollowPromise }">
      <button
        *ngIf="!isFromTemplate"
        ad-button
        adType="primary"
        [noSubmit]="isDisabled"
        (click)="isDisabled ? null : handleSave()"
      >
        {{ 'dj-保存' | translate }}
      </button>
    </ng-container>
  </div> -->
</section>

<app-modify-history-modal
  *ngIf="historyModalProps.transferModal"
  [historyModalProps]="historyModalProps"
  [extendHeader]="dtdService.adpVersionHeaders"
  [application]="isFromTemplate ? applicationCodeProxy : appService.selectedApp?.code"
  (closeModal)="historyModalProps.transferModal = false"
>
</app-modify-history-modal>

<div class="help" *ngIf="!isSkip">
  <span>{{ 'dj-点击新增按钮，可添加任务、数据，或模版节点至画布。' | translate }}</span>
  <span class="submit" (click)="handleHelp()">{{ 'dj-我知道了' | translate }}</span>
</div>

<!--任务开窗-->
<app-task-widget
  *ngIf="taskModalVisible"
  [visible]="taskModalVisible"
  modalFlag="add"
  [addedTask]="addedTask"
  [favouriteCode]="favouriteCode"
  [applicationCodeProxy]="applicationCodeProxy"
  (afterSave)="handleAfterSaveModalSave($event)"
  (cancel)="taskModalVisible = false"
>
</app-task-widget>

<!-- 新建新T -->
<app-flow-widget
  *ngIf="newTModalVisible"
  [visible]="newTModalVisible"
  [favouriteCode]="favouriteCode"
  [applicationCodeProxy]="applicationCodeProxy"
  (afterSave)="handleAfterSaveFlow($event)"
  (cancel)="newTModalVisible = false"
>
</app-flow-widget>

<!-- 数据开窗 -->
<ad-modal
  [(nzVisible)]="dataStateModalVisible"
  [nzTitle]="'dj-新增数据' | translate"
  [nzWidth]="'520px'"
  [nzClosable]="true"
  (nzOnCancel)="dataStateModalVisible = false"
  [nzMaskClosable]="false"
  [nzFooter]="null"
>
  <ng-container *adModalContent>
    <app-add-data
      *ngIf="dataStateModalVisible"
      [data]="dataStateModalData"
      type="ADD"
      [favouriteCode]="favouriteCode"
      [applicationCodeProxy]="applicationCodeProxy"
      (afterSave)="afterDataStateModalSave($event)"
      (cancel)="dataStateModalVisible = false"
    >
    </app-add-data>
  </ng-container>
</ad-modal>

<!-- 收藏开窗 -->
<template-collection-modal
  *ngIf="collectVisible"
  [(visible)]="collectVisible"
  [title]="'dj-收藏为DTD模板' | translate"
  (confirm)="handleCollectConfirm($event)"
  (cancel)="handleCollectCancel()"
>
</template-collection-modal>
