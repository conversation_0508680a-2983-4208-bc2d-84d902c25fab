import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { SystemConfigService } from 'common/service/system-config.service';
import dayjs from 'dayjs';
import { Observable } from 'rxjs';
import { GlobalService } from 'common/service/global.service';
import { DtdDesignerViewService } from '../../../service/dtd-designer-view.service';

@Injectable()
export class ApiService {
  adesignerUrl: string;

  headerCofig = {};
  applicationCodeProxy: string;

  constructor(
    private http: HttpClient,
    private configService: SystemConfigService,
    public dtdDesignerViewService: DtdDesignerViewService,
  ) {
    this.configService.get('adesignerUrl').subscribe((url) => {
      // this.adesignerUrl = 'http://localhost:4201';
      this.adesignerUrl = url;
    });
  }

  /**
   * 获取画布数据
   * @param application
   * @param groupCode
   * @returns
   */
  fetchGraphData(application: string, groupCode: string = ''): Observable<any> {
    if (this.applicationCodeProxy) {
      application = this.applicationCodeProxy;
      groupCode = this.applicationCodeProxy;
    }

    return this.http.get(`${this.adesignerUrl}/athena-designer/task/getDtdCanvas`, {
      params: { application, groupCode },
      headers: Object.assign({}, this.headerCofig, this.dtdDesignerViewService.adpVersionHeaders),
    });
    // const url = '../../../assets/api/getDtdCanvas.json';
    // return this.http.get(url);
  }

  /**
   * 保存画布数据
   * @param param
   * @returns
   */
  saveGraphRelation(param: any): Observable<any> {
    let url = `${this.adesignerUrl}/athena-designer/task/saveStateRelation`;
    if (this.applicationCodeProxy) {
      url = `${this.adesignerUrl}/athena-designer/favourite/saveFavouriteContent`;
    }
    return this.http.post(url, param, {
      headers: this.dtdDesignerViewService.adpVersionHeaders,
    });
  }

  // 更新数据状态
  saveTaskDataState(param: any): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/task/saveTaskDataState`;
    return this.http.post(url, param, {
      headers: Object.assign({}, this.headerCofig, this.dtdDesignerViewService.adpVersionHeaders),
    });
  }

  // 保存默认状态
  saveDefaultState(param: any): Observable<any> {
    if (this.applicationCodeProxy) {
      param.dataDescription.application = this.applicationCodeProxy;
      param.dataStates.forEach((item) => (item.application = this.applicationCodeProxy));
    }
    const url = `${this.adesignerUrl}/athena-designer/data/saveDataDescriptionAndSingleState`;
    return this.http.post(url, param, {
      headers: Object.assign({}, this.headerCofig, this.dtdDesignerViewService.adpVersionHeaders),
    });
  }

  saveDataState(param: any): Observable<any> {
    if (this.applicationCodeProxy) {
      param.application = this.applicationCodeProxy;
    }
    const url = `${this.adesignerUrl}/athena-designer/data/saveDataState`;
    return this.http.post(url, param, { headers: this.headerCofig });
  }

  // 保存收藏
  saveFavourite(params) {
    const url = `${this.adesignerUrl}/athena-designer/favourite/saveFavourite`;
    return this.http.post(url, params);
  }

  /**
   * 查询是否跳过提示
   * @returns
   */
  guideIsSkip(type): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/guide/isSkip?type=${type}`;
    return this.http.get(url);
  }

  /**
   * 设置已看过
   * @param params
   * @returns
   */
  setSkip(params: { isSkip: boolean; type: string }): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/guide/skipSet`;
    return this.http.post(url, params);
  }

  dtdToolBatchSave(param: any): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/dtdTool/batchSave`;
    return this.http.post(url, param);
  }

  // dtd粘贴
  dtdToolPaste(param: any): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/dtdTool/paste`;
    return this.http.post(url, param, { headers: this.headerCofig });
  }

  // DTD完整性校验
  completeDTDValidate(param: any): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/dtd/completeDTDValidate`;
    return this.http.post(url, param, { headers: this.headerCofig });
  }

  // dtd收藏
  saveFavouriteDTD(param: any): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/favourite/saveFavouriteDTD`;
    return this.http.post(url, param);
  }

  /**
   * 获取任务列表
   * @param application
   * @param groupCode
   * @returns
   */
  fetchTasks(application: string, groupCode: string = ''): Observable<any> {
    return this.http.get(`${this.adesignerUrl}/athena-designer/task/getTaskList`, {
      params: { application, groupCode },
      headers: Object.assign({}, this.headerCofig, this.dtdDesignerViewService.adpVersionHeaders),
    });
  }

  /**
   * 获状态数据
   * @param application
   * @param groupCode
   * @returns
   */
  fetchDataList(application: string, groupCode: string = ''): Observable<any> {
    return this.http.get(`${this.adesignerUrl}/athena-designer/task/getDataList`, {
      params: { application, groupCode },
      headers: Object.assign({}, this.headerCofig, this.dtdDesignerViewService.adpVersionHeaders),
    });
  }

  postUpsertProcess(params: any): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/process/upsertProcess`;
    return this.http.post(
      url,
      { ...params },
      { headers: Object.assign({}, this.headerCofig, this.dtdDesignerViewService.adpVersionHeaders) },
    );
  }

  /**
   * 获取任务详情
   * @param code
   * @returns
   */
  queryTaskDetail(code): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/task/getTask/${code}`;
    return this.http.get(url, {
      headers: Object.assign({}, this.headerCofig, this.dtdDesignerViewService.adpVersionHeaders),
    });
  }
}
