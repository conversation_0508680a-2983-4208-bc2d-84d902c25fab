<nz-collapse>
  <nz-collapse-panel [nzHeader]="'dj-基本信息' | translate" [nzActive]="true" [nzExpandedIcon]="'caret-right'">
    <form nz-form nzLayout="vertical" [formGroup]="currentProject['basicForm']" [nzNoColon]="true">
      <!-- 租户 -->
      <nz-form-item *ngIf="currentProject.tenantId && currentProject.tenantId !== 'SYSTEM'">
        <nz-form-label nzRequired>{{ 'dj-租户' | translate }}</nz-form-label>
        <nz-form-control [nzErrorTip]="'dj-租户必填' | translate">
          <app-modal-input
            [innerLabel]="false"
            [attr]="{
              name: '租户',
              required: true
            }"
            [value]="currentProject['basicForm'].get('tenantId')?.value"
            (callBack)="handlePatch('tenantId', $event)"
            formControlName="tenantId"
            ngDefaultControl
          ></app-modal-input>
        </nz-form-control>
      </nz-form-item>

      <!-- 项目代号 -->
      <nz-form-item>
        <nz-form-label nzRequired>{{ 'dj-项目代号' | translate }}</nz-form-label>
        <nz-form-control [nzErrorTip]="userErrorTpl">
          <app-modal-input
            [innerLabel]="false"
            [attr]="{
              name: '项目代号',
              required: true,
              readOnly: modalFlag === 'edit'
            }"
            [value]="currentProject['basicForm'].get('code')?.value"
            (callBack)="handlePatch('code', $event)"
            formControlName="code"
            ngDefaultControl
          ></app-modal-input>
        </nz-form-control>
        <ng-template #userErrorTpl let-control>
          <ng-container *ngIf="control.hasError('required')">{{ 'dj-请输入' | translate }}</ng-container>
          <ng-container *ngIf="control.hasError('duplicated')">{{ 'dj-app-regular2' | translate }}</ng-container>
        </ng-template>
      </nz-form-item>

      <!-- 项目类型 -->
      <nz-form-item>
        <nz-form-label nzRequired>{{ 'dj-项目类型' | translate }}</nz-form-label>
        <nz-form-control [nzErrorTip]="'dj-请选择' | translate">
          <app-modal-select
            [innerLabel]="false"
            [attr]="{
              name: '项目类型',
              required: true,
              options: typeList,
              readOnly: !!isMainPro || !!isRoot || modalFlag === 'edit'
            }"
            [value]="currentProject['basicForm'].get('executeType')?.value"
            (callBack)="handlePatch('executeType', $event)"
            formControlName="executeType"
            ngDefaultControl
          >
          </app-modal-select>
        </nz-form-control>
      </nz-form-item>

      <!-- 项目名称 -->
      <nz-form-item>
        <nz-form-label nzRequired>{{ 'dj-项目名称' | translate }}</nz-form-label>
        <nz-form-control [nzErrorTip]="'dj-请输入' | translate">
          <app-modal-input
            formControlName="name"
            ngDefaultControl
            [innerLabel]="false"
            [attr]="{
              name: '项目名称',
              required: true,
              lang: currentProject['lang']?.name,
              needLang: true
            }"
            [value]="currentProject['lang']?.name?.[('dj-LANG' | translate)]"
            (callBack)="handlePatch('name', $event)"
          ></app-modal-input>
        </nz-form-control>
      </nz-form-item>

      <!-- 项目描述 -->
      <nz-form-item>
        <nz-form-label>{{ 'dj-项目描述' | translate }}</nz-form-label>
        <nz-form-control [nzErrorTip]="'dj-请输入' | translate">
          <app-modal-input
            [innerLabel]="false"
            [attr]="{
              name: '项目描述',
              required: false,
              lang: currentProject['lang']?.description,
              needLang: true
            }"
            [value]="currentProject['lang']?.description?.[('dj-LANG' | translate)]"
            (callBack)="handlePatch('description', $event)"
            formControlName="description"
            ngDefaultControl
          >
          </app-modal-input>
        </nz-form-control>
      </nz-form-item>

      <!-- 业务类型 -->
      <nz-form-item>
        <nz-form-label nzRequired>{{ 'dj-业务类型' | translate }}</nz-form-label>
        <nz-form-control [nzErrorTip]="'dj-请选择' | translate">
          <app-modal-select
            [innerLabel]="false"
            [attr]="{
              name: '业务类型',
              required: true,
              options: businessType
            }"
            [value]="currentProject['basicForm'].get('type')?.value"
            (callBack)="handlePatch('type', $event)"
            formControlName="type"
            ngDefaultControl
          ></app-modal-select>
        </nz-form-control>
      </nz-form-item>

      <!-- 业务模式 -->
      <nz-form-item *ngIf="isTraceable">
        <nz-form-label>{{ 'dj-业务模式' | translate }}</nz-form-label>
        <nz-form-control [nzErrorTip]="'dj-请选择' | translate">
          <app-modal-select
            [innerLabel]="false"
            [attr]="{
              name: '业务模式',
              required: false,
              options: patternData
            }"
            [value]="currentProject['basicForm'].get('pattern')?.value"
            (callBack)="handlePatch('pattern', $event)"
            formControlName="pattern"
            ngDefaultControl
          ></app-modal-select>
        </nz-form-control>
      </nz-form-item>

      <!-- 初始状态 -->
      <nz-form-item>
        <nz-form-label nzRequired>{{ 'dj-初始状态' | translate }}</nz-form-label>
        <nz-form-control [nzErrorTip]="'dj-请选择' | translate">
          <!-- 平台推过来的 主项目 开始结束都要禁掉 -->
          <!-- 平台推过来的 子项目 结束要禁掉 -->
          <app-select-data-status
            formControlName="init"
            ngDefaultControl
            [innerLabel]="false"
            [labelName]="'dj-初始状态'"
            [nzDisabled]="isReference && !currentProject['basicForm'].get('primaryProjectCode')?.value"
            [value]="currentProject['basicForm'].get('init')?.value"
            (update)="handleUpdate($event)"
            (callBack)="handlePatch('init', $event)"
          >
          </app-select-data-status>
        </nz-form-control>
      </nz-form-item>

      <!-- 结束状态 -->
      <nz-form-item>
        <nz-form-label nzRequired>{{ 'dj-结束状态' | translate }}</nz-form-label>
        <nz-form-control [nzErrorTip]="'dj-请选择' | translate">
          <app-select-data-status
            formControlName="end"
            ngDefaultControl
            [innerLabel]="false"
            [labelName]="'dj-结束状态'"
            [nzDisabled]="isReference"
            [value]="currentProject['basicForm'].get('end')?.value"
            (update)="handleUpdate($event)"
            (callBack)="handlePatch('end', $event)"
          >
          </app-select-data-status>
        </nz-form-control>
      </nz-form-item>

      <div class="form-divider"></div>

      <!-- 界面设计 -->
      <nz-form-item>
        <nz-form-label>{{ 'dj-项目基础设置' | translate }}</nz-form-label>
        <nz-form-control>
          <div class="designer-wrapper">
            <!-- <button ad-button adType="text" nzSize="large" (click)="handlePageDesign(false)">
              <i adIcon [iconfont]="'iconjiemiansheji'" aria-hidden="true" class="icon"></i
              >{{ 'dj-界面设计' | translate }}
            </button>
            <br />
            <button ad-button adType="text" nzSize="large" (click)="handlePageDesign(true)">
              <i adIcon [iconfont]="'iconshouji'" aria-hidden="true" class="icon"></i
              >{{ 'dj-mobile界面设计' | translate }}
            </button> -->

            <!-- 界面设计 -->
            <div>
              <a
                class="designer-link"
                ad-button
                adType="link"
                *ngIf="
                  currentProject['basicForm']?.get('executeType')?.value === 'mainline' &&
                  currentProject['basicForm']?.get('manualAble')?.value === true
                "
                (click)="handlePageDesign(currentProject)"
              >
                <i
                  adIcon
                  iconfont="iconjiemiansheji"
                  aria-hidden="true"
                  class="iconfont"
                  nz-popconfirm
                  nzPopconfirmPlacement="bottom"
                >
                </i
                ><span>{{ 'dj-界面设计' | translate }}</span>
              </a>
            </div>

            <!-- mobile界面设计 -->
            <div>
              <a
                class="designer-link"
                ad-button
                adType="link"
                *ngIf="
                  currentProject['basicForm']?.get('executeType')?.value === 'mainline' &&
                  currentProject['basicForm']?.get('manualAble')?.value === true
                "
                (click)="handleMobilePageDesign(currentProject)"
              >
                <i
                  adIcon
                  type="mobile"
                  aria-hidden="true"
                  class="iconfont"
                  nz-popconfirm
                  nzPopconfirmPlacement="bottom"
                >
                </i
                ><span>{{ 'dj-mobile界面设计' | translate }}</span>
              </a>
            </div>

            <div
              *ngIf="
                currentProject['basicForm']?.get('executeType')?.value === 'user' &&
                currentProject['basicForm'].get('pattern')?.value === 'BUSINESS'
              "
            >
              <a class="designer-link" ad-button adType="link" (click)="handleProjectCustomize()">
                <i class="iconfont" adIcon iconfont="iconjiemiansheji" aria-hidden="true"></i>
                <span>{{ 'dj-项目卡自定义' | translate }}</span>
              </a>
            </div>

            <!-- 扩展信息 -->
            <div>
              <a class="designer-link" ad-button adType="link">
                <app-extended-info
                  class="extended-info"
                  [code]="currentProject.meta.code"
                  [sceneCode]="'datamapProject'"
                  [showName]="true"
                  [extendHeader]="dtdDesignerViewService.adpVersionHeaders"
                  [appCode]="appService.selectedApp.code"
                ></app-extended-info>
              </a>
            </div>

            <!-- 个案 -->
            <ng-container *operateAuth="{ guards: [!isReference] }">
              <div>
                <a
                  class="designer-link"
                  ad-button
                  adType="link"
                  *ngIf="currentProject['basicForm']?.get('executeType')?.value === 'mainline'"
                  (click)="handleCase(currentProject)"
                >
                  <i adIcon type="copy" theme="outline" class="iconfont"></i>
                  <span>{{ 'dj-个案' | translate }}</span>
                </a>
              </div>
            </ng-container>
          </div>
        </nz-form-control>
      </nz-form-item>

      <div class="form-divider"></div>

      <ng-container *ngIf="isTraceable; else mainlineTpl">
        <!-- 来源实体 -->
        <nz-form-item>
          <nz-form-label nzRequired>{{ 'dj-来源实体' | translate }}</nz-form-label>
          <nz-form-control [nzErrorTip]="'dj-请输入' | translate">
            <app-modal-input
              [innerLabel]="false"
              [attr]="{ name: '来源实体名称', required: true }"
              [value]="currentProject['basicForm']?.get('sourceEntityName')?.value"
              (callBack)="handlePatch('sourceEntityName', $event)"
              formControlName="sourceEntityName"
              ngDefaultControl
            >
            </app-modal-input>
          </nz-form-control>
        </nz-form-item>

        <!-- 来源名称 -->
        <nz-form-item>
          <nz-form-label nzRequired>{{ 'dj-来源名称' | translate }}</nz-form-label>
          <nz-form-control [nzErrorTip]="'dj-请输入' | translate">
            <app-modal-input
              [innerLabel]="false"
              [attr]="{
                name: '来源名称',
                required: true,
                lang: currentProject['lang']?.sourceName,
                needLang: true
              }"
              [value]="currentProject['lang']?.sourceName?.[('dj-LANG' | translate)]"
              (callBack)="handlePatch('sourceName', $event)"
              formControlName="sourceName"
              ngDefaultControl
            >
            </app-modal-input>
          </nz-form-control>
        </nz-form-item>

        <!-- 预计完成日 -->
        <nz-form-item>
          <nz-form-label nzRequired>{{ 'dj-预计完成日' | translate }}</nz-form-label>
          <nz-form-control [nzErrorTip]="'dj-请输入' | translate">
            <app-modal-input
              [innerLabel]="false"
              [attr]="{
                name: '预计完成日',
                required: true,
                lang: currentProject['lang']?.dueDateName,
                needLang: true
              }"
              [value]="currentProject['lang']?.dueDateName?.[('dj-LANG' | translate)]"
              (callBack)="handlePatch('dueDateName', $event)"
              formControlName="dueDateName"
              ngDefaultControl
            >
            </app-modal-input>
          </nz-form-control>
        </nz-form-item>

        <!-- 目标代号 -->
        <nz-form-item>
          <nz-form-label>{{ 'dj-目标代号' | translate }}</nz-form-label>
          <nz-form-control>
            <app-modal-input
              [innerLabel]="false"
              [attr]="{ name: '目标代号' }"
              [value]="currentProject['basicForm']?.get('targetCode')?.value"
              (callBack)="handlePatch('targetCode', $event)"
              formControlName="targetCode"
              ngDefaultControl
            >
            </app-modal-input>
          </nz-form-control>
        </nz-form-item>

        <!-- 目标名称 -->
        <nz-form-item>
          <nz-form-label>{{ 'dj-目标名称' | translate }}</nz-form-label>
          <nz-form-control>
            <app-modal-input
              [innerLabel]="false"
              [attr]="{
                name: '目标名称',
                lang: currentProject['targetLang']?.name,
                needLang: true
              }"
              [value]="currentProject['targetLang']?.name?.[('dj-LANG' | translate)]"
              (callBack)="handlePatch('targetName', $event)"
              formControlName="targetName"
              ngDefaultControl
            >
            </app-modal-input>
          </nz-form-control>
        </nz-form-item>

        <!-- 起始日期 -->
        <nz-form-item *ngIf="currentProject['basicForm']?.get('dueDateType')?.value === 'MULTI_DAY'">
          <nz-form-label>{{ 'dj-起始日期' | translate }}</nz-form-label>
          <nz-form-control>
            <ad-date-picker
              [innerLabel]="false"
              ngDefaultControl
              formControlName="startDate"
              nzFormat="yyyy-MM-dd"
              [nzPlaceHolder]="'dj-起始日期' | translate"
            >
            </ad-date-picker>
          </nz-form-control>
        </nz-form-item>

        <!-- 天数间隔 -->
        <nz-form-item *ngIf="currentProject['basicForm']?.get('dueDateType')?.value === 'MULTI_DAY'">
          <nz-form-label>{{ 'dj-天数间隔' | translate }}</nz-form-label>
          <nz-form-control>
            <app-modal-input
              [innerLabel]="false"
              [attr]="{ name: '天数间隔' }"
              [value]="currentProject['basicForm'].get('count')?.value"
              (callBack)="handlePatch('count', $event)"
              formControlName="count"
              ngDefaultControl
            ></app-modal-input>
          </nz-form-control>
        </nz-form-item>
      </ng-container>

      <ng-template #mainlineTpl>
        <nz-form-item>
          <nz-form-control>
            <span #manualRef class="check-label" [ngStyle]="{ marginLeft: '10px' }">{{
              'dj-手动发起' | translate
            }}</span>
            <label nz-checkbox formControlName="manualAble"></label>
          </nz-form-control>
        </nz-form-item>

        <nz-form-item *ngIf="currentProject['basicForm']?.get('manualAble')?.value">
          <nz-form-control>
            <span class="check-label" [ngStyle]="{ marginLeft: '10px' }">{{ 'dj-权限控制' | translate }}</span>
            <label nz-checkbox formControlName="authorityPrefix"></label>
          </nz-form-control>
        </nz-form-item>
      </ng-template>
    </form>
  </nz-collapse-panel>
</nz-collapse>

<!--个案开窗-->
<app-case-modal
  *ngIf="caseModalVisible"
  #caseModalRef
  [caseData]="caseData"
  [caseModal]="caseModalVisible"
  (saveModal)="saveCase($event)"
  (closeModal)="caseModalVisible = false"
>
</app-case-modal>

<!--手工发起项目界面设计器-->
<app-project-work-design
  *ngIf="workVisible && workData?.data?._pageModel === 'pageView'"
  [workVisible]="workVisible"
  [workData]="workData"
  [descriptionLang]="descriptionLang"
  (close)="handlePageClose()"
>
</app-project-work-design>

<!-- 新的手工发起项目界面设计器 -->
<app-project-work-design-new
  *ngIf="!isMobile && workVisible && workData?.data?._pageModel === 'dsl'"
  [workVisible]="workVisible"
  [workData]="workData"
  [extendHeader]="dtdDesignerViewService.adpVersionHeaders"
  [descriptionLang]="descriptionLang"
  (close)="handlePageClose()"
  (refresh)="reload.emit()"
>
</app-project-work-design-new>

<app-project-work-design-new-mobile
  *ngIf="isMobile && workVisible && workData?.data?._pageModel === 'dsl'"
  [workVisible]="workVisible"
  [workData]="workData"
  [descriptionLang]="descriptionLang"
  [extendHeader]="dtdDesignerViewService.adpVersionHeaders"
  (close)="handlePageClose()"
>
</app-project-work-design-new-mobile>

<!-- 项目自定义 -->
<app-task-project-card-design
  *ngIf="cardJsonVisible"
  [type]="'project'"
  [workVisible]="cardJsonVisible"
  [workData]="workData"
  [extendHeader]="dtdDesignerViewService.adpVersionHeaders"
  (save)="handleJsonSave()"
  (close)="handleJsonModalCancel()"
></app-task-project-card-design>
