import { Component, EventEmitter, Input, OnInit, Output, ViewChildren } from '@angular/core';
import { AppService } from 'pages/apps/app.service';
import { ApiService } from './service/api.service';
import { to } from 'common/utils/core.utils';
import { DataStateService } from './service/data-state.service';
import { GraphComponent } from '../graph/graph.component';
import { DataStateListItemComponent } from './data-state-list-item/data-state-list-item.component';
import { TranslateService } from '@ngx-translate/core';
import { cloneDeep, isBoolean } from 'lodash';
import { NzMessageService } from 'ng-zorro-antd/message';
import { DriveExecutionState } from 'common/implements/drive-execution';
import { AdModalService } from 'components/ad-ui-components/ad-modal/ad-modal.service';
import { DtdDesignerComponent } from '../dtd-designer.component';
import { DtdDesignerViewService } from '../../service/dtd-designer-view.service';

@Component({
  selector: 'dtd-designer-data-state-list',
  exportAs: 'driveExecutionDataState',
  templateUrl: './data-state-list.component.html',
  styleUrls: ['./data-state-list.component.less'],
})
export class DataStateListComponent implements OnInit, DriveExecutionState {
  @Input() graphComponentRef: GraphComponent;
  @Input() dtdDesignerRef: DtdDesignerComponent;
  @Output() afterDeleted = new EventEmitter();
  @ViewChildren('listItem') listItemRefs: DataStateListItemComponent[];

  loading: boolean = true;
  selectedDataState: any;
  originDataStateList: any[] = []; // 原始列表
  dataStateList: any[] = []; // 搜索后的列表
  dataStateMap: any = new Map(); // 所有数据映射表，注意：是dataStateList状态
  dataStateModalVisible: boolean = false;
  dataStateModalTitle: string; // 数据开窗标题
  dataStateModalData: any; // 传入数据状态开窗的数据
  dataStateModalType: 'ADD' | 'EDIT'; // 新增/编辑数据状态
  validMap: Map<string, boolean> = new Map(); // 存放状态校验结果

  // graph组件的 stateNodeMap 属性
  get graphStateNodeMap() {
    return this.graphComponentRef?.stateNodeMap;
  }

  get noData() {
    return !this.loading && !this.dataStateList?.length;
  }

  constructor(
    private appService: AppService,
    private apiService: ApiService,
    private dataStateService: DataStateService,
    private translateService: TranslateService,
    private modal: AdModalService,
    private message: NzMessageService,
    private dtdDesignerViewService: DtdDesignerViewService,
  ) {}

  ngOnInit() {
    // 获取数据列表
    // this.fetchDataList();
  }

  initPage() {
    // 获取数据列表
    this.fetchDataList();
  }

  async fetchDataList() {
    const { code, groupCode } = this.appService.selectedApp;
    const [err, res] = await to(this.apiService.fetchDataList(code, groupCode).toPromise());
    if (res?.code === 0) {
      this.originDataStateList = res.data;
      // 缓存状态映射，取res.data.dataStateList
      res.data.forEach((i) => i.dataStateList.forEach((state) => this.dataStateMap.set(state.code, state)));
      this.handleSearch('', false);
    }
    this.loading = false;
  }

  /**
   * 搜索列表
   * @param searchValue 搜索词
   * @param isShowAll 是否展开所有节点
   */
  handleSearch(searchValue: string, isShowAll: boolean = true): void {
    const searchValueTrim = searchValue.trim();
    this.dataStateList = this.dataStateService.filterList(searchValueTrim, this.originDataStateList);

    if (isShowAll) {
      // 展开节点
      const t = setTimeout(() => {
        clearTimeout(t);
        this.listItemRefs.forEach((c: DataStateListItemComponent) => (c.childrenVisible = true));
      }, 10);
    }
  }

  /**
   * 点击新增数据状态
   */
  handleAddClick(): void {
    this.dataStateModalTitle = this.translateService.instant('dj-新增数据');
    this.dataStateModalData = null;
    this.dataStateModalType = 'ADD';
    this.dataStateModalVisible = true;
  }

  /**
   * 开窗编辑数据状态
   * @param dataStateData
   */
  handleEdit(dataStateData: any): void {
    this.dataStateModalTitle = this.translateService.instant('dj-编辑');
    this.dataStateModalData = cloneDeep(dataStateData);
    this.dataStateModalType = 'EDIT';
    this.dataStateModalVisible = true;
  }

  /**
   * 选中、拖拽状态节点
   * @param dataState
   */
  async handleMouseDown({ e, isInGraph, itemData }) {
    // 属性面板有修改时进行拦截，不允许切换
    const canPass = await this.dtdDesignerViewService.checkPropsPanelCanContinue(true);
    if (!canPass) return;

    this.handleListItemClick(itemData);

    // 开属性面板
    this.dtdDesignerRef?.handleOpenPanel({ ...itemData, type: 'datastate' });

    if (isInGraph) return;
    if (isBoolean(canPass) && canPass) {
      this.graphComponentRef?.startDrag(e, itemData, 'datastate');
    }
  }

  /**
   * 点击列表中的状态
   */
  handleListItemClick(dataState) {
    this.selectedDataState = dataState;
    if (this.graphComponentRef) {
      // 选中画布上的节点
      this.graphComponentRef?.selectNodeByCode(dataState.code);
    }
  }

  /**
   * 数据状态开窗点确定后回调
   * @param data
   * data.param 保存时的表单数据
   * data.type 保存类型，ADD/EDIT
   * data.beforeData 编辑前的数据
   * *对新增的状态需在画布中间添加状态节点，编辑的状态需更新状态节点名称
   */
  async afterDataStateModalSave(info) {
    this.dataStateModalVisible = false;
    await this.fetchDataList();
    // 同步画布中的状态节点
    this.dataStateService.syncStateNode(info, this.graphComponentRef, this.dtdDesignerRef);
  }

  /**
   * 根据code设置selectedDataState
   * @param code
   */
  public setSelectedDataState(code: string) {
    this.selectedDataState = this.dataStateMap.get(code);
  }

  /**
   * 删除状态
   * @param itemData
   */
  handleDelete(itemData: any): void {
    let loading = false;
    const { dataStateList, dataDescription } = itemData;
    this.modal.confirm({
      nzTitle: this.translateService.instant('dj-确认删除某数据？', { name: dataDescription.name }),
      nzWrapClassName: 'vertical-center-modal',
      nzOkText: this.translateService.instant('dj-确定'),
      nzOkLoading: loading,
      nzOnOk: async () => {
        const [err, res] = await to(this.apiService.deleteDataDescriptionByCode(dataDescription.code).toPromise());
        if (res?.code === 0) {
          this.message.success(this.translateService.instant('dj-删除成功！'));
          // 从画布中删除状态节点
          this.graphComponentRef && this.dataStateService.removeStateNodes(dataStateList, this.graphComponentRef);
          // 保存画布 无需提示
          this.graphComponentRef?.saveGraph(false);
          // 刷新列表
          this.fetchDataList();
          this.afterDeleted.emit({ type: 'datastate', code: dataDescription.code, data: itemData });
        }
        loading = false;
      },
    });
  }

  /**
   * 设置任务是否验证通过
   * 供外使用
   * @param taskCode
   */
  public setDataStateValid(code, valid) {
    if (valid) {
      this.validMap.delete(code);
    } else {
      this.validMap.set(code, this.dataStateMap.get(code));
    }
  }
}
