<ad-select
  style="margin-top: 5px; width: 100%"
  nzShowSearch
  nzAllowClear
  #nzSelectRef
  [nzDisabled]="nzDisabled"
  [(ngModel)]="value"
  [nzPlaceHolder]="innerLabel ? (hasValue ? (labelName | translate) : '') : (labelName | translate)"
  [nzDropdownRender]="renderTemplate"
  (ngModelChange)="handleChange($event)"
  (nzBlur)="handleBlur()"
  (nzFocus)="handleFocus()"
>
  <ad-option
    *ngFor="let option of optionList; trackBy: trackByValue"
    [nzValue]="option.value"
    [nzLabel]="option.label"
  ></ad-option>
</ad-select>
<ng-template #renderTemplate>
  <nz-divider style="margin: 12px 0"></nz-divider>
  <button
    ad-button
    nzSize="large"
    adType="text"
    (click)="handleAdd()"
    style="font-size: 14px; width: 210px; text-align: left"
  >
    <i adIcon iconfont="icondianji" aria-hidden="true" class="iconfont"></i>
    {{ 'dj-新增数据状态' | translate }}
  </button>
</ng-template>
<div
  [class]="hasValue ? 'select-item-h-label' : 'select-item-h-placeholder'"
  [ngClass]="{
    'label-hidden': !innerLabel
  }"
  (click)="handleLabelClick()"
>
  <span *ngIf="isRequired" style="color: red">*</span>
  {{ labelName | translate }}
</div>

<ad-modal
  [(nzVisible)]="showModal"
  [nzTitle]="'dj-新增数据' | translate"
  [nzWidth]="'520px'"
  [nzClosable]="true"
  (nzOnCancel)="showModal = false"
  [nzMaskClosable]="false"
  [nzFooter]="null"
>
  <ng-container *adModalContent>
    <app-add-data
      (afterSave)="handleAddDataModal()"
      [type]="'ADD'"
      [effectAdpVersion]="effectAdpVersion"
      (cancel)="showModal = false"
    >
    </app-add-data>
  </ng-container>
</ad-modal>
