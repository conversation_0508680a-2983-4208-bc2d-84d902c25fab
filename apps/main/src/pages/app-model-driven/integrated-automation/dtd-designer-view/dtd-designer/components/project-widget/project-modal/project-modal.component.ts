import {
  AfterViewChecked,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  OnInit,
  Output,
  ViewChild,
} from '@angular/core';
import { AppService } from 'pages/apps/app.service';
import { TranslateService } from '@ngx-translate/core';
import { NzMessageService } from 'ng-zorro-antd/message';
import { processType, policyType } from '../project-set';
import * as dayjs from 'dayjs';
import { isFunction } from 'common/utils/core.utils';
import { DataDetectionStepMilepostComponent } from '../step-milepost/step-milepost.component';
import { DtdService } from 'pages/app/dtd/dtd.service';
import { ProjectSetService } from '../service/project-set.service';
import { size } from 'lodash';
import { DtdDesignerViewService } from 'pages/app-model-driven/integrated-automation/dtd-designer-view/service/dtd-designer-view.service';

@Component({
  selector: 'data-detection-project-modal',
  templateUrl: './project-modal.component.html',
  styleUrls: ['./project-modal.component.less'],
})
export class DataDetectionProjectModalComponent implements OnInit, AfterViewChecked {
  @ViewChild('stepMilepost') stepMilepost: DataDetectionStepMilepostComponent;

  processType = processType; // 任务处理方式（当责者）
  policyType = policyType; // 定义人员选择策略
  performerType: any; // 用户类型（执行人）
  anchorContainer: any;

  historyModalProps: HistoryModalProps = {
    transferModal: false,
    code: '',
    collection: 'project',
  };

  @Input() extra: any;
  @Input() projectLoading: boolean;
  @Input() modalFlag: any;
  @Input() isMainPro: any;
  @Input() activeControl: any;
  @Input() currentProject: any;
  @Input() isRoot: any;
  @Output() modalClose: EventEmitter<any> = new EventEmitter();

  isShowJsonModal = false;
  defaultAssignConfig = {};

  constructor(
    private translateService: TranslateService,
    private message: NzMessageService,
    private appService: AppService,
    private cdr: ChangeDetectorRef,
    private projectSetService: ProjectSetService,
    public dtdService: DtdService,
    public dtdDesignerViewService: DtdDesignerViewService,
  ) {}

  ngAfterViewChecked(): void {
    this.cdr.detectChanges();
  }

  ngOnInit(): void {
    this.performerType = [
      { value: 'user', label: this.translateService.instant('dj-用户') },
      { value: 'duty', label: this.translateService.instant('dj-职能') },
      {
        value: 'deptDirector',
        label: this.translateService.instant('dj-部门主管'),
      },
      {
        value: 'deptUser',
        label: this.translateService.instant('dj-部门人员'),
      },
      {
        value: 'personInCharge',
        label: this.translateService.instant('dj-当责者'),
      },
      { value: 'activity', label: this.translateService.instant('dj-活动') },
    ];
    // 如果数据中 没有assignConfig 对象 使用assignConfig.assignAble默认值 false (兼容平台保持一致)，有则默认值是true
    // 没有assignConfig字段的（迁移数据） 和有assignConfig且没有action字段的 给默认值
    if (!this.currentProject?.assignConfig) {
      this.currentProject.assignConfig = Object.assign({ assignAble: false }, this.defaultAssignConfig);
    } else {
      if ([undefined, null, ''].includes(this.currentProject?.assignConfig?.['assignAble'])) {
        this.currentProject.assignConfig.assignAble = true;
      }
      if (!this.currentProject?.assignConfig.action) {
        this.currentProject.assignConfig = Object.assign(
          {},
          this.currentProject.assignConfig,
          this.defaultAssignConfig,
        );
      }
    }
  }

  ngAfterViewInit(): void {
    this.anchorContainer = document.getElementById('project-add-sub-modal');
  }

  // 获取按钮是否可点击
  handleValid(): boolean {
    let validate = true;
    if (this.currentProject.basicForm.invalid) {
      validate = false;
    }
    if (this.currentProject.milestone) {
      for (const m of this.currentProject.phases || []) {
        if (m.invalid) {
          validate = false;
          break;
        }
      }
    }
    for (const s of this.currentProject.atmcDatas || []) {
      const { proVarKey, athenaKey } = s;
      if ([undefined, null, ''].includes(proVarKey) || [undefined, null, ''].includes(athenaKey)) {
        validate = false;
        break;
      }
    }
    for (const s of this.currentProject.presetVariables || []) {
      const { performerName, performerValue } = s;
      if ([undefined, null, ''].includes(performerName) || [undefined, null, ''].includes(performerValue)) {
        validate = false;
        break;
      }
    }
    const executor = this.currentProject.executor;
    if (executor) {
      const source = executor?.source;
      // 使用了新的当责者
      if (executor.type !== 'default') {
        if (source === 'personnel') {
          if (size(executor.personnel) === 0) {
            validate = false;
          }
        } else if (source === 'variable') {
          if (size(executor.variable) === 0) {
            validate = false;
          }
        } else if (source === 'activity') {
          if (size(executor.activity) === 0) {
            validate = false;
          }
        } else if (source === 'formFiled') {
          if (size(executor.formFiled) === 0) {
            validate = false;
          }
        } else if (source === 'department') {
          if (size(executor.department) === 0) {
            validate = false;
          }
        }
      }
    } else {
      const { choosePolicy: policy, identities = [] } = this.currentProject.personInCharge;
      if ([undefined, null, ''].includes(policy)) {
        validate = false;
      }
      if (validate) {
        for (const s of identities) {
          const { isBusiness, performerType, performerName, performerVariable, performerValue } = s;
          if (
            [undefined, null, ''].includes(performerType) ||
            [undefined, null, ''].includes(performerName) ||
            [undefined, null, ''].includes(performerValue) ||
            (!!isBusiness && [undefined, null, ''].includes(performerVariable))
          ) {
            validate = false;
            break;
          }
        }
      }
    }

    const { actionParams = [] } = this.currentProject.sourceDetailAction;
    for (const s of actionParams) {
      const { name, type, value } = s;
      if (
        [undefined, null, ''].includes(name) ||
        [undefined, null, ''].includes(type) ||
        [undefined, null, ''].includes(value)
      ) {
        validate = false;
        break;
      }
    }
    return validate;
  }

  // 校验
  handleValidate(): boolean {
    let validate = true;
    // basic
    for (const i of Object.keys(this.currentProject.basicForm?.controls)) {
      this.currentProject.basicForm.controls[i].markAsDirty();
      this.currentProject.basicForm.controls[i].updateValueAndValidity();
    }
    if (this.currentProject.basicForm.invalid) {
      validate = false;
    }
    // phases
    if (this.currentProject.milestone) {
      for (const m of this.currentProject.phases || []) {
        if (m.invalid) {
          validate = false;
          break;
        }
      }
    }
    // push
    this.currentProject.atmcDatas.forEach((s) => {
      const { proVarKey, athenaKey } = s;
      s['errorKeys'] = [];
      if ([undefined, null, ''].includes(proVarKey)) {
        s['errorKeys'].push('proVarKey');
        validate = false;
      }
      if ([undefined, null, ''].includes(athenaKey)) {
        s['errorKeys'].push('athenaKey');
        validate = false;
      }
    });
    // charge
    const executor = this.currentProject.executor;
    if (executor) {
      const source = executor?.source;
      // 使用了新的当责者
      if (executor.type !== 'default') {
        if (source === 'personnel') {
          if (size(executor.personnel) === 0) {
            validate = false;
          }
        } else if (source === 'variable') {
          if (size(executor.variable) === 0) {
            validate = false;
          }
        } else if (source === 'activity') {
          if (size(executor.activity) === 0) {
            validate = false;
          }
        } else if (source === 'formFiled') {
          if (size(executor.formFiled) === 0) {
            validate = false;
          }
        } else if (source === 'department') {
          if (size(executor.department) === 0) {
            validate = false;
          }
        }
      }
    } else {
      const { choosePolicy: policy, identities = [] } = this.currentProject.personInCharge;
      this.currentProject.personInCharge['errorKeys'] = '';
      if ([undefined, null, ''].includes(policy)) {
        validate = false;
        this.currentProject.personInCharge['errorKeys'] = 'choosePolicy';
      }
      identities.forEach((s) => {
        const { isBusiness, performerType, performerName, performerVariable, performerValue } = s;
        s['errorKeys'] = [];
        if ([undefined, null, ''].includes(performerType)) {
          s['errorKeys'].push('performerType');
          validate = false;
        }
        if ([undefined, null, ''].includes(performerName)) {
          s['errorKeys'].push('performerName');
          validate = false;
        }
        if ([undefined, null, ''].includes(performerValue)) {
          s['errorKeys'].push('performerValue');
          validate = false;
        }
        if (!!isBusiness) {
          if ([undefined, null, ''].includes(performerVariable)) {
            s['errorKeys'].push('performerVariable');
            validate = false;
          }
        }
      });
    }

    // source
    const { actionParams = [] } = this.currentProject.sourceDetailAction;
    actionParams.forEach((s) => {
      const { name, type, value } = s;
      s['errorKeys'] = [];
      if ([undefined, null, ''].includes(name)) {
        s['errorKeys'].push('name');
        validate = false;
      }
      if ([undefined, null, ''].includes(type)) {
        s['errorKeys'].push('type');
        validate = false;
      }
      if ([undefined, null, ''].includes(value)) {
        s['errorKeys'].push('value');
        validate = false;
      }
    });
    return validate;
  }

  initOrEndChanged(): void {
    if (this.currentProject.basicForm.get('init').value && this.currentProject.basicForm.get('end').value) {
      this.stepMilepost?.loadTasksByDataState();
    }
  }

  // 保存
  handleSaveProject(): void {
    this.currentProject.formDirty = true;
    if (!this.handleValidate()) {
      return;
    }
    const flag = this.modalFlag;
    const {
      basicForm,
      atmcDatas = [],
      sourceDetailAction = {},
      personInCharge = {},
      presetVariables = [],
      assignConfig = {},
      executor,
      lang,
      targetLang,
    } = this.currentProject;
    const { targetName, targetCode, primaryProjectCode, dueDateType, startDate, count, ...param } =
      basicForm.getRawValue();
    const init = this.extra.allDataStatus.find((s) => s.code === param['init']);
    param['init'] = {
      code: init?.code,
      dataCode: init?.dataCode,
    };
    const end = this.extra.allDataStatus.find((s) => s.code === param['end']);
    param['end'] = {
      code: end?.code,
      dataCode: end?.dataCode,
    };
    if (param['executeType'] !== 'mainline') {
      param['primaryProjectCode'] = primaryProjectCode;
    }
    param['lang'] = lang;
    param['target'] = {
      name: targetName,
      code: targetCode,
      lang: targetLang,
    };
    param['dueDateTimeDistance'] = {
      type: dueDateType,
    };
    if (dueDateType === 'MULTI_DAY') {
      param['dueDateTimeDistance']['startDate'] = dayjs(startDate).format('YYYY-MM-DD');
      param['dueDateTimeDistance']['count'] = count;
    }
    if (dueDateType === 'WEEK_DAY') {
      param['dueDateTimeDistance']['timeDistanceBegin'] = 1;
      param['dueDateTimeDistance']['timeDistanceEnd'] = 7;
      param['dueDateTimeDistance']['interval'] = 'WEEK';
    }
    if (dueDateType === 'DAY') {
      param['dueDateTimeDistance']['timeDistanceBegin'] = 1;
      param['dueDateTimeDistance']['timeDistanceEnd'] = 1;
      param['dueDateTimeDistance']['interval'] = 'DAY';
    }
    if (!dueDateType) param['dueDateTimeDistance'] = null;
    param['milestone'] = this.currentProject.milestone;
    param['phases'] = this.currentProject.phases.map((one) => {
      // eslint-disable-next-line no-shadow
      const { code, name, taskCodes, lang } = isFunction(one?.getRawValue) ? one?.getRawValue() : one;
      return { code, name, taskCodes, lang };
    });
    param['application'] = this.appService?.selectedApp?.code;
    param['atmcDatas'] = atmcDatas;
    if (!!sourceDetailAction['actionId']) {
      param['sourceDetailAction'] = {
        actionId: sourceDetailAction['actionId'] || '',
        actionName: sourceDetailAction['actionName'] || '',
        // type: sourceDetailAction['type'] || '',
        actionParams: sourceDetailAction['actionParams'] || [],
      };
    }
    if (executor) {
      param['executor'] = executor;
    } else {
      param['personInCharge'] = {
        choosePolicy: personInCharge?.choosePolicy,
        processType: personInCharge?.processType,
        identities: personInCharge?.identities?.map((d) => {
          const { isBusiness, performerVariable, ...others } = d;
          const temp = {
            isBusiness,
            ...others,
          };
          if (isBusiness) {
            temp['performerVariable'] = performerVariable;
          }
          return temp;
        }),
      };
    }

    param['presetVariables'] = presetVariables;
    param['assignConfig'] = assignConfig;

    if (this.currentProject.pageCode) {
      param['pageCode'] = this.currentProject.pageCode;
    }
    this.projectLoading = true;
    // 添加组合入参
    param.projectFrom = 'combination';
    if (this.dtdDesignerViewService.adpVersionHeaders) {
      param['adpVersionHeaders'] = this.dtdDesignerViewService.adpVersionHeaders;
    }
    this.dtdService.saveProject(flag, param).subscribe(
      (res) => {
        if (res.code === 0) {
          this.projectLoading = false;
          this.message.success(this.translateService.instant('dj-保存成功！'));
          this.modalClose.emit({ data: param, flag });
          this.projectSetService.projectUpdate$.next(param.code);
        }
      },
      () => {
        this.projectLoading = false;
      },
    );
  }

  handleAssignAbleChange() {
    if (this.currentProject.assignConfig.assignAble == true) {
      this.isShowJsonModal = true;
    }
  }

  handleClickLabelJson() {
    this.isShowJsonModal = true;
  }

  // 更新json
  handleSaveSet(data) {
    this.isShowJsonModal = false;
    if (!this.currentProject.assignConfig) {
      this.currentProject.assignConfig = {};
    }
    this.currentProject.assignConfig = data || {};
  }

  openModifyHistoryModal() {
    this.historyModalProps.code = this.currentProject['basicForm'].get('code')?.value;
    this.historyModalProps.transferModal = true;
  }
}
