<div class="exception">
  <div class="group-fields">
    <form [formGroup]="dataFormGroup">
      <nz-form-item class="nz-form-item-content">
        <nz-form-control>
          <div class="form-item">
            <div class="item-title">
              {{ 'dj-方案字段' | translate }}
              <!-- <span class="item-required">*</span> -->
            </div>
            <app-modal-input
              formControlName="groupField"
              ngDefaultControl
              [attr]="{ name: '方案字段' }"
              [value]="dataFormGroup.get('groupField')?.value"
              [innerLabel]="false"
              [nzDisabled]="isFromDtdReference"
            >
            </app-modal-input>
          </div>
        </nz-form-control>
      </nz-form-item>

      <div formArrayName="resolvePlans" *ngIf="this.dataFormGroup.get('resolvePlans')?.controls.length">
        <div
          *ngFor="let item of dataFormGroup.get('resolvePlans').controls; let i = index"
          class="except-item"
          [formGroupName]="i"
        >
          <i class="delete" nz-icon nzType="minus-circle" nzTheme="fill" (click)="handleDeleteException(i)"></i>
          <nz-form-item class="nz-form-item-content">
            <nz-form-control [nzAutoTips]="errorTips">
              <div class="except" *ngIf="item.get('planId')">
                <div class="item-title">
                  {{ 'dj-处理方式' | translate }}
                  <span class="item-required">*</span>
                </div>
                <app-modal-input
                  [attr]="{
                    name: '处理方式',
                    required: true,
                    needLang: false
                  }"
                  style="width: 100%"
                  formControlName="planId"
                  [value]="item.get('planId')?.value"
                  ngDefaultControl
                  [innerLabel]="false"
                  [nzDisabled]="isFromDtdReference"
                  (callBack)="handlePlansData(i, 'planId', $event)"
                >
                </app-modal-input>
              </div>
            </nz-form-control>
          </nz-form-item>
          <nz-form-item class="nz-form-item-content">
            <nz-form-control [nzAutoTips]="errorTips">
              <div class="except" *ngIf="item.get('planName')">
                <div class="item-title">
                  {{ 'dj-方式名称' | translate }}
                  <span class="item-required">*</span>
                </div>
                <app-modal-input
                  formControlName="planName"
                  [attr]="{
                    name: '方式名称',
                    required: true,
                    needLang: true,
                    lang: item.get('lang')?.value?.planName
                  }"
                  [value]="item.get('planName')?.value"
                  ngDefaultControl
                  [innerLabel]="false"
                  [nzDisabled]="isFromDtdReference"
                  (callBack)="handlePlansData(i, 'planName', $event)"
                  (translateLoading)="handleTranslateLoading($event)"
                >
                </app-modal-input>
              </div>
            </nz-form-control>
          </nz-form-item>
          <nz-form-item class="nz-form-item-content" *ngIf="currentTask.type === 'solve'">
            <nz-form-control>
              <div class="except">
                <div class="item-title">
                  {{ 'dj-项目代号' | translate }}
                  <span class="item-required">*</span>
                </div>
                <app-modal-input
                  formControlName="projectCode"
                  [attr]="{
                    name: '项目代号',
                    required: currentTask.type === 'solve' ? true : false
                  }"
                  [value]="item.get('projectCode')?.value"
                  ngDefaultControl
                  [innerLabel]="false"
                  [nzDisabled]="isFromDtdReference"
                  (callBack)="handlePlansData(i, 'projectCode', $event)"
                >
                </app-modal-input>
              </div>
            </nz-form-control>
          </nz-form-item>
        </div>
      </div>
    </form>
  </div>

  <div class="add-exception">
    <button class="button" nz-button nzType="default" (click)="handleAddException()">
      {{ 'dj-新增参数' | translate }}
    </button>
  </div>
</div>
