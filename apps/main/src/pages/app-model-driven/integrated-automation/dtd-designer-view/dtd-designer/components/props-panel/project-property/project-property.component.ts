import { Component, ElementRef, EventEmitter, Input, OnD<PERSON>roy, OnInit, Output, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import dayjs from 'dayjs';
import { combineLatest, forkJoin, of, Subject } from 'rxjs';
import { debounceTime, distinctUntilChanged, filter, take, takeUntil } from 'rxjs/operators';
import { cloneDeep, isEqual, isFunction, omit, size } from 'lodash';
import { NzMessageService } from 'ng-zorro-antd/message';

import { GlobalService } from 'common/service/global.service';
import { DtdService } from 'pages/app/dtd/dtd.service';
import { AppService } from 'pages/apps/app.service';

import { DtdDesignerViewService } from '../../../../service/dtd-designer-view.service';
import { PropsPanelApiService } from '../props-panel-api.service';
import { WorkDesignType } from '../../../types';
import { GraphComponent } from '../../../graph/graph.component';
import { ProjectListComponent } from '../../../project/project-list.component';
import { ProjectPropertyApiService } from './project-property-api.service';
import { ProjectSetService } from '../../project-widget/service/project-set.service';
import { businessType, dateType } from '../../project-widget/project-set';
import { MilestoneComponent } from './components/milestone/milestone.component';
import { AuthService } from 'common/service/auth.service';

@Component({
  selector: 'app-project-property',
  templateUrl: './project-property.component.html',
  styleUrls: ['./project-property.component.less'],
  providers: [ProjectSetService],
})
export class ProjectPropertyComponent implements OnInit, OnDestroy {
  @Input()
  get data() {
    return this._data;
  }
  set data(value) {
    this._data = value;
    this.projectSetService.projectLoading = null;
    this.milestoneInited$.next(false);
    this.loadRemoteData();
  }

  @Input() graphComponentRef: GraphComponent;
  @Input() projectComponentRef: ProjectListComponent;

  @Output() formChange = new EventEmitter<{ valid: boolean; getRawValue: () => { id: string } }>();

  @ViewChild('milestoneComponentRef') milestoneComponentRef: MilestoneComponent;

  // 是否正在获取数据
  public loading: boolean = false;
  public validMap: { [x: string]: boolean } = {};

  public readonly errorTips: Record<string, Record<string, string>> = {
    default: {
      required: this.translate.instant('dj-必填'),
      pattern: this.translate.instant('dj-超长'),
    },
  };

  public lang: Record<
    string,
    {
      zh_CN: string;
      zh_TW: string;
      en_US: string;
    }
  > = {};

  public basicForm = this.fb.group({});
  public projectTreeData;

  public typeList = [
    {
      code: 'mainline',
      name: 'dj-数据预整理项目',
    },
    {
      code: 'user',
      name: 'dj-可追踪项目',
    },
  ] as const;

  // public currentProject = {
  //   basicForm: this.fb.group({})
  // };
  // public modalFlag = false;
  // public isMainPro = false;
  // public isRoot = false;

  dateType = dateType; // 时距
  businessType = businessType;
  patternData = [{ value: 'BUSINESS', label: 'BUSINESS' }];
  performerType = [];
  choosePolicy = [];
  isSaving: boolean;
  milestoneInvalid: boolean = false;

  private milestoneInited$ = new Subject<boolean>();
  private destory$ = new Subject();
  private _data: any;
  private originData: {};
  private flagLeave: boolean = false; // 是否离开，如果离开则不再提示保存，当表单再次被修改时设为true

  get WorkDesignType() {
    return WorkDesignType;
  }

  get modalFlag() {
    return this.projectSetService.modalFlag;
  }

  get isMainPro() {
    return this.projectSetService.isMainPro;
  }

  get projectLoading() {
    return this.projectSetService.projectLoading;
  }

  get currentProject() {
    return this.projectSetService.currentProject;
  }

  // set currentProject(val) {
  //   this.projectSetService.currentProject = val;
  // }

  get isRoot() {
    return this.projectSetService.isRoot;
  }

  get allDataStatus() {
    return this.projectSetService.allDataStatus;
  }
  set allDataStatus(val) {
    this.projectSetService.allDataStatus = val;
  }

  get projectData() {
    return this.projectSetService.projectData;
  }

  // 是否为可追踪项目
  get isTraceable() {
    return ['user', 'solve'].includes(this.currentProject.meta?.executeType);
  }

  // 是否公共解决方案
  get isCommonApp() {
    return this.appService.isCommonApp;
  }

  constructor(
    private router: Router,
    private fb: FormBuilder,
    private translate: TranslateService,
    private apiService: PropsPanelApiService,
    public globalService: GlobalService,
    private appService: AppService,
    private dtdDesignerService: DtdDesignerViewService,
    private projectPropertyApiService: ProjectPropertyApiService,
    private projectSetService: ProjectSetService,
    public dtdService: DtdService,
    private message: NzMessageService,
    private element: ElementRef<Element>,
    private authService: AuthService,
  ) {}

  private addObserver() {
    this.graphComponentRef.graphService.dataState$.pipe(takeUntil(this.destory$)).subscribe((value) => {
      if (value === null) return;

      const item = value.find((e) => e.code === this.data.code);

      if (!item) return;

      // this.currentProject.stateMaps = item.stateMaps;
      // this.transferStateMaps();
    });
  }

  private async loadProjectInfo(notChangeLoading = false) {
    await this.projectSetService.handleLoadDataStatus();
    await this.projectSetService.handleEdit(
      { id: this.data.code },
      this.currentProject.root ? 'isRoot' : 'notRoot',
      notChangeLoading,
    );

    this.currentProject.basicForm.valueChanges
      .pipe(takeUntil(this.destory$), distinctUntilChanged(), debounceTime(250))
      .subscribe(() => {
        this.validMap['basicForm'] = this.currentProject.basicForm.valid;
        this.emitValidChanged();

        this.checkContentChangeWithoutSave((isChange) => {
          // 如果表单变动，则重置离开标识
          isChange && (this.flagLeave = false);
        });
      });

    combineLatest([
      this.projectSetService.dataStatusLoaded$,
      this.projectSetService.dataGroupsLoaded$,
      this.isTraceable ? this.milestoneInited$ : of(true),
    ])
      .pipe(
        filter(([status, groups, milestoneReady]) => {
          return status && groups && milestoneReady;
        }),
        take(1),
      )
      .subscribe(() => {
        this.setOriginData();
      });
  }

  private async loadRemoteData() {
    this.loadProjectInfo();
  }

  /**
   * 设置检查内容变化的函数
   */
  private setCheckContentChangedFn() {
    this.dtdDesignerService.setContentChangeCheckObject({
      contentComponent: this,
      checkFunction: 'checkContentChangeWithoutSave',
    });
  }

  /**
   * 设置原始值
   */
  private setOriginData(): void {
    const params = this.isTraceable ? this.generateTraceableParams() : this.generatePreProcessedParams();

    this.originData = cloneDeep(params);
  }

  private emitValidChanged() {
    const valid = Object.values(this.validMap).every((valid) => valid);

    this.formChange.emit({
      valid,
      getRawValue: () => ({
        id: this.currentProject.meta.code,
      }),
    });
  }

  public handleValidChanged(key: string, valid: boolean) {
    this.validMap[key] = valid;
  }

  public initOrEndChanged(): void {
    if (this.currentProject.basicForm.get('init').value && this.currentProject.basicForm.get('end').value) {
      this.milestoneComponentRef?.loadTasksByDataState();
    }
  }

  public handleUpdate(value: any): void {
    this.projectSetService.statusListUpdate$.next(value.list);
  }

  public handlePatch({ key, data }: { key: string; data: any }): void {
    const value = {
      [key]: data?.value,
    };

    this.currentProject.basicForm.patchValue(value);
    this.currentProject.basicForm.get(key).markAsDirty();
    this.currentProject.basicForm.get(key).updateValueAndValidity({ onlySelf: true });
  }

  public checkContentChangeWithoutSave(callback?: Function): boolean {
    const params = this.generateParams();

    const isChanged = !isEqual(this.originData, params);
    callback && callback(isChanged);
    return isChanged && !this.flagLeave;
  }

  public async handleReloadProject() {
    await this.loadProjectInfo();
  }

  // TODO: 界面设计 / 自定义卡面设计
  public handlePageDesign(isMobile: boolean): void {
    this.projectComponentRef.handlePageDesign(this.projectTreeData);
  }

  // mobile界面设计
  public handleMobilePageDesign(): void {
    this.projectComponentRef.handlePageDesign(this.projectTreeData);
  }

  public handleMilestoneInited() {
    this.milestoneInited$.next(true);
  }

  handleValidMilestone(validate: boolean = true) {
    if (this.currentProject.milestone) {
      // 里程碑配置
      for (const m of this.currentProject.phases || []) {
        if (m.invalid) {
          validate = false;
          break;
        }
      }
      this.milestoneInvalid = !validate;
    } else {
      // 里程碑关闭，不验证
      this.milestoneInvalid = false;
    }

    return validate;
  }

  // 校验
  handleTraceableValidate(): boolean {
    let validate = true;
    // basic
    for (const i of Object.keys(this.currentProject.basicForm?.controls)) {
      this.currentProject.basicForm.controls[i].markAsDirty();
      this.currentProject.basicForm.controls[i].updateValueAndValidity();
    }
    if (this.currentProject.basicForm.invalid) {
      validate = false;
    }
    // phases
    validate = this.handleValidMilestone(validate);
    // push
    this.currentProject.atmcDatas.forEach((s) => {
      const { proVarKey, athenaKey } = s;
      s['errorKeys'] = [];
      if ([undefined, null, ''].includes(proVarKey)) {
        s['errorKeys'].push('proVarKey');
        validate = false;
      }
      if ([undefined, null, ''].includes(athenaKey)) {
        s['errorKeys'].push('athenaKey');
        validate = false;
      }
    });
    const executor = this.currentProject.executor;
    if (executor) {
      const source = executor?.source;
      // 使用了新的当责者
      if (executor.type !== 'default') {
        if (source === 'personnel') {
          if (size(executor.personnel) === 0) {
            validate = false;
          }
        } else if (source === 'variable') {
          if (size(executor.variable) === 0) {
            validate = false;
          }
        } else if (source === 'activity') {
          if (size(executor.activity) === 0) {
            validate = false;
          }
        } else if (source === 'formFiled') {
          if (size(executor.formFiled) === 0) {
            validate = false;
          }
        } else if (source === 'department') {
          if (size(executor.department) === 0) {
            validate = false;
          }
        }
      }
    } else {
      // 维持使用旧的数据结构 charge
      const { choosePolicy: policy, identities = [] } = this.currentProject.personInCharge;
      this.currentProject.personInCharge['errorKeys'] = '';
      if ([undefined, null, ''].includes(policy)) {
        validate = false;
        this.currentProject.personInCharge['errorKeys'] = 'choosePolicy';
      }
      identities.forEach((s) => {
        const { isBusiness, performerType, performerName, performerVariable, performerValue } = s;
        s['errorKeys'] = [];
        if ([undefined, null, ''].includes(performerType)) {
          s['errorKeys'].push('performerType');
          validate = false;
        }
        if ([undefined, null, ''].includes(performerName)) {
          s['errorKeys'].push('performerName');
          validate = false;
        }
        if ([undefined, null, ''].includes(performerValue)) {
          s['errorKeys'].push('performerValue');
          validate = false;
        }
        if (!!isBusiness) {
          if ([undefined, null, ''].includes(performerVariable)) {
            s['errorKeys'].push('performerVariable');
            validate = false;
          }
        }
      });
    }
    this.currentProject.presetVariables.forEach((s) => {
      const { performerType, performerName, performerValue } = s;
      s['errorKeys'] = [];
      if ([undefined, null, ''].includes(performerType)) {
        s['errorKeys'].push('performerType');
        validate = false;
      }
      if ([undefined, null, ''].includes(performerName)) {
        s['errorKeys'].push('performerName');
        validate = false;
      }
      if ([undefined, null, ''].includes(performerValue)) {
        s['errorKeys'].push('performerValue');
        validate = false;
      }
    });
    // source
    const { actionParams = [] } = this.currentProject.sourceDetailAction;
    actionParams.forEach((s) => {
      const { name, type, value } = s;
      s['errorKeys'] = [];
      if ([undefined, null, ''].includes(name)) {
        s['errorKeys'].push('name');
        validate = false;
      }
      if ([undefined, null, ''].includes(type)) {
        s['errorKeys'].push('type');
        validate = false;
      }
      if ([undefined, null, ''].includes(value)) {
        s['errorKeys'].push('value');
        validate = false;
      }
    });
    return validate;
  }

  handlePreProcessedValidate(): boolean {
    let validate = true;

    for (const i of Object.keys(this.currentProject.basicForm?.controls)) {
      this.currentProject.basicForm.controls[i].markAsDirty();
      this.currentProject.basicForm.controls[i].updateValueAndValidity();
    }

    if (this.currentProject.basicForm.invalid) {
      validate = false;
    }

    return validate;
  }

  async handleRefreshProjects() {
    await this.projectComponentRef?.fetchProjects();
  }

  // 保存预处理项目
  // private doSavePreProcessedProject(): void {
  //   this.currentProject.formDirty = true;
  //   if (!this.handlePreProcessedValidate()) {
  //     return;
  //   }
  //   const flag = this.modalFlag;
  //   const params = this.generatePreProcessedParams();

  //   this.isSaving = true;
  //   this.dtdService.saveProject(flag, params).subscribe(
  //     (res) => {
  //       if (res.code === 0) {
  //         this.isSaving = false;
  //         this.message.success(this.translate.instant('dj-保存成功！'));
  //         this.projectSetService.projectUpdate$.next(params.code);

  //         // 如果是手动发起的项目，则通知任务列表更新
  //         if (params['manualAble'] === true) {
  //           this.dtdService.updateTaskList$.next(true);
  //         }
  //       }
  //     },
  //     () => {
  //       this.isSaving = false;
  //     },
  //   );
  // }

  private generatePreProcessedParams() {
    const { basicForm, selectedTask = [], lang } = this.currentProject;
    const { primaryProjectCode, ...param } = basicForm.getRawValue();
    const init = this.allDataStatus?.find((s) => s.code === param['init']) ?? {};
    const end = this.allDataStatus?.find((s) => s.code === param['end']) ?? {};

    param['init'] = {
      code: init.code,
      dataCode: init.dataCode,
    };
    param['end'] = {
      code: end.code,
      dataCode: end.dataCode,
    };

    if (param['executeType'] !== 'mainline') {
      param['primaryProjectCode'] = primaryProjectCode;
    }

    param['lang'] = {
      name: lang?.name,
      description: lang?.description,
    };

    if (param['manualAble'] && !!param['authorityPrefix']) {
      if (this.isCommonApp) {
        param['authorityPrefix'] = `${this.appService?.selectedApp?.code}:${param.code}`;
      } else {
        param['authorityPrefix'] = `${this.appService?.selectedApp?.code}:startProject`;
      }
    } else {
      param['authorityPrefix'] = '';
    }

    param['application'] = this.appService?.selectedApp?.code;
    param['subTasks'] = selectedTask;

    return param;
  }

  private doValidate() {
    Object.values((this.currentProject.basicForm as FormGroup).controls).forEach((i) => {
      i.markAsDirty();
      i.updateValueAndValidity({ onlySelf: true });
    });
    (this.currentProject.basicForm as FormGroup).updateValueAndValidity();
    let valid: boolean;
    if (this.isTraceable) {
      valid = this.handleTraceableValidate() && this.currentProject.basicForm.valid;
    } else {
      valid = this.handlePreProcessedValidate() && this.currentProject.basicForm.valid;
    }

    this.formChange.emit({ valid, getRawValue: () => ({ id: this.currentProject.meta.code }) });

    if (!valid) {
      const errorDom =
        this.element.nativeElement.querySelector('.ant-form-item-explain-error') ??
        this.element.nativeElement.querySelector('.error-text');

      errorDom?.parentElement.scrollIntoView();
    }

    return valid;
  }

  private generateParams() {
    if (this.isTraceable) {
      return this.generateTraceableParams();
    } else {
      return this.generatePreProcessedParams();
    }
  }

  /**
   * 保存
   * 外部可用
   * @param needTips
   * @returns
   */
  public async handleSave(needTips = true): Promise<any> {
    this.currentProject.formDirty = true;

    const isValid = this.doValidate();
    if (!isValid) {
      return false;
    }

    const flag = this.modalFlag;
    const params = this.generateParams();

    // 项目属性面板只有组合项目才能打开
    params.projectFrom = 'combination';

    this.isSaving = true;
    try {
      if (this.dtdDesignerService.adpVersionHeaders) {
        params['adpVersionHeaders'] = this.dtdDesignerService.adpVersionHeaders;
      }
      const res = await this.dtdService.saveProject(flag, params).toPromise();

      if (res.code === 0) {
        this.isSaving = false;
        needTips && this.message.success(this.translate.instant('dj-保存成功！'));
        this.originData = { ...this.originData, ...omit(params, ['projectFrom', 'adpVersionHeaders']) };
        this.projectSetService.projectUpdate$.next(params.code);

        // 保存后无需重新刷新当前面板
        // await this.handleReloadProject();
        await this.handleRefreshProjects();

        // 如果是手动发起的项目，则通知任务列表更新
        if (params['manualAble'] === true) {
          this.dtdService.updateTaskList$.next(true);
        }
        return true;
      }
    } catch (error) {
      this.isSaving = false;
    }
  }

  private generateTraceableParams() {
    const {
      basicForm,
      atmcDatas = [],
      sourceDetailAction = {},
      personInCharge = {},
      executor,
      presetVariables = [],
      assignConfig = {},
      lang,
      targetLang,
    } = this.currentProject;
    const { targetName, targetCode, primaryProjectCode, dueDateType, startDate, count, ...param } =
      basicForm.getRawValue();
    const init = this.allDataStatus.find((s) => s.code === param['init']);
    param['init'] = {
      code: init?.code,
      dataCode: init?.dataCode,
    };
    const end = this.allDataStatus.find((s) => s.code === param['end']);

    param['end'] = {
      code: end?.code,
      dataCode: end?.dataCode,
    };

    if (param['executeType'] !== 'mainline') {
      param['primaryProjectCode'] = primaryProjectCode;
    }

    param['lang'] = lang;
    param['target'] = {
      name: targetName,
      code: targetCode,
      lang: targetLang,
    };
    param['dueDateTimeDistance'] = {
      type: dueDateType,
    };

    if (dueDateType === 'MULTI_DAY') {
      param['dueDateTimeDistance']['startDate'] = dayjs(startDate).format('YYYY-MM-DD');
      param['dueDateTimeDistance']['count'] = count;
    } else if (dueDateType === 'WEEK_DAY') {
      param['dueDateTimeDistance']['timeDistanceBegin'] = 1;
      param['dueDateTimeDistance']['timeDistanceEnd'] = 7;
      param['dueDateTimeDistance']['interval'] = 'WEEK';
    } else if (dueDateType === 'DAY') {
      param['dueDateTimeDistance']['timeDistanceBegin'] = 1;
      param['dueDateTimeDistance']['timeDistanceEnd'] = 1;
      param['dueDateTimeDistance']['interval'] = 'DAY';
    } else if (!dueDateType) param['dueDateTimeDistance'] = null;

    param['milestone'] = this.currentProject.milestone;
    param['phases'] = this.currentProject.phases.map((one) => {
      // eslint-disable-next-line no-shadow
      const { code, name, taskCodes, lang, isHideKey } = isFunction(one?.getRawValue) ? one?.getRawValue() : one;

      return { code, name, taskCodes, lang, isHideKey };
    });
    param['application'] = this.appService?.selectedApp?.code;
    param['atmcDatas'] = atmcDatas;

    if (!!sourceDetailAction['actionId']) {
      param['sourceDetailAction'] = {
        actionId: sourceDetailAction['actionId'] || '',
        actionName: sourceDetailAction['actionName'] || '',
        // type: sourceDetailAction['type'] || '',
        actionParams: sourceDetailAction['actionParams'] || [],
      };
    }
    if (!executor) {
      param['personInCharge'] = {
        choosePolicy: personInCharge?.choosePolicy,
        processType: personInCharge?.processType,
        identities: personInCharge?.identities?.map((d) => {
          const { isBusiness, performerVariable, ...others } = d;
          const temp = {
            isBusiness,
            ...others,
          };
          if (isBusiness) {
            temp['performerVariable'] = performerVariable;
          }
          return temp;
        }),
      };
      param['presetVariables'] = presetVariables;
    } else {
      param['personInCharge'] = {};
      param['presetVariables'] = [];
    }
    param['assignConfig'] = assignConfig;
    param['executor'] = executor;

    if (this.currentProject.pageCode) {
      param['pageCode'] = this.currentProject.pageCode;
    }

    return param;
  }

  async handleSaveAndRefresh() {
    if (this.authService.getAuth('update')) {
      await this.handleSave(false);
    }
    this.loadProjectInfo(true);
  }

  /**
   * 设置离开标识
   * @param value
   */
  public setFlagLeave(value: boolean) {
    this.flagLeave = value;
  }

  public async triggerSave(callback?: Function) {
    const res = await this.handleSave(false);
    callback && callback(res);
  }

  ngOnInit() {
    this.setCheckContentChangedFn();
    this.addObserver();
  }

  ngOnDestroy(): void {
    this.dtdDesignerService.resetContentChangeCheckObject();
    this.destory$.next();
    this.destory$.complete();
  }
}
