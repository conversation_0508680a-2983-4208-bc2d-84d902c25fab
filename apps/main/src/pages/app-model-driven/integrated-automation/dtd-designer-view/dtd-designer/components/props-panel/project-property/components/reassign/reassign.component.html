<nz-collapse>
  <nz-collapse-panel [nzHeader]="'dj-转派' | translate" [nzActive]="true" [nzExpandedIcon]="'caret-right'">
    <div *ngIf="currentProject.steps[currentProject.steps.length - 1].display" class="assign">
      <div class="assign-control">
        <label
          nz-checkbox
          [(ngModel)]="currentProject.assignConfig.assignAble"
          (ngModelChange)="handleAssignAbleChange()"
        ></label>

        <span class="check-label">{{ 'dj-转派' | translate }}</span>
        <span class="check-label-json" (click)="handleClickLabelJson()" *ngIf="currentProject.assignConfig.assignAble"
          >{{ 'dj-转派业务逻辑' | translate }}&lt;/&gt;</span
        >
      </div>

      <app-extend-editor-modal
        *ngIf="isShowJsonModal"
        [data]="currentProject.assignConfig"
        [width]="'640px'"
        (ok)="handleSaveSet($event)"
        (close)="isShowJsonModal = false"
      ></app-extend-editor-modal>
    </div>
  </nz-collapse-panel>
</nz-collapse>
