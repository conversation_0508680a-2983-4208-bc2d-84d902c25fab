import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { <PERSON><PERSON>rray, FormBuilder, Validators } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { Subject } from 'rxjs/internal/Subject';

@Component({
  selector: 'app-task-push-data',
  templateUrl: './push-data.component.html',
  styleUrls: ['./push-data.component.less'],
})
export class PushDataComponent implements OnInit {
  @Input() atmcDatas: any;
  @Input() extra: any;
  @Input() dataFormGroup: any;
  @Input() isReference: boolean = false;
  @Output() dataChange: EventEmitter<any> = new EventEmitter();
  // pushDataFormGroup: any
  public lang: Record<
    string,
    {
      zh_CN: string;
      zh_TW: string;
      en_US: string;
    }
  > = {};

  constructor(private fb: FormBuilder, public translate: TranslateService) {}

  public readonly errorTips: Record<string, Record<string, string>> = {
    default: {
      required: this.translate.instant('dj-必填'),
      pattern: this.translate.instant('dj-超长'),
    },
  };
  private destory$ = new Subject();
  ngOnInit(): void {
    this.init();
  }
  ngOnChanges(change) {
    this.init();
  }
  ngOnDestroy(): void {
    this.destory$.next();
    this.destory$.complete();
  }
  init(): void {
    if (this.atmcDatas?.length) {
      this.dataFormGroup.get('atmcDatas').clear();
      this.atmcDatas.forEach((data) => {
        this.dataFormGroup.get('atmcDatas').push(
          this.fb.group({
            athenaKey: [data.athenaKey || '', [Validators.required]],
            proVarKey: [data.proVarKey || '', [Validators.required]],
          }),
        );
      });
    } else if (this.atmcDatas?.length === 0 || !this.atmcDatas) {
      this.dataFormGroup.get('atmcDatas').clear();
    }
  }
  // 增加推送数据
  handleAddPush(): void {
    this.dataFormGroup.get('atmcDatas').push(
      this.fb.group({
        athenaKey: ['', [Validators.required]],
        proVarKey: ['', [Validators.required]],
      }),
    );
    this.dataFormGroup.get('atmcDatas').controls.markAsDirty();
    this.dataFormGroup.get('atmcDatas').controls.updateValueAndValidity({ onlySelf: true });
  }

  // 删除推送数据
  handleDeletePush(index: any): void {
    (this.dataFormGroup.get('atmcDatas') as FormArray).removeAt(index);
    this.atmcDatas.splice(index, 1);
  }

  // 修改推送数据
  handlePushData(index, key: any, data: any): void {
    this.dataFormGroup.get('atmcDatas').controls[index].get(key).setValue(data?.value);
    this.dataFormGroup.get('atmcDatas').controls[index].get(key).markAsDirty();
    this.dataFormGroup.get('atmcDatas').controls[index].get(key).updateValueAndValidity({ onlySelf: true });
  }
}
