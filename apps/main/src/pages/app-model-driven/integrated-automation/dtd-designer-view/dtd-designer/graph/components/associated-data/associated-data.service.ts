import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { SystemConfigService } from 'common/service/system-config.service';
import { Observable } from 'rxjs';

@Injectable()
export class ApiService {
  adesignerUrl: string;
  headerCofig = {};
  applicationCodeProxy;

  constructor(private http: HttpClient, private configService: SystemConfigService) {
    this.configService.get('adesignerUrl').subscribe((url) => {
      this.adesignerUrl = url;
    });
  }

  saveDataState(param: any): Observable<any> {
    if (this.applicationCodeProxy) {
      param.application = this.applicationCodeProxy;
    }
    const url = `${this.adesignerUrl}/athena-designer/data/saveDataState`;
    return this.http.post(url, param, { headers: this.headerCofig });
  }

  /**
   * 获状态数据
   * @param application
   * @param groupCode
   * @returns
   */
  fetchDataList(application: string, groupCode: string = ''): Observable<any> {
    if (this.applicationCodeProxy) {
      application = this.applicationCodeProxy;
      groupCode = this.applicationCodeProxy;
    }
    return this.http.get(`${this.adesignerUrl}/athena-designer/task/getDataList`, {
      params: { application, groupCode },
      headers: this.headerCofig,
    });
  }
}
