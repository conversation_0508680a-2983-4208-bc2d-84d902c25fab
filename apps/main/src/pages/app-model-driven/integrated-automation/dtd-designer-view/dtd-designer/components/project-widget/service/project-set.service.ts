import { Injectable, OnDestroy } from '@angular/core';
import { FormBuilder, ValidationErrors, Validators } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { BehaviorSubject, Observable, Observer, Subject } from 'rxjs';
import { projectType, getProjectSteps } from '../project-set';
import { cloneDeep } from 'lodash';
import { AppService } from 'pages/apps/app.service';
import { LocaleService } from 'common/service/locale.service';
import { ApiService } from './api.service';
import CodeEnum from '../../../../../../../../common/config/code';

@Injectable()
export class ProjectSetService implements OnDestroy {
  modalFlag: 'add' | 'edit'; // 开窗类型: 编辑、添加
  isMainPro: any; // 是否主项目模式
  projectLoading: any;
  currentProject: any; // 当前任务数据（新增或编辑）
  performerType: any; // 用户类型（执行人）
  choosePolicy: any; // 人员选择策略（当责者）
  projectType = projectType; // 项目类型;
  projectData: any;
  allDataGroups: any;
  allDataStatus: any; // 数据状态
  isRoot: boolean = false; // 子项目是否是根节点
  originalData: any[] = [];
  statusListUpdate$: any = new Subject();
  projectUpdate$: any = new Subject();

  defaultAssignConfig = {
    // action: {
    //   sequence: 0,
    //   actionParams: [
    //     {
    //       name: 'liable_person_info',
    //       type: 'PROCESS_VARIABLE',
    //       value: 'question_info',
    //     },
    //   ],
    //   actionId: 'esp_transfer.principal.info.update',
    //   title: '更新任务',
    //   type: 'ESP',
    //   serviceName: 'transfer.principal.info.update',
    // },
  };
  orignProBaseInfo: any = {};

  dataStatusLoaded$ = new BehaviorSubject<boolean>(false);
  dataGroupsLoaded$ = new BehaviorSubject<boolean>(false);

  constructor(
    private fb: FormBuilder,
    private translateService: TranslateService,
    private appService: AppService,
    private languageService: LocaleService,
    private apiService: ApiService,
  ) {
    this.handleInit();
    // this.handleLoadDataStatus();
    this.handleLoadDataGroups();
  }

  get appCode() {
    return this.appService?.selectedApp?.code;
  }

  handleInit() {
    this.currentProject = {
      steps: getProjectSteps(this.isMainPro),
      basicForm: this.fb.group({}), // 基础信息
      phases: [], // 里程碑
      milestone: true, // 是否开启里程碑
      atmcDatas: [], // 推送数据
      presetVariables: [], // 当责者
      selectedTask: [], // 任务 目前好像没有用到
      executor: { type: 'default', source: 'personnel', personnel: [] },
      personInCharge: {},
      // personInCharge: { choosePolicy: '', processType: '', identities: [] }, // 人员选择策略
      // 转派
      assignConfig: {
        assignAble: false,
      },
      // 来源详情api
      sourceDetailAction: {
        actionId: '',
        actionName: '',
        // type: '',
        actionParams: [],
      },
    };
    // 用户类型
    this.performerType = [
      { value: 'user', label: this.translateService.instant('dj-用户') },
      { value: 'duty', label: this.translateService.instant('dj-职能') },
      {
        value: 'deptDirector',
        label: this.translateService.instant('dj-部门主管'),
      },
      {
        value: 'deptUser',
        label: this.translateService.instant('dj-部门人员'),
      },
      {
        value: 'personInCharge',
        label: this.translateService.instant('dj-当责者'),
      },
      { value: 'activity', label: this.translateService.instant('dj-活动') },
    ];
    // 定义人员选择策略
    this.choosePolicy = [
      { value: 'single', label: this.translateService.instant('dj-单人') },
      // { value: 'all', label: this.translateService.instant('dj-多人') },
    ];
  }

  // 获取数据群落
  handleLoadDataGroups(): void {
    const param = `?application=${this.appService?.selectedApp?.code}`;
    this.apiService.loadDataGroupsByApplication(param).subscribe((res) => {
      if (res.code === 0) {
        this.allDataGroups = (res.data || []).map((s) => {
          return {
            ...s,
            label: s.name,
            value: s.code,
          };
        });

        this.dataGroupsLoaded$.next(true);
      }
    });
  }

  // 获取数据状态
  async handleLoadDataStatus() {
    const res = await this.apiService.loadDataStates({ application: this.appService?.selectedApp?.code }).toPromise();
    if (res.code === 0) {
      this.allDataStatus = (res.data || []).map((s) => {
        return {
          ...s,
          label: s.name,
          value: s.code,
        };
      });
      this.dataStatusLoaded$.next(true);
    }
  }

  async initMainlineProject() {
    this.isMainPro = true;
    this.handleSwitchForm(true);
    this.modalFlag = 'add';
    this.currentProject['lang'] = {
      name: { zh_CN: '', zh_TW: '', en_US: '' },
      description: { zh_CN: '', zh_TW: '', en_US: '' },
      sourceName: { zh_CN: '', zh_TW: '', en_US: '' },
      dueDateName: { zh_CN: '', zh_TW: '', en_US: '' },
    };
    this.currentProject['targetLang'] = {
      name: { zh_CN: '', zh_TW: '', en_US: '' },
    };
    this.projectType = cloneDeep(projectType);
    this.currentProject['steps'] = cloneDeep(getProjectSteps(this.isMainPro));
    this.currentProject['meta'] = {};
    this.currentProject.formDirty = false;
    this.currentProject.basicForm.reset();
    this.currentProject.basicForm.get('code').enable();
    // this.currentProject.basicForm.get('code').clearValidators();
    // this.currentProject.basicForm.get('code').updateValueAndValidity();
    const mData = await this.handleInitMainlineData();
    this.currentProject.basicForm.patchValue({
      ...mData,
    });
    this.currentProject = {
      ...this.currentProject,
      dtdData: null,
      atmcDatas: [],
      selectedTask: [],
      executor: { type: 'default', source: 'personnel', personnel: [] },
      // personInCharge: { choosePolicy: '', processType: '', identities: [] },
      sourceDetailAction: {
        actionId: '',
        actionName: '',
        // type: '',
        actionParams: [],
      },
      projectTypeData: this.projectType,
      isNew: true,
    };
    // this.projectModal = true;
  }

  async handleInitMainlineData() {
    await this.getPorjectList();
    // 设置任务代号 task0001递增
    let projectCode = '';
    let projectName = '';
    const { name } = this.appService?.selectedApp;
    let { code } = this.appService?.selectedApp;
    code = code.replace(/-/g, '_');
    const nameList = [...new Set(this.originalData?.map((item) => item.id))];
    const nameTaskList = nameList.filter((item: string) => item.startsWith(`${code}_mainline_project`));
    // if (nameTaskList.length > 0) {
    const taskIndexList = nameTaskList
      .map((item: string) => {
        const n = Number(item.split('mainline_project_')[1]);
        if (!Number.isNaN(n)) return n;
      })
      .filter((item) => item);
    if (taskIndexList.length === 0) taskIndexList.push(0);
    const maxIndex = Math.max(...taskIndexList) + 1;
    // 获取code
    // projectCode = `${code}_mainline_project_${String(maxIndex).padStart(4, '0')}`;
    // projectCode = this.checkCode(projectCode, 'mainline_project_');
    projectCode = await this.appService.getCodeByType(CodeEnum.PM);
    projectName = `${name}_${this.translateService.instant('dj-项目')}_${String(maxIndex).padStart(4, '0')}`;

    this.currentProject.lang = {
      ...(this.currentProject.lang || {}),
      name: { en_US: projectName, zh_CN: projectName, zh_TW: projectName },
      description: { en_US: projectName, zh_CN: projectName, zh_TW: projectName },
    };
    return await {
      code: projectCode,
      name: projectName,
      description: projectName,
      executeType: 'mainline',
      type: 'BUSINESS',
      groupCode: this.allDataGroups?.[0]?.value,
      tenantId: 'SYSTEM',
      manualAble: false,
      authorityPrefix: false,
    };
  }

  async initTraceableProject(project: any, type: string) {
    this.isRoot = type === 'isRoot';
    this.isMainPro = false;
    this.handleSwitchForm(false);
    this.modalFlag = 'add';
    this.currentProject['lang'] = {
      name: { zh_CN: '', zh_TW: '', en_US: '' },
      description: { zh_CN: '', zh_TW: '', en_US: '' },
      sourceName: { zh_CN: '', zh_TW: '', en_US: '' },
      dueDateName: { zh_CN: '', zh_TW: '', en_US: '' },
    };
    this.currentProject['targetLang'] = {
      name: { zh_CN: '', zh_TW: '', en_US: '' },
    };
    this.projectType = projectType.filter((s) => s.value !== 'mainline');
    this.currentProject['steps'] = cloneDeep(getProjectSteps(this.isMainPro));
    this.currentProject['meta'] = {};
    this.currentProject.formDirty = false;
    this.currentProject.basicForm.reset();
    this.currentProject.basicForm.get('code').enable();
    // this.currentProject.basicForm.get('code').clearValidators();
    // this.currentProject.basicForm.get('code').updateValueAndValidity();
    const CData = await this.handleInitChildData(project);
    this.currentProject.basicForm.patchValue({
      ...CData,
    });
    this.currentProject = {
      ...this.currentProject,
      dtdData: null,
      atmcDatas: [
        {
          proVarKey: 'sourceIds',
          athenaKey: 'sourceIds',
        },
        {
          proVarKey: 'dueDate',
          athenaKey: 'dueDate',
        },
      ],
      presetVariables: [],
      selectedTask: [],
      executor: { type: 'default', source: 'personnel', personnel: [] },
      // personInCharge: {
      //   choosePolicy: 'single',
      //   processType: '',
      //   identities: [
      //     {
      //       isBusiness: false,
      //       performerName: this.translateService.instant('dj-当责者'),
      //       performerType: 'user',
      //       performerValue: '$(personInCharge)',
      //       performerVariable: '',
      //     },
      //   ],
      // },
      // 转派
      assignConfig: {
        assignAble: false,
      },
      sourceDetailAction: {
        actionId: '',
        actionName: '',
        // type: '',
        actionParams: [],
      },
      projectTypeData: this.projectType,
      isNew: true,
    };
    // this.projectModal = true;
  }

  async handleInitChildData(project: any) {
    await this.getPorjectList();
    // 设置任务代号 task0001递增
    let projectCode = '';
    let projectName = '';
    const { name } = this.appService?.selectedApp;
    let { code } = this.appService?.selectedApp;
    code = code.replace(/-/g, '_');
    const nameList = [...new Set(this.originalData?.map((item) => item.id))];
    const nameTaskList = nameList.filter((item: string) => item.startsWith(`${code}_project`));
    // if (nameTaskList.length > 0) {
    const taskIndexList = nameTaskList
      .map((item: string) => {
        const n = Number(item.split('project_')[1]);
        if (!Number.isNaN(n)) return n;
      })
      .filter((item) => item);
    if (taskIndexList.length === 0) taskIndexList.push(0);
    const maxIndex = Math.max(...taskIndexList) + 1;
    // projectCode = `${code}_project_${String(maxIndex).padStart(4, '0')}`;
    // projectCode = this.checkCode(projectCode, 'project_');
    projectCode = await this.appService.getCodeByType(CodeEnum.PU);
    projectName = `${name}_${this.translateService.instant('dj-项目')}_${String(maxIndex).padStart(4, '0')}`;

    this.currentProject.lang = {
      ...(this.currentProject.lang || {}),
      name: { en_US: projectName, zh_CN: projectName, zh_TW: projectName },
      description: { en_US: projectName, zh_CN: projectName, zh_TW: projectName },
    };

    return await {
      primaryProjectCode: project.id,
      code: projectCode,
      name: projectName,
      tenantId: 'SYSTEM',
      groupCode: this.allDataGroups?.[0]?.value,
      description: projectName,
      type: 'BUSINESS',
      executeType: 'user',
      assignAble: false,
      merge: false,
    };
  }

  checkCode(proCode: any, type: 'project_' | 'mainline_project_'): any {
    if (this.originalData.find((item) => item.id === proCode)) {
      const proCodeIndex = Number(proCode.split(type)[1]);
      let { code } = this.appService?.selectedApp;
      code = code.replace(/-/g, '_');
      const _code = `${code}_${type}${String(proCodeIndex + 1).padStart(4, '0')}`;
      return this.checkCode(_code, type);
    } else {
      return proCode;
    }
  }

  codeValidator = (control) =>
    new Observable((observer: Observer<ValidationErrors | null>) => {
      const { value = '' } = control;
      const str = new RegExp(`^(?!.*__.*$)^(?!.*-.*$)`, 'g');
      //
      if (!str.test(value)) {
        observer.next({
          error: true,
          duplicated: true,
        });
      } else {
        observer.next(null);
      }
      observer.complete();
    });

  // 切换form
  handleSwitchForm(main: boolean): void {
    let forms: any = {
      code: [null, [Validators.required]],
      name: [null, [Validators.required]],
      description: [null],
      init: [null, [Validators.required]],
      end: [null, [Validators.required]],
      executeType: [null, [Validators.required]],
      type: [null, [Validators.required]],
      groupCode: [null, [Validators.required]],
      tenantId: [null, [Validators.required]],
      primaryProjectCode: [null],
      daemon: [false],
    };
    if (main) {
      forms = {
        ...forms,
        init: [null, [Validators.required]],
        end: [null, [Validators.required]],
        manualAble: [null],
        authorityPrefix: [null],
      };
    }
    if (!main) {
      const notMain = {
        ...forms,
        sourceName: [null, [Validators.required]],
        sourceEntityName: [null, [Validators.required]],
        dueDateName: [null, [Validators.required]],
        targetCode: [null],
        targetName: [null],
        mergeFields: [null],
        dueDateType: [null],
        startDate: [null],
        count: [null],
        assignAble: [null],
        merge: [null],
        pattern: [null],
        manualAble: [undefined],
      };
      this.currentProject.basicForm = this.fb.group(notMain);
    } else {
      this.currentProject.basicForm = this.fb.group(forms);
    }
  }

  async handleEdit(project: any, type?: 'isRoot' | 'notRoot', notChangeLoading: boolean = false) {
    this.isRoot = type === 'isRoot';
    if (!notChangeLoading) this.projectLoading = true;

    const res = await this.apiService.loadCurrentAppData(project.id).toPromise();

    if (!notChangeLoading) this.projectLoading = false;

    if (res.code === 0) {
      this.handleEditData(res.data || {});
      this.currentProject['meta'] = res.data;
    }
    await true;
  }

  handleEditData(data: any): void {
    this.handleLang(data);
    this.modalFlag = 'edit';
    this.isMainPro = data.executeType === 'mainline';
    this.handleSwitchForm(this.isMainPro);
    this.projectType = cloneDeep(projectType);
    this.currentProject['steps'] = cloneDeep(getProjectSteps(this.isMainPro));
    this.currentProject.formDirty = false;
    if (data.pageCode) {
      this.currentProject.pageCode = data.pageCode;
    }
    let mainForm: any = {
      code: data['code'] || null,
      name: data['name'] || null,
      description: data['description'] || null,
      init: data['init']?.code || null,
      end: data['end']?.code || null,
      executeType: data['executeType'] || null,
      type: data['type'] || null,
      groupCode: data['groupCode'] || null,
      tenantId: data['tenantId'] || null,
      primaryProjectCode: data['primaryProjectCode'] || null,
    };
    if (this.isMainPro) {
      mainForm = {
        ...mainForm,
        manualAble: !!data['manualAble'],
        authorityPrefix: !!data['authorityPrefix'],
      };
    }
    this.orignProBaseInfo = {
      code: data['code'] || null,
      init: data['init']?.code || null,
      end: data['end']?.code || null,
      groupCode: data['groupCode'] || null,
      phases: data?.phases || [],
      milestone: data.milestone === undefined ? true : data.milestone,
    };
    if (!this.isMainPro) {
      const formData = {
        ...mainForm,
        sourceEntityName: data['sourceEntityName'] || null,
        sourceName: data['sourceName'] || null,
        dueDateName: data['dueDateName'] || null,
        targetCode: data?.target?.code || null,
        targetName: data?.target?.name || null,
        mergeFields: data['mergeFields'] || [],
        dueDateType: data?.dueDateTimeDistance?.type || null,
        startDate: data?.dueDateTimeDistance?.startDate || '',
        count: data?.dueDateTimeDistance?.count || '',
        assignAble: !!data?.assignConfig?.['assignAble'],
        merge: !!data['merge'],
        pattern: data?.pattern || null,
      };
      this.currentProject.basicForm.patchValue(formData);
    } else {
      this.currentProject.basicForm.patchValue(mainForm);
    }
    this.currentProject.basicForm.get('code').disable();
    this.currentProject = {
      ...this.currentProject,
      presetVariables: data?.presetVariables || [],
      atmcDatas: data?.atmcDatas || [],
      selectedTask: data?.subTasks || [],
      executor: data?.executor,
      personInCharge: data?.executor
        ? {}
        : {
            choosePolicy: data?.personInCharge?.choosePolicy || '',
            processType: data?.personInCharge?.processType || '',
            identities: data?.personInCharge?.identities || [],
          },
      phases: data?.phases || [], // patchValue
      milestone: data.milestone || false,
      assignConfig: data?.assignConfig ?? {},
      sourceDetailAction: {
        actionId: data?.sourceDetailAction?.actionId,
        actionName: data?.sourceDetailAction?.actionName,
        type: data?.sourceDetailAction?.type,
        actionParams: data?.sourceDetailAction?.actionParams || [],
      },
      projectTypeData: this.projectType,
      // 2024-09-25 特殊情况，当先打开一个存在pageCode的解决方案，取消，再打开一个不存在pageCode的解决方案，这种赋值方式会造成上一个pageCode留存到没有pageCode的解决方案
      pageCode: data.pageCode,
    };
  }

  handleLang(data: any): void {
    const language = this.languageService?.currentLanguage || 'zh_CN';
    const format = (key: any): any => {
      const lang = {
        zh_CN: data?.lang?.[key]?.zh_CN || '',
        zh_TW: data?.lang?.[key]?.zh_TW || '',
        en_US: data?.lang?.[key]?.en_US || '',
      };
      if (!lang[language]) {
        lang[language] = data[key] || '';
      }
      return lang;
    };
    this.currentProject['lang'] = {
      ...(data.lang || {}),
      name: format('name'),
      description: format('description'),
      sourceName: format('sourceName'),
      dueDateName: format('dueDateName'),
    };
    this.currentProject['targetLang'] = {
      ...(data.target?.lang || {}),
      name: {
        zh_CN: data?.target?.lang?.name?.zh_CN || '',
        zh_TW: data?.target?.lang?.name?.zh_TW || '',
        en_US: data?.target?.lang?.name?.en_US || '',
      },
    };
  }

  async getPorjectList() {
    const res = await this.apiService.loadAppData(this.appCode).toPromise();
    const { children = [] } = (res.data || []).find((s) => s.id === this.appCode) || {};
    this.originalData = JSON.parse(JSON.stringify(children));
  }

  ngOnDestroy(): void {
    this.statusListUpdate$.unsubscribe();
  }
}
