<div class="app-task-push-data">
  <form nz-form class="content" [formGroup]="dataFormGroup">
    <div formArrayName="atmcDatas" *ngIf="this.dataFormGroup.get('atmcDatas')?.length">
      <div
        *ngFor="let data of dataFormGroup.get('atmcDatas').controls; let i = index"
        class="push-item"
        [formGroupName]="i"
      >
        <i class="delete" nz-icon nzType="minus-circle" nzTheme="fill" (click)="handleDeletePush(i)"></i>
        <nz-form-item class="nz-form-item-content">
          <nz-form-control [nzAutoTips]="errorTips">
            <div class="atmc" *ngIf="data.get('proVarKey')">
              <div class="item-title">
                {{ 'dj-流程变量名' | translate }}
                <span class="item-required">*</span>
              </div>
              <app-modal-input
                [attr]="{
                  name: '流程变量名',
                  required: true
                }"
                style="width: 100%"
                formControlName="proVarKey"
                [value]="data.get('proVarKey')?.value"
                ngDefaultControl
                [innerLabel]="false"
                [nzDisabled]="isReference"
                (callBack)="handlePushData(i, 'proVarKey', $event)"
              >
              </app-modal-input>
            </div>
          </nz-form-control>
        </nz-form-item>
        <nz-form-item class="nz-form-item-content">
          <nz-form-control [nzAutoTips]="errorTips">
            <div class="atmc" *ngIf="data.get('athenaKey')">
              <div class="item-title">
                {{ 'dj-athena变量名' | translate }}
                <span class="item-required">*</span>
              </div>
              <app-modal-input
                [attr]="{
                  name: 'athena变量名',
                  required: true,
                  needLang: false
                }"
                style="width: 100%"
                formControlName="athenaKey"
                [value]="data.get('athenaKey')?.value"
                ngDefaultControl
                [innerLabel]="false"
                [nzDisabled]="isReference"
                (callBack)="handlePushData(i, 'athenaKey', $event)"
              >
              </app-modal-input>
            </div>
          </nz-form-control>
        </nz-form-item>
      </div>
    </div>
  </form>
  <div class="add-push">
    <button class="button" nz-button nzType="default" (click)="handleAddPush()">{{ 'dj-新增参数' | translate }}</button>
  </div>
</div>
