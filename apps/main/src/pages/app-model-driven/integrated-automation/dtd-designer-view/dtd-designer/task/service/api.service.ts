import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { SystemConfigService } from 'common/service/system-config.service';
import { Observable } from 'rxjs';
import { GlobalService } from 'common/service/global.service';
import { DtdDesignerViewService } from '../../../service/dtd-designer-view.service';

@Injectable()
export class ApiService {
  adesignerUrl: string;

  /* 以下用于模板管理： */
  headerCofig = {}; // 用于保存请求头
  applicationCodeProxy = null; // 解决方案code

  constructor(
    private http: HttpClient,
    private configService: SystemConfigService,
    public dtdDesignerViewService: DtdDesignerViewService,
  ) {
    this.configService.get('adesignerUrl').subscribe((url) => {
      this.adesignerUrl = url;
    });
  }

  /**
   * 获取任务列表
   * @param application
   * @param groupCode
   * @returns
   */
  fetchTasks(application: string, groupCode: string = ''): Observable<any> {
    return this.http.get(`${this.adesignerUrl}/athena-designer/task/getTaskList`, {
      params: { application, groupCode },
      headers: Object.assign({}, this.headerCofig, this.dtdDesignerViewService.adpVersionHeaders),
    });
  }

  deleteTaskTree(param: any): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/task/deleteTask/${param}`;
    return this.http.get(url, { headers: this.dtdDesignerViewService.adpVersionHeaders });
  }

  /**
   * 保存任务个案
   * @param param
   * @returns
   */
  copyTaskRule(param: any): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/task/copyTask`;
    return this.http.post(url, param, { headers: this.headerCofig });
  }

  // TAG转DSL, 回溯DSL为TAG, recover为true时表示回溯dsl为tag
  transformTag(params): Observable<any> {
    const url = !params.recover
      ? `${this.adesignerUrl}/athena-designer/tagTransform/taskTagTransformDsl`
      : `${this.adesignerUrl}/athena-designer/tagTransform/taskUiBootRecover`;
    return this.http.get(url, { params: { code: params.code } });
  }

  setPageModel(param: any): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/task/setPageModel`;
    return this.http.post(url, param);
  }
}
