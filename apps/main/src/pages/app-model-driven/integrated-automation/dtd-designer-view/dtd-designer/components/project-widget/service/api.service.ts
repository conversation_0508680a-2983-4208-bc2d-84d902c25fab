import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { SystemConfigService } from 'common/service/system-config.service';
import { GlobalService } from 'common/service/global.service';
import { Observable } from 'rxjs';
import { DtdDesignerViewService } from 'pages/app-model-driven/integrated-automation/dtd-designer-view/service/dtd-designer-view.service';

@Injectable()
export class ApiService {
  adesignerUrl: string;

  constructor(
    private http: HttpClient,
    private configService: SystemConfigService,
    public dtdDesignerViewService: DtdDesignerViewService,
  ) {
    this.configService.getConfig().subscribe((config) => {
      this.adesignerUrl = config.adesignerUrl;
    });
  }

  loadDataStates(param: any): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/data/findDataStatesByApplication`;
    return this.http.get(url, { params: param, headers: this.dtdDesignerViewService.adpVersionHeaders });
  }

  loadDataGroupsByApplication(param: any): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/data/dataGroup/listByApplication${param}`;
    return this.http.get(url, { headers: this.dtdDesignerViewService.adpVersionHeaders });
  }

  // 根据数据状态查询关联任务
  loadTasksByDataState(param): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/task/getTaskList`;
    return this.http.post(url, param, { headers: this.dtdDesignerViewService.adpVersionHeaders });
  }

  // 获取当前app对应数据
  loadCurrentAppData(param: any): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/project/getProject/${param}`;
    return this.http.get(url, { headers: this.dtdDesignerViewService.adpVersionHeaders });
  }

  // 获取appData
  loadAppData(param: any): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/project/projectTree/${param}`;
    return this.http.get(url, { headers: this.dtdDesignerViewService.adpVersionHeaders });
  }
}
