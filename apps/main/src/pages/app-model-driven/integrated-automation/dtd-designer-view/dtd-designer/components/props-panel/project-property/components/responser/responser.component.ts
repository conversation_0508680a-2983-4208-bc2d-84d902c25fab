import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { AdModalService } from 'components/ad-ui-components/ad-modal/ad-modal.service';
import { size } from 'lodash';

@Component({
  selector: 'app-responser',
  templateUrl: './responser.component.html',
  styleUrls: ['./responser.component.less'],
})
export class ResponserComponent implements OnInit {
  @Input() currentProject: any;

  @Output() patchValue = new EventEmitter<any>();
  @Output() update = new EventEmitter<any>();
  @Output() validChanged = new EventEmitter<boolean>();

  public choosePolicy = [
    { value: 'single', label: this.translate.instant('dj-单人') },
    // { value: 'all', label: this.translate.instant('dj-多人') },
  ];

  public performerType = [
    { value: 'user', label: this.translate.instant('dj-用户') },
    { value: 'duty', label: this.translate.instant('dj-职能') },
    {
      value: 'deptDirector',
      label: this.translate.instant('dj-部门主管'),
    },
    {
      value: 'deptUser',
      label: this.translate.instant('dj-部门人员'),
    },
    {
      value: 'personInCharge',
      label: this.translate.instant('dj-当责者'),
    },
    { value: 'activity', label: this.translate.instant('dj-活动') },
  ];

  constructor(
    public translate: TranslateService,
    private modal: AdModalService,
    private translateService: TranslateService,
  ) {}

  // 当责者数据
  handleChargeData(key: any, data: any, index?: number): void {
    if ([undefined, null].includes(index)) {
      this.currentProject.personInCharge[key] = data.value;
    } else {
      this.currentProject.personInCharge.identities[index][key] = data.value;
    }
    if (this.currentProject.formDirty) {
      this.handleValidate();
    }
  }

  // 异常排除数据
  handleExceptionLang(key: any, data: any, index?: number): void {
    if ([undefined, null].includes(index)) {
      this.currentProject[key] = data.value;
    } else {
      this.currentProject.presetVariables[index][key] = data.value;
      if (data.needLang) {
        this.currentProject.presetVariables[index].lang = {
          ...(this.currentProject.presetVariables[index].lang || {}),
          [key]: data.lang,
        };
      }
    }
    if (this.currentProject.formDirty) {
      this.handleValidate();
    }
  }

  // 增加当责者
  handleAddCharge(): void {
    this.currentProject.personInCharge.identities.push({
      isBusiness: false,
      performerType: '',
      performerName: '',
      performerVariable: '',
      performerValue: '',
    });
  }

  // 增加业务执行人
  handleAddBusinessExecutor() {
    this.currentProject.presetVariables.push({
      performerType: 'user',
      performerName: '',
      performerValue: '',
    });
  }

  // 删除当责者
  handleDeleteCharge(index: any): void {
    this.currentProject.personInCharge.identities.splice(index, 1);
  }

  // 删除业务执行人
  handleDeleteBusinessExecutor(index: any): void {
    this.currentProject.presetVariables.splice(index, 1);
  }

  // 业务执行人数据
  handleBusinessExecutorData(key: any, data: any, index?: number): void {
    if (key === 'lang') {
      this.currentProject.presetVariables[index][key] = {
        performerName: data,
      };
    } else {
      this.currentProject.presetVariables[index][key] = data.value;
    }
    if (this.currentProject.formDirty) {
      this.handleValidate();
    }
  }

  // 校验
  handleValidate(): boolean {
    let validate = true;
    const executor = this.currentProject.executor;
    if (executor) {
      const source = executor?.source;
      // 使用了新的当责者
      if (executor.type !== 'default') {
        if (source === 'personnel') {
          if (size(executor.personnel) === 0) {
            validate = false;
          }
        } else if (source === 'variable') {
          if (size(executor.variable) === 0) {
            validate = false;
          }
        } else if (source === 'activity') {
          if (size(executor.activity) === 0) {
            validate = false;
          }
        } else if (source === 'formFiled') {
          if (size(executor.formFiled) === 0) {
            validate = false;
          }
        } else if (source === 'department') {
          if (size(executor.department) === 0) {
            validate = false;
          }
        }
      }
    } else {
      const { choosePolicy: policy, identities = [] } = this.currentProject.personInCharge;
      this.currentProject.personInCharge['errorKeys'] = '';
      if ([undefined, null, ''].includes(policy)) {
        validate = false;
        this.currentProject.personInCharge['errorKeys'] = 'choosePolicy';
      }
      identities.forEach((s) => {
        const { isBusiness, performerType, performerName, performerVariable, performerValue } = s;
        s['errorKeys'] = [];
        if ([undefined, null, ''].includes(performerType)) {
          s['errorKeys'].push('performerType');
          validate = false;
        }
        if ([undefined, null, ''].includes(performerName)) {
          s['errorKeys'].push('performerName');
          validate = false;
        }
        if ([undefined, null, ''].includes(performerValue)) {
          s['errorKeys'].push('performerValue');
          validate = false;
        }
        if (!!isBusiness) {
          if ([undefined, null, ''].includes(performerVariable)) {
            s['errorKeys'].push('performerVariable');
            validate = false;
          }
        }
      });
    }
    this.currentProject.presetVariables.forEach((s) => {
      const { performerType, performerName, performerValue } = s;
      s['errorKeys'] = [];
      if ([undefined, null, ''].includes(performerType)) {
        s['errorKeys'].push('performerType');
        validate = false;
      }
      if ([undefined, null, ''].includes(performerName)) {
        s['errorKeys'].push('performerName');
        validate = false;
      }
      if ([undefined, null, ''].includes(performerValue)) {
        s['errorKeys'].push('performerValue');
        validate = false;
      }
    });
    this.validChanged.emit(validate);

    return validate;
  }

  ngOnInit() {}

  handleExchange(e: MouseEvent) {
    e.stopPropagation();
    this.modal.confirm({
      nzTitle: this.translateService.instant('dj-切换提示'),
      nzOnOk: () => {
        this.currentProject.personInCharge = {};
        this.currentProject.presetVariables = [];
        this.currentProject.executor = {
          type: 'default',
          personnel: [],
          source: 'personnel',
        };
      },
      nzOnCancel: () => {},
    });
  }

  handlePeopleSettingChange(data) {
    const { executor } = data;
    this.currentProject.executor = executor;
  }
}
