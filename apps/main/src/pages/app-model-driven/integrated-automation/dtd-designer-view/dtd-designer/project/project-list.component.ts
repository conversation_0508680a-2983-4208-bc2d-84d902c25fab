import { Component, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';
import { ApiService } from './service/api.service';
import { to } from 'common/utils/core.utils';
import { AppService } from 'pages/apps/app.service';
import { ProjectService } from './service/project.service';
import { IntroService } from 'common/service/intro/intro.service';
import { TranslateService } from '@ngx-translate/core';
import { AdModalService } from 'components/ad-ui-components/ad-modal/ad-modal.service';
import { NzMessageService } from 'ng-zorro-antd/message';
import { cloneDeep, delay, isEmpty } from 'lodash';
import { CaseModalComponent } from 'components/bussiness-components/case-modal/case-modal.component';
import { ProjectWidgetComponent } from '../components/project-widget/project-widget.component';
import { TaskListComponent } from '../task/task-list.component';
import { GraphComponent } from '../graph/graph.component';
import { DtdDesignerViewService } from '../../service/dtd-designer-view.service';
import { DtdDesignerComponent } from '../dtd-designer.component';
import { ActivatedRoute, Router } from '@angular/router';

@Component({
  selector: 'dtd-designer-project-list',
  exportAs: 'driveExecutionProject',
  templateUrl: './project-list.component.html',
  styleUrls: ['./project-list.component.less'],
  providers: [ProjectService],
})
export class ProjectListComponent implements OnInit {
  @Input() graphComponentRef: GraphComponent;
  @Input() taskComponentRef: TaskListComponent;
  @Input() dtdDesignerRef: DtdDesignerComponent;
  @Output() afterDeleted = new EventEmitter<any>();
  @ViewChild('projectWidget') projectWidgetRef: ProjectWidgetComponent;
  @ViewChild('caseModalRef') caseModalRef: CaseModalComponent;

  loading: boolean = true;
  originProjectList: any[] = []; // 原始项目列表
  projectList: any[] = []; // 用于渲染的项目列表
  selectedProject: any = null;
  projectModalVisible: boolean = false; // 项目开窗
  projectWidgetType: 'isRoot' | 'notRoot'; // 项目类型
  modalTitle: any; // 开窗标题
  projectLoading: any; // 开窗loading
  workVisible: any; // 界面设计
  workData: any; // 任务界面设计数据
  isMobile: boolean = false; // 是否是移动端
  descriptionLang: any; //项目介绍
  caseModalVisible: boolean = false; // 用例开窗
  cardJsonVisible: boolean; // 项目卡自定义开窗是否可见
  caseData: any; // 个案开窗需要的数据
  projectWidgetFlag: string;
  params: any = {}; // 新增子项目参数
  isMainPro: boolean; // 是否是主项目
  isHighlightSuccess: boolean; // 当前高亮是否成功
  validMap: Map<string, boolean> = new Map(); // 存放项目校验结果
  isGraphInitComplete: boolean = false; // 画布是否初始化渲染完成
  routeParams: any = {}; // 路由参数

  getSelectedProject() {
    return this.selectedProject;
  }

  constructor(
    private apiService: ApiService,
    private appService: AppService,
    private projectService: ProjectService,
    public introService: IntroService,
    private translate: TranslateService,
    private message: NzMessageService,
    public adModalService: AdModalService,
    private dtdDesignerViewService: DtdDesignerViewService,
    private route: ActivatedRoute,
  ) {}

  async ngOnInit() {
    this.route.queryParams.subscribe((params) => {
      this.routeParams = params;
    });
    // // 请求项目列表
    // await this.fetchProjects();
    // // 根据 projectCode 自动选中项目
    // this.highlightProject();
  }

  async initPage() {
    // 请求项目列表
    await this.fetchProjects();
    // 根据 projectCode 自动选中项目
    this.highlightProject();
  }

  /**
   * 请求项目列表
   */
  async fetchProjects() {
    this.loading = true;
    const appCode = this.appService.selectedApp?.code;
    const [err, res] = await to(this.apiService.getProjectList(appCode).toPromise());

    if (res?.code === 0) {
      this.originProjectList = this.projectService.transformProjectList(res.data[0]?.children || []);
      this.handleSearch('');
    }
    this.loading = false;
  }

  /**
   * 搜索项目
   * @param searchValue
   */
  handleSearch(searchValue: string): void {
    const searchValueTrim = searchValue.trim();
    // 搜索结果
    this.projectList = this.projectService.filterProjects(searchValueTrim, this.originProjectList);
  }

  /**
   * 点击列表项
   * @param projectData
   */
  async handleProjectClick({ e, projectData }: any) {
    e?.stopPropagation();
    if (!projectData) return;

    // 属性面板有修改时进行拦截，不允许切换
    const canPass = await this.dtdDesignerViewService.checkPropsPanelCanContinue();
    if (!canPass) return;

    // 开属性面板
    this.dtdDesignerRef?.handleOpenPanel({
      type: 'project',
      code: projectData.id,
    });

    // if (this.selectedProject?.id === projectData?.id) return;
    this.selectedProject = projectData;

    // 当前可见
    this.handleCurrentVisible(projectData);
  }

  /**
   * 点击新增项目
   */
  handleAddModal(): void {
    this.isMainPro = true;
    this.projectWidgetType = null;
    this.modalTitle = this.translate.instant('dj-新增数据预整理项目');
    this.projectWidgetFlag = 'add';
    this.projectModalVisible = true;
  }

  // 添加子项目
  handleAddChild(project: any, type) {
    this.isMainPro = false;
    this.projectWidgetType = type;
    this.modalTitle = this.translate.instant('dj-新增可追踪项目');
    this.projectWidgetFlag = 'add';
    this.params = { project, type };
    this.projectModalVisible = true;
  }

  /**
   * 当前可见
   * @param projectData
   */
  handleCurrentVisible(projectData, isShowTips = false) {
    const graphRef = this.graphComponentRef?.getGraph();
    if (!graphRef) {
      return;
    }

    // 显示当前项目下的数据&任务
    graphRef.getCells().forEach((g) => g.show());
    const result = this.projectService.findStartAndEndNode(this.graphComponentRef?.graph, projectData, isShowTips);
    this.isHighlightSuccess = result;

    // 获取选中的项目，根据选中的项目的输入输出渲染对应状态样式
    const { init, end } = this.selectedProject;
    const stateNodes = graphRef.getNodes().filter((node) => node.getData().type === 'datastate');

    stateNodes.forEach((node) => {
      const { ngArguments, ...data } = node.getData();
      // 重置状态样式
      node.setData({ ...data, ngArguments: { ...ngArguments, start_end: false } });
      // 如果当前节点是初始状态或结束状态，并且存在完整dtd则渲染样式
      if ((data.code === init.code || data.code === end.code) && this.isHighlightSuccess) {
        node.setData({ ...data, ngArguments: { ...ngArguments, start_end: true } });
      }
    });
  }

  /**
   * 取消当前可见
   * @param projectData
   * @returns
   */
  handleCancelVisible(projectData) {
    const graphRef = this.graphComponentRef?.getGraph();
    if (!graphRef) {
      return;
    }
    // 显示当前项目下的数据&任务
    graphRef.getCells().forEach((g) => g.show());
    this.isHighlightSuccess = false;

    const stateNodes = graphRef.getNodes().filter((node) => node.getData().type === 'datastate');

    stateNodes.forEach((node) => {
      const { ngArguments, ...data } = node.getData();
      if (ngArguments.start_end) {
        // 重置状态样式
        node.setData({ ...data, ngArguments: { ...ngArguments, start_end: false } });
      }
    });
  }

  /**
   * 新增子项目
   * @param projectData
   */
  async handleCreateSubProject({ e, projectData }) {
    e.stopPropagation();
    // 属性面板有修改时进行拦截，不允许切换
    const canPass = await this.dtdDesignerViewService.checkPropsPanelCanContinue();
    if (!canPass) return;
    this.handleAddChild(projectData, 'notRoot');
  }

  /**
   * 编辑
   * @param projectData
   */
  handleEdit(projectData): void {
    this.projectWidgetFlag = 'edit';
    this.params = { project: projectData, type: undefined };
    this.isMainPro = projectData?.extendFields?.executeType === 'mainline';
    this.modalTitle = this.translate.instant(this.isMainPro ? 'dj-编辑数据预整理项目' : 'dj-编辑可追踪项目');
    this.projectModalVisible = true;
  }

  /**
   * 界面设计
   * @param data
   */
  handlePageDesign(data: any) {
    this.isMobile = false;
    if (['dsl', 'new'].includes(data.extendFields?._pageModel)) {
      // new和dls都视为dsl，否则是tag模式
      // const param = {
      //   code: data.id,
      //   type: 'project',
      //   _pageModel: 'pageView',
      // };
      // this.apiService.setPageModel(param).subscribe((res) => {
      //   this.afterHandlePage({ ...res.data, _pageModel: 'pageView' });
      // });
      this.afterHandlePage({ ...data, _pageModel: 'dsl' });
      return;
    }
    this.afterHandlePage('_pageModel' in data ? data : { ...data, _pageModel: 'pageView' });
  }

  // 界面设计关闭
  handlePageClose(): void {
    this.fetchProjects();
    if (this.workData.data?.extendFields?.manualAble || this.workData.data?.manualAble) {
      this.taskComponentRef?.fetchTasks();
    }
    this.workData = null;
    this.workVisible = false;
  }

  /**
   * 移动端界面设计
   * @param projectData
   */
  handleMobilePageDesign(projectData): void {
    this.isMobile = true;
    projectData._pageModel = 'dsl';
    this.afterHandlePage('_pageModel' in projectData ? projectData : { ...projectData, _pageModel: 'pageView' });
  }

  /**
   * 项目卡自定义
   * @param projectData
   */
  handleCustomCard(projectData): void {
    const app = this.appService?.selectedApp;
    const title = {
      en_US: `${app?.lang?.name?.['en_US'] ?? app?.name}-${projectData.lang?.name?.['en_US'] ?? projectData.name}`,
      zh_CN: `${app?.lang?.name?.['zh_CN'] ?? app?.name}-${projectData.lang?.name?.['zh_CN'] ?? projectData.name}`,
      zh_TW: `${app?.lang?.name?.['zh_TW'] ?? app?.name}-${projectData.lang?.name?.['zh_TW'] ?? projectData.name}`,
    };
    this.workData = {
      title, // 解决方案名称 + 作业名
      code: projectData?.id || projectData?.code,
      category: 'Activity',
      name: projectData.name,
      project: projectData?.id || projectData?.code,
      data: {
        ...projectData,
        taskPattern: projectData?.extendFields?.taskPattern,
      },
    };
    this.cardJsonVisible = true;
  }

  /**
   * 项目卡自定义保存回调
   */
  handleJsonSave(): void {
    this.fetchProjects();
  }

  handleJsonModalCancel() {
    this.cardJsonVisible = false;
  }

  /**
   * 个案
   * @param projectData
   */
  handleCase(projectData): void {
    this.caseModalVisible = true;
    this.caseData = cloneDeep({ ...projectData, code: projectData.id });
  }

  // 保存个案
  async saveCase(data: any) {
    const params = {
      newProjectCode: data.newCode,
      newProjectName: data.newName,
      tenantIds: data.tenantIds,
      projectCode: this.caseData.id,
    };
    this.caseData = cloneDeep(data);
    this.caseData.needLocation = true;

    const [err, res] = await to(this.apiService.copyMonitorRule(params).toPromise());
    if (res.code === 0) {
      this.message.success(this.translate.instant('dj-操作成功'));
      this.fetchProjects();
    }
    this.caseModalRef.closeLoad();
  }

  /**
   * 删除
   * @param projectData
   */
  handleDelete(projectData): void {
    let loading = false;
    this.adModalService.confirm({
      nzTitle: this.translate.instant('dj-确认删除？'),
      nzWrapClassName: 'vertical-center-modal',
      nzOkText: this.translate.instant('dj-确定'),
      nzOkLoading: loading,
      nzOnOk: async () => {
        loading = true;
        const [err, res] = await to(this.apiService.deletProjectById(projectData.id).toPromise());
        if (res.code === 0) {
          this.message.success(this.translate.instant('dj-删除成功！'));
          this.fetchProjects();
          if (this.selectedProject?.id === projectData.id) {
            // 如果删除的是当前选中的项目，则清空选中项目
            this.selectedProject = null;
          }
          this.afterDeleted.emit({ type: 'project', code: projectData.id, data: projectData });
        }
        loading = false;
      },
      nzOnCancel: () => {},
    });
  }

  // 界面设计
  afterHandlePage(data: any): void {
    const app = this.appService?.selectedApp;
    const title = {
      en_US: `${app?.lang?.name?.['en_US'] ?? app?.name}-${data.lang?.name?.['en_US'] ?? data.name}`,
      zh_CN: `${app?.lang?.name?.['zh_CN'] ?? app?.name}-${data.lang?.name?.['zh_CN'] ?? data.name}`,
      zh_TW: `${app?.lang?.name?.['zh_TW'] ?? app?.name}-${data.lang?.name?.['zh_TW'] ?? data.name}`,
    };
    const descriptionLang = data?.extendFields?.descriptionLang;
    this.workData = {
      title, // 解决方案名称 + 作业名
      code: null,
      category: 'Activity',
      name: data.name,
      project: data?.id || data?.code,
      data: {
        ...data,
        taskPattern: data?.extendFields?.taskPattern,
      },
      descriptionLang,
    };
    this.workVisible = true;
    this.descriptionLang = descriptionLang;
  }

  // 项目设定弹窗关闭
  handleModalClose(info): void {
    const { data, flag } = info;
    this.fetchProjects();
    if (flag === 'add' && data?.manualAble) {
      // 如果新增的是手动项目，则需刷新任务列表
      this.taskComponentRef?.fetchTasks();
    }
    this.introService.play('dtdDriveExecutionStep');
    this.projectModalVisible = false;
    this.projectWidgetType = null;
    this.params = {};
  }

  /**
   * 设置项目是否验证通过
   * 供外使用
   * @param projectCode
   */
  public setProjectValid(projectCode, valid) {
    if (valid) {
      this.validMap.delete(projectCode);
      return;
    }
    const projectData = this.projectList.find((item) => item.id === projectCode);
    this.validMap.set(projectCode, projectData);
  }

  /**
   * 获取路由参数
   * 根据 projectCode 自动选中项目
   */
  highlightProject() {
    const { projectCode } = this.routeParams;
    if (projectCode && this.isGraphInitComplete && this.projectList.length > 0) {
      let projectData = this.projectService.getProjectByCode(projectCode, this.originProjectList);
      // 代表切换了多版本， 透传太深，这里因为场景特殊巧取一下
      if (isEmpty(projectData)) {
        projectData = cloneDeep(this.originProjectList)?.[0];
      }
      this.handleProjectClick({ projectData });
    }
  }

  /**
   * 提供给画布使用
   * 用于通知本组件画布加载完成
   */
  public emitGraphComplete() {
    this.isGraphInitComplete = true;
    this.highlightProject();
  }
}
