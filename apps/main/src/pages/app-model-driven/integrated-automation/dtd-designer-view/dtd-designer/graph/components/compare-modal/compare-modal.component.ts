import { Component, Input, OnInit, AfterViewInit, Output, EventEmitter, ElementRef, ViewChild } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { NzMessageService } from 'ng-zorro-antd/message';
import { GraphService } from '../../service/graph.service';
import { AppService } from 'pages/apps/app.service';
import { AutoLayoutService } from '../../service/auto-layout.service';
import { Graph } from '@antv/x6';

@Component({
  selector: 'app-compare-modal',
  templateUrl: './compare-modal.component.html',
  styleUrls: ['./compare-modal.component.less'],
})
export class CompareModalComponent implements AfterViewInit {
  @Input() visible: boolean;
  @Input() compareData: any;
  @Output() close = new EventEmitter();

  @ViewChild('graphContainer') containerRef!: ElementRef;

  graph!: Graph;

  tipNode = [
    {
      text: 'dj-删除节点',
      type: 'delete',
    },
    {
      text: 'dj-新增节点',
      type: 'add',
    },
  ];

  constructor(
    private message: NzMessageService,
    private translateService: TranslateService,
    public graphService: GraphService,
    private appService: AppService,
    private autoLayoutService: AutoLayoutService,
  ) {}

  ngAfterViewInit(): void {
    const container = this.containerRef.nativeElement;
    this.graph = new Graph({
      container: container!,
      grid: {
        size: 10,
        visible: true,
        type: 'doubleMesh',
        args: [
          {
            color: '#e6f7ff',
            thickness: 1,
          },
        ],
      },
      panning: {
        enabled: true,
        eventTypes: ['mouseWheel', 'leftMouseDown'],
      },
      autoResize: true, // 监听容器大小改变，并自动更新画布大小
      virtual: false,
      async: false,
      mousewheel: {
        enabled: true,
        zoomAtMousePosition: true,
        modifiers: 'ctrl',
        minScale: 0.5,
        maxScale: 2,
      },
      highlighting: {
        magnetAvailable: {
          name: 'stroke',
          args: {
            padding: 4,
            attrs: {
              'stroke-width': 2,
              stroke: '#F7F8FE',
            },
          },
        },
      },
    });
    this.handleInit();
  }

  handleInit() {
    const { graph, dataStateTaskInfo, effectGraph, effectDataStateTaskInfo } = this.compareData;
    // 对比画布数据差异
    if (!this.compareData.graph?.cells) {
      this.compareData.graph = {
        ...(this.compareData?.graph || {}),
        cells: [],
      };
    }
    this.compareData.graph.cells = this.handleCompareGraphData(effectGraph, graph);
    // 对比数据状态任务信息差异
    this.compareData.dataStateTaskInfo = this.handleCompareVersionData(
      effectDataStateTaskInfo,
      dataStateTaskInfo,
      'dataStateTaskInfo',
    );
    this.compareData.graph?.cells?.forEach(this.graphService.intercept); // 对原数据处理
    this.dtdLayout();
  }

  /**
   * 对比画布布局
   */
  dtdLayout() {
    const autoLayoutNodes = this.compareData.dataStateTaskInfo.nodes || [];
    const graphCells = this.compareData.graph.cells || [];
    const isReference = this.appService.selectedApp?.tag?.sourceComponent === 'BC'; // 是否是引用应用
    if (isReference) {
      // 引用的DTD，以data.dataStateTaskInfo数据为准，
      const normalNodes = []; // 引用dtd中可正常渲染的节点（这类节点是开发平台用户保存过的）
      const incompleteNodes = []; // 引用dtd中不完整节点（这类节点是中台新推的,开发平台未保存过的)

      autoLayoutNodes.forEach((node) => {
        const type = node.labels[0].toLocaleLowerCase();
        const matchNode = graphCells.find(
          (n) => n?.data?.code === `${node?.properties?.code}` && type === n.data?.type,
        );

        if (matchNode) {
          // 若引用节点在本地中存在，表示之前用户在本地保存过引用节点，则使用本地节点样式及坐标
          const nodeProps = this.autoLayoutService.referenceToNormal(node, matchNode);
          normalNodes.push(nodeProps);
        } else {
          // 否则走自动布局
          incompleteNodes.push(node);
        }
      });

      // 布局
      this.autoLayoutService.autoLayout(
        {
          nodes: incompleteNodes,
          normalNodes: normalNodes,
          relationships: this.compareData.dataStateTaskInfo.relationships,
        },
        this.graph,
      );
      setTimeout(() => {
        this.graph.centerContent();
      }, 100);
      return;
    }

    if (!this.compareData.graph?.autoLayout) {
      this.autoLayoutService.autoLayout(
        {
          nodes: this.compareData.dataStateTaskInfo.nodes || [],
          normalNodes: [],
          relationships: this.compareData.dataStateTaskInfo.relationships,
        },
        this.graph,
      );
    } else {
      this.graph.fromJSON({ cells: this.compareData.graph.cells || [] });
    }
    console.log(this.graph.getNodes());
    setTimeout(() => {
      this.graph.centerContent();
    }, 100);
  }

  /**
   * 对比graph数据
   * @param effectGraph
   * @param draftGraph
   * @returns
   */
  handleCompareGraphData(effectGraph, draftGraph) {
    const isNodeWithData = (cell) => {
      return cell.shape === 'ng-task-node' || cell.shape === 'ng-data-state-node';
    };
    const effect: { nodes: any[]; relationships: any[] } = {
      nodes: [],
      relationships: [],
    };
    effectGraph?.cells?.forEach((cell) => {
      if (isNodeWithData(cell)) {
        effect.nodes.push(cell);
      } else {
        effect.relationships.push(cell);
      }
    });

    const draft: { nodes: any[]; relationships: any[] } = {
      nodes: [],
      relationships: [],
    };
    draftGraph?.cells?.forEach((cell) => {
      if (isNodeWithData(cell)) {
        draft.nodes.push(cell);
      } else {
        draft.relationships.push(cell);
      }
    });
    const result: { nodes: any[]; relationships: any[] } = this.handleCompareVersionData(effect, draft, 'graph');
    return [...result.nodes, ...result.relationships];
  }

  /**
   * 对比两个版本的任务图数据（以 draftData 为基准），标记节点和关系的变化状态。
   *
   * nodes比较逻辑：
   * - 使用 node.properties.code 作为唯一标识；
   * - 如果 code 同时存在于 draftData 和 effectData 中 → addOrDelete = 0（不变）；
   * - 如果 code 只存在于 draftData 中 → addOrDelete = 1（新增）；
   * - 如果 code 只存在于 effectData 中 → addOrDelete = 2（被删，需复制该节点加入结果）；
   *
   * relationships比较逻辑：
   * - 只有 startNodeCode 和 endNodeCode 同时一致，relationship 才视为“相同”。
   * - 如果 startNodeCode 和 endNodeCode 同时存在于 draftData 和 effectData 中 → addOrDelete = 0（不变）；
   * - 如果 startNodeCode 和 endNodeCode  只存在于 draftData 中 → addOrDelete = 1（新增）；
   * - 如果 startNodeCode 和 endNodeCode  只存在于 effectData 中 → addOrDelete = 2（被删，需复制该关系加入结果）；
   *
   * @param {Object} effectData - 原始版本的图数据（旧版本，用于判断被删项）
   * @param {Object} draftData - 当前编辑版本的图数据（草稿版本，用于判断新增项）
   * @returns {Object} result - 包含对比后的 nodes 和 relationships，均带有 addOrDelete 状态字段：
   */

  handleCompareVersionData(
    effectData: { nodes: any[]; relationships: any[] },
    draftData: { nodes: any[]; relationships: any[] },
    type: 'graph' | 'dataStateTaskInfo',
  ) {
    const result: { nodes: any[]; relationships: any[] } = {
      nodes: [],
      relationships: [],
    };

    // ==================== 节点处理 ====================

    type NodeType = typeof draftData.nodes[number];

    const draftNodeMap: Map<string, NodeType> = new Map(
      draftData.nodes?.map((node) => [type === 'dataStateTaskInfo' ? node.properties?.code : node.data?.code, node]),
    );
    const effectNodeMap: Map<string, NodeType> = new Map(
      effectData.nodes?.map((node) => [type === 'dataStateTaskInfo' ? node.properties?.code : node.data?.code, node]),
    );

    const allNodeCodes = new Set([...draftNodeMap.keys(), ...effectNodeMap.keys()]);

    for (const code of allNodeCodes) {
      const draftNode = draftNodeMap.get(code);
      const effectNode = effectNodeMap.get(code);

      if (draftNode && effectNode) {
        if (type === 'dataStateTaskInfo') {
          result.nodes.push({ ...draftNode, addOrDelete: 0 }); // 不变
        } else {
          draftNode.data.ngArguments.addOrDelete = 0;
          result.nodes.push({ ...draftNode }); // 不变
        }
      } else if (draftNode) {
        if (type === 'dataStateTaskInfo') {
          result.nodes.push({ ...draftNode, addOrDelete: 1 }); // 新增
        } else {
          draftNode.data.ngArguments.addOrDelete = 1;
          result.nodes.push({ ...draftNode }); // 新增
        }
      } else if (effectNode) {
        if (type === 'dataStateTaskInfo') {
          result.nodes.push({ ...effectNode, addOrDelete: 2 }); // 被删
        } else {
          effectNode.data.ngArguments.addOrDelete = 2;
          result.nodes.push({ ...effectNode }); // 被删
        }
      }
    }

    // ==================== 关系处理 ====================

    type RelType = typeof draftData.relationships[number];

    const getNodeIdToCodeMap = (nodes: NodeType[]): Map<number, string> => {
      const map = new Map<number, string>();
      nodes.forEach((node) => {
        const key = type === 'dataStateTaskInfo' ? node.properties?.code : node.data?.code;
        if (node?.id != null && key) {
          map.set(node.id, key);
        }
      });
      return map;
    };

    const draftIdToCode = getNodeIdToCodeMap(draftData.nodes);
    const effectIdToCode = getNodeIdToCodeMap(effectData.nodes);

    const buildRelMap = (rels: RelType[], idToCode: Map<number, string>): Map<string, RelType> => {
      const map = new Map<string, RelType>();
      rels.forEach((rel) => {
        const startNodeCode = idToCode.get(type === 'dataStateTaskInfo' ? rel.startNode : rel.source?.cell);
        const endNodeCode = idToCode.get(type === 'dataStateTaskInfo' ? rel.endNode : rel.target?.cell);
        if (startNodeCode && endNodeCode) {
          const key = `${startNodeCode}-->${endNodeCode}`;
          map.set(key, rel);
        }
      });
      return map;
    };

    const draftRelMap = buildRelMap(draftData.relationships, draftIdToCode);
    const effectRelMap = buildRelMap(effectData.relationships, effectIdToCode);

    const allRelKeys = new Set([...draftRelMap.keys(), ...effectRelMap.keys()]);

    for (const key of allRelKeys) {
      const draftRel = draftRelMap.get(key);
      const effectRel = effectRelMap.get(key);

      if (draftRel && effectRel) {
        if (type === 'dataStateTaskInfo') {
          result.relationships.push({ ...draftRel, addOrDelete: 0 }); // 不变
        } else {
          draftRel.attrs.addOrDelete = 0;
          result.relationships.push({ ...draftRel }); // 不变
        }
      } else if (draftRel) {
        if (type === 'dataStateTaskInfo') {
          result.relationships.push({ ...draftRel, addOrDelete: 1 }); // 新增
        } else {
          draftRel.attrs.addOrDelete = 1;
          result.relationships.push({ ...draftRel }); // 新增
        }
      } else if (effectRel) {
        if (type === 'dataStateTaskInfo') {
          result.relationships.push({ ...effectRel, addOrDelete: 2 }); // 被删
        } else {
          effectRel.attrs.addOrDelete = 2;
          result.relationships.push({ ...effectRel }); // 被删
        }
      }
    }
    return result;
  }

  handleOnCancel() {
    this.close.emit();
  }

  handleOnOk() {
    this.close.emit();
  }
}
