import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { SystemConfigService } from 'common/service/system-config.service';
import { Observable } from 'rxjs';
import { GlobalService } from 'common/service/global.service';
import { DtdDesignerViewService } from '../../../service/dtd-designer-view.service';

@Injectable()
export class ApiService {
  adesignerUrl: string;

  constructor(
    private http: HttpClient,
    private configService: SystemConfigService,
    public dtdDesignerViewService: DtdDesignerViewService,
  ) {
    this.configService.get('adesignerUrl').subscribe((url) => {
      this.adesignerUrl = url;
    });
  }

  /**
   * 获取项目列表
   * @param param
   * @returns
   */
  getProjectList(appCode: string): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/project/projectTree/${appCode}`;
    return this.http.get(url, { headers: this.dtdDesignerViewService.adpVersionHeaders });
  }

  /**
   * 删除项目
   * @param id 项目id
   * @returns
   */
  deletProjectById(id: any): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/project/deleteProject/${id}`;
    return this.http.get(url, { headers: this.dtdDesignerViewService.adpVersionHeaders });
  }

  /**
   * 获取dataGroup列表
   * @param param
   * @returns
   */
  loadDataGroupsByApplication(param: any): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/data/dataGroup/listByApplication${param}`;
    return this.http.get(url, { headers: this.dtdDesignerViewService.adpVersionHeaders });
  }

  loadDataStates(param: any): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/data/findDataStatesByApplication`;
    return this.http.get(url, {
      params: param,
      headers: this.dtdDesignerViewService.adpVersionHeaders,
    });
  }

  // 获取当前app对应数据
  loadCurrentAppData(param: any): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/project/getProject/${param}`;
    return this.http.get(url, { headers: this.dtdDesignerViewService.adpVersionHeaders });
  }

  copyMonitorRule(param: any): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/project/copyProject`;
    return this.http.post(url, param);
  }

  // TAG转DSL, 回退DSL为TAG, recover为true时表示回退dsl为tag
  transformTag(params): Observable<any> {
    const url = !params.recover
      ? `${this.adesignerUrl}/athena-designer/tagTransform/projectTagTransformDsl`
      : `${this.adesignerUrl}/athena-designer/tagTransform/projectUiBootRecover`;
    return this.http.get(url, { params: { code: params.code } });
  }

  // 修改界面设计渲染模式
  setPageModel(param: any): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/task/setPageModel`;
    return this.http.post(url, param);
  }
}
