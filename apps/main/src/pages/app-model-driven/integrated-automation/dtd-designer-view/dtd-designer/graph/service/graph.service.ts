import { ComponentFactoryResolver, ElementRef, Injectable, Injector } from '@angular/core';
import { Cell, Edge, Graph, Node, Shape } from '@antv/x6';
import { Keyboard } from '@antv/x6-plugin-keyboard';
import { Scroller } from '@antv/x6-plugin-scroller';
import { Snapline } from '@antv/x6-plugin-snapline';
import { Selection } from '@antv/x6-plugin-selection';
import { History } from '@antv/x6-plugin-history';
import { Clipboard } from '@antv/x6-plugin-clipboard';
import { Export } from '@antv/x6-plugin-export';
import { NzMessageService } from 'ng-zorro-antd/message';
import { TranslateService } from '@ngx-translate/core';
import {
  addButtonPosition,
  addNgButtonPosition,
  commonConnector,
  commonEdgeAttrs,
  dataStateNodeStyles,
  edgeAttrs,
  nodeIcon,
  NodePort,
  taskNodeStyles,
} from '../utils/graph.util';
import { cloneDeep, isEqual, parseInt } from 'lodash';
import { to } from 'common/utils/core.utils';
import { ApiService } from './api.service';
import { AppService } from 'pages/apps/app.service';
import { UUID } from 'angular2-uuid';
import { taskTypeMap } from '../../task/utils';
import { ComponentRefKeys, ComponentType, NgNodeTypes } from '../types';
import { AdModalService } from 'components/ad-ui-components/ad-modal/ad-modal.service';
import { NgTaskNodeComponent } from '../components/ng-task-node/ng-task-node.component';
import { NgDataStateNodeComponent } from '../components/ng-data-state-node/ng-data-state-node.component';
import { BehaviorSubject, Observable } from 'rxjs';
import { distinctUntilChanged } from 'rxjs/operators';
import { register } from 'common/utils/x6-angular-shape';
import { AutoLayoutService } from './auto-layout.service';
import { AuthService } from 'common/service/auth.service';
import { DtdDesignerViewService } from '../../../service/dtd-designer-view.service';

@Injectable()
export class GraphService {
  // 输入输出数据变化订阅器
  private readonly _dataState$ = new BehaviorSubject<any>(null);
  get dataState$(): Observable<any> {
    return this._dataState$.asObservable().pipe(distinctUntilChanged());
  }

  // 组件实例映射表
  private componentRefMap = new Map();

  isGraphChanged = false;
  highlightEdges: Edge[] = []; // 高亮的边
  screenWidth: number = window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth;
  screenHeight: number = window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight;
  dtdPopoverWidth: number = 313;
  dtdPopoverHeight: number = 456;
  descriptionCode = UUID.UUID().replace(/-/g, '').toLocaleLowerCase();
  hideToolsTimers: any = {};
  taskOutputMap: Map<string, object> = new Map(); // 所有任务的output
  favouriteCode: string;
  panelParams: any; // 当前打开的面板信息

  constructor(
    private message: NzMessageService,
    private appService: AppService,
    private translateService: TranslateService,
    private apiService: ApiService,
    private modal: AdModalService,
    private injector: Injector,
    private cfr: ComponentFactoryResolver,
    private autoLayout: AutoLayoutService,
    private authService: AuthService,
    private dtdDesignerService: DtdDesignerViewService,
  ) {}

  /**
   * 获取组件实例
   * @param key 组件实例的唯一标识
   * @returns
   */
  public getComponentRef(key?: ComponentRefKeys): ComponentType {
    return this.componentRefMap.get(key);
  }

  /**
   * 设置组件实例
   * @param key 组件实例的唯一标识
   * @param componentInstance 组件实例
   */
  public setComponentRef(key: ComponentRefKeys, componentInstance: ComponentType): void {
    if (key?.trim() && !this.componentRefMap.has(key)) {
      this.componentRefMap.set(key, componentInstance);
    }
  }

  /**
   * 删除组件实例，若key为空则清空所有组件实例
   * @param key 组件实例的唯一标识
   * @returns
   */
  public removeComponentRef(key?: ComponentRefKeys): void {
    if (key?.trim() && this.componentRefMap.has(key)) {
      this.componentRefMap.delete(key);
      return;
    }
  }

  /**
   * 清除所有组件映射
   */
  public clearComponentRefs() {
    this.componentRefMap.clear();
  }

  /**
   * 初始化配置Graph对象
   * @param container
   * @returns
   */
  initGraph(container, graphWrapper): Graph {
    const graph = new Graph({
      container: container,
      grid: {
        size: 10,
        visible: true,
        type: 'doubleMesh',
        args: [
          {
            color: '#e6f7ff',
            thickness: 1,
          },
        ],
      },
      panning: {
        enabled: true,
        eventTypes: ['mouseWheel', 'leftMouseDown'],
      },
      autoResize: true, // 监听容器大小改变，并自动更新画布大小
      virtual: false,
      async: false,
      mousewheel: {
        enabled: true,
        zoomAtMousePosition: true,
        modifiers: 'ctrl',
        minScale: 0.5,
        maxScale: 2,
      },
      highlighting: {
        magnetAvailable: {
          name: 'stroke',
          args: {
            padding: 4,
            attrs: {
              'stroke-width': 2,
              stroke: '#F7F8FE',
            },
          },
        },
      },
      connecting: {
        allowBlank: false,
        allowLoop: false,
        allowMulti: false,
        createEdge() {
          return new Shape.Edge({
            ...commonEdgeAttrs,
            router: {
              name: 'orth',
            },
          });
        },
        validateConnection: ({ sourceView, targetView, sourceMagnet, targetMagnet }) => {
          if (sourceView === targetView) {
            return false;
          }
          if (!sourceMagnet) {
            return false;
          }
          return true;
        },
        validateEdge: ({ edge, type, previous }) => {
          const source = graph.getCellById(edge.source['cell']);
          const target = graph.getCellById(edge.target['cell']);
          const sourceType = source?.data?.type;
          const endType = target?.data?.type;
          if (sourceType === 'datastate' && endType === 'datastate') {
            this.message.error(this.translateService.instant('dj-请连接至任务项！'));
            return false;
          }
          if (sourceType === 'task' && endType === 'task') {
            this.message.error(this.translateService.instant('dj-禁止任务直接关联！'));
            return false;
          }
          // 找出重覆的edge
          const edges = graph.getEdges().filter((_edge) => {
            return edge.source['cell'] === _edge.source['cell'] && edge.target['cell'] === _edge.target['cell'];
          });
          return edges.length <= 1;
        },
      },
    });

    graph
      .use(new Snapline({ enabled: true }))
      .use(new Keyboard({ enabled: true }))
      .use(
        new Selection({
          enabled: true,
          multiple: true,
          rubberband: true,
          rubberEdge: true,
          movable: true,
          showNodeSelectionBox: true,
          modifiers: ['shift'],
        }),
      )
      .use(new History({ enabled: false }))
      .use(new Clipboard({ enabled: true }))
      .use(new Export());

    // 注册Angular自定义节点
    this.registerNgNodes();

    return graph;
  }

  /**
   * 注册Angular自定义节点
   */
  registerNgNodes() {
    register({
      shape: NgNodeTypes.TASK_NODE,
      content: NgTaskNodeComponent,
      injector: this.injector,
      cfr: this.cfr,
    });
    register({
      shape: NgNodeTypes.DATA_STATE_NODE,
      content: NgDataStateNodeComponent,
      injector: this.injector,
      cfr: this.cfr,
    });
  }

  /**
   * 创建节点
   * @param graph
   * @param data
   */
  createNode(graph: Graph, data: any, type: 'task' | 'datastate') {
    let nodeProps = {
      task: cloneDeep(taskNodeStyles),
      datastate: cloneDeep(dataStateNodeStyles),
    };
    if (!nodeProps[type]) return;

    const node: any = nodeProps[type];
    const ngArguments = { name: data.name, lang: data?.lang };

    node['data'] = { type, code: data.code };
    if (type === 'datastate') {
      node['data']['dataCode'] = data.dataCode;
    }
    if (type === 'task') {
      let taskType = this.getTaskType(data);
      // 设置图标
      ngArguments['icon'] = nodeIcon[taskType || data.adpType];
      // 设置任务类型
      ngArguments['taskCategory'] = data.adpType || data.driveType || 'flow';
      // 任务的类型
      node.data['taskCategory'] = data.adpType || data.driveType;
    }

    node.data['ngArguments'] = ngArguments;

    return graph.createNode(node);
  }

  /**
   * 保存画布
   * @param graph
   * @param isNeesTips 是否需要保存后提示
   * @param isChangeVersionCanNotSave  切换版本的时候 不可以调保存版本的接口
   */
  async saveGraph(graph: Graph, isNeesTips: boolean = true, isChangeVersionCanNotSave: boolean = false) {
    const authority = this.authService.getAuth('update');
    if (!authority || isChangeVersionCanNotSave) return;

    if (this.favouriteCode) return;
    // 去除线高亮效果
    (this.highlightEdges || []).forEach((edge: Edge) => this.toggleEdgeHeiglight(edge, false));
    // 将所有cell的Visible属性设置为true，并清空tools属性
    const graphJson = this.cleanGraphJson(graph.toJSON());
    console.log('ccccc: ', graphJson);
    const { groupCode } = this.appService.selectedApp || {};
    const [err, res] = await to(
      this.apiService
        .saveGraphRelation({ code: groupCode, relationshipData: { cells: graphJson.cells, autoLayout: true } })
        .toPromise(),
    );
    if (res?.code === 0) {
      isNeesTips && this.message.success(this.translateService.instant('dj-保存成功！'));
      this.isGraphChanged = false;
    }
  }

  /**
   * 保存前处理graphJson数据
   * @param graphJson
   * @returns
   */
  cleanGraphJson(graphJson) {
    (graphJson.cells || []).forEach((cell: any) => {
      const { data, shape } = cell;
      const { ngArguments, type } = data || {};
      cell.visible = true;
      cell.tools = [];

      if (shape !== 'edge') cell.ports = {};
      if (ngArguments) Reflect.deleteProperty(ngArguments, 'valid');
      if (type === 'datastate' && ngArguments) Reflect.deleteProperty(ngArguments, 'start_end');
    });
    return graphJson;
  }

  /**
   * 控制edge是否高亮效果
   * @param edge
   * @param isHighlight
   */
  toggleEdgeHeiglight(edge: Edge, isHighlight: boolean = true) {
    if (isHighlight) {
      edge.setAttrByPath('line', {
        stroke: '#6A4CFF',
        strokeDasharray: 5,
        strokeWidth: 4,
        targetMarker: {
          name: 'classic',
        },
        style: {
          animation: 'ant-line 30s infinite linear',
        },
      });
      edge.setZIndex(10);
      return;
    }

    edge.setAttrByPath('line', {
      stroke: '#D5D5D5',
      strokeWidth: 2,
      strokeDasharray: 0,
      style: {
        animation: 'none',
      },
    });
    edge.removeZIndex();
  }

  /**
   * 点击节点 高亮显示输入输出的线
   * @param graph
   * @param cell
   */
  highlightEdgesByNode(graph: Graph, cell: Cell) {
    // 清除高亮效果
    if (this.highlightEdges.length) {
      this.highlightEdges.forEach((edge) => {
        this.toggleEdgeHeiglight(edge, false);
      });
      this.highlightEdges = [];
    }

    // 添加高亮效果
    const incoming_edges = graph.model.getConnectedEdges(cell, { incoming: true });
    incoming_edges.forEach((edge) => {
      this.toggleEdgeHeiglight(edge);
      this.highlightEdges.push(edge);
    });

    const outgoing_edges = graph.model.getConnectedEdges(cell, { outgoing: true });
    outgoing_edges.forEach((edge) => {
      this.toggleEdgeHeiglight(edge);
      this.highlightEdges.push(edge);
    });
  }

  /**
   * 清除高亮效果
   */
  clearHighlightEdges(): void {
    // 清除高亮效果
    if (this.highlightEdges.length) {
      this.highlightEdges.forEach((edge) => {
        this.toggleEdgeHeiglight(edge, false);
      });
      this.highlightEdges = [];
    }
  }

  /**
   * 在画布中创建任务节点
   * @param graph 画布实例
   * @param taskData 任务数据
   * @param position 节点坐标
   */
  addTaskNodeToGraph(graph: Graph, taskData, position: { x: number; y: number }, isSelect = true) {
    const node = this.createNode(graph, taskData, 'task');
    node.setPosition(position);
    graph.addNode(node);
    graph.cleanSelection();
    graph.select(node);
    graph.trigger('node:click', { cell: node });
    return node;
  }

  /**
   *
   * @param graph 画布实例
   * @param code 状态code
   * @param dataCode 状态dataCode
   * @param name 状态名称
   * @param position 节点坐标
   */
  addDataStateNodeToGraph(graph: Graph, code, dataCode, name, position: { x: number; y: number }, isSelect = true) {
    const node = this.createNode(graph, { name, code, dataCode }, 'datastate');
    node.setPosition(position);
    graph.cleanSelection();
    graph.addNode(node);
    graph.select(node);
    return node;
  }

  /**
   * 获取画布可见区中心点
   * @param graph 画布实例
   * @param graphWrapper
   * @returns
   */
  getGraphCenter(graph: Graph, graphWrapper: ElementRef<HTMLDivElement>) {
    const dom = graphWrapper.nativeElement;
    const { top, left, width, height } = dom.getBoundingClientRect();
    const viewX = width / 2 + left;
    const viewY = height / 2 + top;
    return graph.clientToLocal(viewX, viewY);
  }

  /**
   * 节点链接桩显隐
   * @param node 节点实例
   * @param isShow 是否显示
   */
  togglePortVisible(node: Node, isShow: boolean) {
    if (isShow) {
      node.setProp('ports', NodePort(node.getProp('shape') as any));
      return;
    }
    node.removePorts();
  }

  /**
   * 节点连接线
   * @param edge 边实例
   */
  async edgeConnected(edge: Edge, graph: Graph) {
    // 更新数据与 任务之间的关联
    const source = edge.getSourceCell() as Node;
    const target = edge.getTargetCell() as Node;
    const sourceType = source?.data?.type;
    const targetType = target?.data?.type;

    // 节点到节点的连接
    this.transformProtToNode(edge, source.id, target.id);

    // 更新任务的stateMap
    let stateMaps;
    if (sourceType === 'task') {
      stateMaps = this.refreshTaskStateMaps(graph, [source]);
    } else if (targetType === 'task') {
      stateMaps = this.refreshTaskStateMaps(graph, [target]);
    }
    const [err, res] = await to(this.apiService.saveTaskDataState(stateMaps).toPromise());
    if (res?.code === 0) {
      await this.saveGraph(graph);
      this.noticeDataStatesChanged(stateMaps);
    } else {
      this.message.error(this.translateService.instant('dj-连接失败'));
      graph.removeEdge(edge);
    }
  }

  /**
   * 根据 taskList 组装 taskOutputMap
   * @param taskList
   */
  transformOutput(taskList) {
    taskList.forEach((task) => {
      const { stateMaps } = task;
      if (stateMaps?.[0]?.output) {
        this.taskOutputMap.set(task.code, stateMaps[0].output);
      }
    });
  }

  /**
   * 扭转任务的output数据结构，将 key 与 value 相互替换
   * 即：{ [stateCode]: key }
   * @param taskCode
   * @returns
   */
  variantOutput(taskCode) {
    const codeMap = new Map();
    const output = this.taskOutputMap.get(taskCode);

    for (const key in output) {
      codeMap.set(output[key], key);
    }
    return codeMap;
  }

  /**
   * 更新任务节点的stateMaps
   * 如果某个状态已经在stateMaps的ouput中，则不更新；如果不在则新增
   * @param graph
   * @param taskNodes
   * @returns
   */
  refreshTaskStateMaps(graph: Graph, taskNodes: Node[]): any[] {
    const allStateMaps: any[] = [];
    taskNodes.forEach((node: Node) => {
      const { code } = node.getData();
      // 当前任务变异后的output，结构是 Map<code, key>，其中状态code作为key
      const variantOutput: Map<string, string> = this.variantOutput(code);
      let currentStateMaps: any = { code, stateMaps: [] };

      if (this.isInGraph(graph, code, 'task')) {
        const inStateNodes = graph.getNeighbors(node, { incoming: true });
        const outStateNodes = graph.getNeighbors(node, { outgoing: true });

        // 根据inStateNodes、outStateNodes组装stateMaps
        const input = inStateNodes.map((node: Node) => node.getData().code);
        const output = outStateNodes.reduce((previous: Record<string, string>, n: Node) => {
          const { code } = n.getData();
          // 如果该状态已经在stateMaps的ouput中，则不更新，仍使用旧的key；如果不在则使用key=`end${code}`
          previous[variantOutput.get(code) || `end${code}`] = code;
          return previous;
        }, {});

        if (input.length > 0) {
          currentStateMaps.stateMaps = input.map((inputCode: string) => ({ input: inputCode, output }));
        } else {
          currentStateMaps.stateMaps = [{ input: '', output }];
          if (Object.keys(output).length === 0) {
            currentStateMaps.stateMaps = [];
          }
        }
      }
      allStateMaps.push(currentStateMaps);

      // 当前任务最新的output，更新到this.taskOutputMap中
      this.taskOutputMap.set(code, currentStateMaps.stateMaps[0]?.output);
    });
    return allStateMaps;
  }

  /**
   * 设置边的source和target属性
   * @param edge 边实例
   * @param sourceId 源节点id
   * @param targetId 目标节点id
   */
  transformProtToNode(edge, sourceId, targetId) {
    edge.setProp('source', { cell: sourceId });
    edge.setProp('target', { cell: targetId });
  }

  addNodeAddTool(args, callback) {
    const { cell } = args;
    const nodeShape = cell.shape;

    cell?.removeTool('add-button');
    cell.addTools([
      // 上
      {
        name: 'add-button',
        args: {
          ...addNgButtonPosition.top[nodeShape],
          onClick: ({ view, e }: any) => {
            const node = view.cell;
            callback && callback({ e, node, direction: 'top' });
          },
        },
      },
      // 下
      {
        name: 'add-button',
        args: {
          ...addNgButtonPosition.bottom[nodeShape],
          onClick: ({ view, e }: any) => {
            const node = view.cell;
            callback && callback({ e, node, direction: 'bottom' });
          },
        },
      },
      // 左
      {
        name: 'add-button',
        args: {
          ...addNgButtonPosition.left[nodeShape],
          onClick: ({ view, e }: any) => {
            const node = view.cell;
            callback && callback({ e, node, direction: 'left' });
          },
        },
      },
      // 右
      {
        name: 'add-button',
        args: {
          ...addNgButtonPosition.right[nodeShape],
          onClick: ({ view, e }: any) => {
            const node = view.cell;
            callback && callback({ e, node, direction: 'right' });
          },
        },
      },
    ]);
  }

  /**
   * 处理popover展示坐标
   */
  handlePosition(e) {
    let { clientX, clientY } = e;
    if (clientX + this.dtdPopoverWidth > this.screenWidth) {
      clientX = this.screenWidth - this.dtdPopoverWidth;
    }
    if (clientY + this.dtdPopoverHeight > this.screenHeight) {
      clientY = this.screenHeight - this.dtdPopoverHeight;
    }
    return {
      clientX,
      clientY,
    };
  }

  /**
   * 根据节点周围加号获取要新增的节点位置
   * @param info
   * @returns
   */
  getTaskPosition(info) {
    const { node, direction } = info;
    const originPosition = node.getPosition();
    let newPosition: any = {};

    if (direction === 'top') {
      newPosition = { x: originPosition.x - 19, y: originPosition.y - 136 };
    } else if (direction === 'bottom') {
      newPosition = { x: originPosition.x - 19, y: originPosition.y + 166 };
    } else if (direction === 'left') {
      newPosition = { x: originPosition.x - 250, y: originPosition.y };
    } else if (direction === 'right') {
      newPosition = { x: originPosition.x + 210, y: originPosition.y };
    }

    return newPosition;
  }

  /**
   * 根据节点周围加号获取要新增的节点位置
   * @param info
   * @returns
   */
  getStatePosition(info) {
    const { node, direction } = info;
    const originPosition = node.getPosition();
    let newPosition: any = {};

    if (direction === 'top') {
      newPosition = { x: originPosition.x + 35, y: originPosition.y - 186 };
    } else if (direction === 'bottom') {
      newPosition = { x: originPosition.x + 35, y: originPosition.y + 150 };
    } else if (direction === 'left') {
      newPosition = { x: originPosition.x - 250, y: originPosition.y - 25 };
    } else if (direction === 'right') {
      newPosition = { x: originPosition.x + 250, y: originPosition.y - 25 };
    }

    return newPosition;
  }

  /**
   * {
        "application": "A20240305094754-athenaPaaSDesigner",
        "dataCode": "40f615e72ec546a1cc82db294288fb3a",
        "code": "9a3780ec978c3ddcf9738255fabd6bbc",
        "name": "aaa1",
        "dataFeatureSets": [],
        "condition": {
            "type": "script",
            "expression": ""
        }
      }
   * @param type
   * @param formValue
   * @returns
   */
  generateDataStateData(type: 'add' | 'edit', formValue) {
    const { name, conditionExpression, dataFeatureSets, code, dataCode, variables } = formValue;
    if (type === 'add') {
      return {
        dataDescription: {
          application: this.appService?.selectedApp?.code || '',
          code: this.descriptionCode,
          // 与后端约定的，用于匹配默认数据的分组
          groupCode: 'dtd planning tool',
          name: '默认数据',
          tenantId: 'SYSTEM', // this.userService.getUser('tenantId') || 'SYSTEM',
        },
        dataStates: [
          {
            application: this.appService?.selectedApp?.code,
            code: UUID.UUID().replace(/-/g, '').toLocaleLowerCase(),
            condition: { type: 'script', expression: conditionExpression || '' },
            dataCode: this.descriptionCode,
            dataFeatureSets: dataFeatureSets || [],
            groupCode: 'dtd planning tool',
            name,
            tenantId: 'SYSTEM', //this.userService.getUser('tenantId') || 'SYSTEM',
            variables: variables || [],
          },
        ],
      };
    }
    return {
      application: this.appService?.selectedApp?.code || '',
      dataCode: dataCode,
      code: code,
      name: name,
      dataFeatureSets: dataFeatureSets || [],
      condition: {
        type: 'script',
        expression: conditionExpression,
      },
      variables: variables || [],
    };
  }

  /**
   * 添加任务(带连线)
   * @param newNode
   */
  connectNode(graph: Graph, sourceNode: Node, newNode: Node) {
    const edge = this.addEdge(graph, { cell: sourceNode.id }, { cell: newNode.id });
    return edge;
  }

  /**
   * 添加连线
   * @param startNode
   * @param endNode
   */
  addEdge(graph: Graph, source: { cell: string }, target: { cell: string }) {
    const edge = graph.addEdge({
      source,
      target,
      ...commonEdgeAttrs,
      router: {
        name: 'orth',
      },
    });
    return edge;
  }

  // 設定刪除鈕、關聯鈕的顯示或隱藏
  nodeInputOutputDeleteTool(graph: Graph, node: Node, visible, callbackMap?: { delete; in; out }) {
    if (!visible) {
      ['delete-button', 'in-button', 'out-button'].forEach((tool: string) => {
        node.removeTool(tool);
      });
    } else {
      const offset_x = -25;
      node.addTools([
        {
          name: 'delete-button',
          args: {
            x: '100%',
            y: 0,
            offset: { x: offset_x + 42, y: -30 },
            cursor: 'pointer',
            onClick: ({ cell }) => {
              callbackMap?.delete([cell]);
            },
          },
        },
        {
          name: 'in-button',
          args: {
            x: '100%',
            y: 0,
            offset: { x: offset_x + 21, y: -30 },
            cursor: 'pointer',
            onClick: ({ cell }) => {
              callbackMap?.out(cell);
            },
          },
        },
        {
          name: 'out-button',
          args: {
            x: '100%',
            y: 0,
            offset: { x: offset_x, y: -30 },
            cursor: 'pointer',
            onClick: ({ cell }) => {
              callbackMap?.in(cell);
            },
          },
        },
      ]);
    }
  }

  /**
   * 设置tools是否可见
   * @param node
   * @param visible
   * @param callbackMap delete:fn; in:fn; out:fn;
   */
  setNodeOptionsTool(graph: Graph, node: any, visible: boolean, callbackMap?: { delete; in; out }) {
    const { id } = node;
    if (this.hideToolsTimers?.[id]) {
      clearTimeout(this.hideToolsTimers[id]);
      delete this.hideToolsTimers[id];
    }
    if (visible) {
      this.nodeInputOutputDeleteTool(graph, node, visible, callbackMap);
    } else {
      this.hideToolsTimers[id] = window.setTimeout(() => {
        this.nodeInputOutputDeleteTool(graph, node, visible, callbackMap);
      }, 300);
    }
  }

  /**
   * 删除节点操作
   * @param graph
   * @param cells
   */
  handleRemoveNodes(graph: Graph, cells: Node[]) {
    const isEffect = this.isEffectTask(graph, cells);
    return new Promise((resolve) => {
      if (cells.length > 0) {
        this.modal.confirm({
          nzTitle: this.translateService.instant(isEffect ? 'dj-删除状态tips' : 'dj-确认删除？'),
          nzContent: '',
          nzWrapClassName: 'vertical-center-modal',
          nzOkText: this.translateService.instant('dj-确定'),
          nzOnOk: async () => {
            // 按当前批量删除，批量删除选中的节点中没有包含连线，所以removeNodes方法中不包含删除线的处理
            await this.removeNodes(graph, cells);
            resolve(true);
          },
        });
      }
    });
  }

  /**
   * 判断批量删除节点有没有受影响的task
   */
  isEffectTask(graph: Graph, nodes: Node[]) {
    const stateNodes: Node[] = nodes.filter((cell) => cell.getData()?.type === 'datastate');
    const neighbors = stateNodes.map((node: Node) => graph.getNeighbors(node)).flat();
    if (neighbors.length > 0) {
      return true;
    }
  }

  /**
   * 从画布上删除节点
   * @param graph
   * @param nodes
   */
  async removeNodes(graph: Graph, nodes: Node[]) {
    // 删除节点时，更新状态节点数据
    let getVariableFlag = false;
    if (this.panelParams?.type === 'datastate') {
      // 从当前选中节点直连节点中获取是否存在删除节点
      const panelNode = graph.getNodes().find((node) => {
        const data = node.getData();
        return data.code === this.panelParams?.code;
      });
      if (panelNode) {
        // 当前选中节点的前置节点中
        let activeTaskNodes = graph.getNeighbors(panelNode, {
          incoming: true,
        });
        const activeCodes = activeTaskNodes.map((node) => node.getData().code);
        const removeCodes = nodes.map((node) => node.getData().code);
        if (activeCodes.some((code) => removeCodes.includes(code))) {
          getVariableFlag = true;
        }
      }
    }
    const taskNodesMap = new Map();
    const taskNodes: Node[] = nodes.filter((cell) => {
      if (cell.getData()?.type === 'task') {
        taskNodesMap.set(cell.getData().code, cell);
        return true;
      }
    });
    const stateNodes: Node[] = nodes.filter((cell) => cell.getData()?.type === 'datastate');
    const neighbors = stateNodes.map((node: Node) => graph.getNeighbors(node)).flat();

    // 所有要更新stateMaps的任务(包括删除的、被删除状态关联的任务)
    const allTaskNodes = taskNodes.concat(neighbors.filter((node) => !taskNodesMap.has(node.getData().code)) as Node[]);
    // 删除节点 并加入画布历史 以便删除失败后恢复
    await this.batchUpdate(graph, () => graph.removeCells(nodes));
    //  更新所有相关任务的stateMaps
    const allStateMaps = this.refreshTaskStateMaps(graph, allTaskNodes);

    try {
      // 保存删除后任务节点的最新stateMaps
      allStateMaps.length > 0 && (await this.apiService.saveTaskDataState(allStateMaps).toPromise());
      // 保存画布
      await this.saveGraph(graph, false);
      this.noticeDataStatesChanged(allStateMaps);
      this.message.success(this.translateService.instant('dj-删除成功！'));
      if (getVariableFlag) {
        this.dtdDesignerService._isGetVariableByProTask$.next(true);
      }
    } catch (error) {
      // 恢复删除前的画布状态
      this.undo(graph);
    }
  }

  /**
   * 批量加入画布历史
   * @param graph 画布实例
   * @param cmd
   * @param callback
   */
  batchUpdate(graph: Graph, callback) {
    return new Promise((resolve) => {
      graph.disableHistory();
      graph.enableHistory();
      graph.batchUpdate(() => {
        callback && callback();
        resolve(true);
      });
    });
  }

  /**
   * 退回
   * @param graph
   * @param cmd
   */
  undo(graph: Graph) {
    graph.undo();
    graph.disableHistory();
  }

  /**
   * 批量连线，从多个起始节点连到一个目标节点
   * @param graph 画布实例
   * @param codes 起始节点对应的code数组
   * @param node 目标节点
   */
  connectEdgeToNode(graph: Graph, codes, targetNode: Node) {
    const nodes: Node[] = graph.getNodes().filter((node) => codes.includes(node.getData().code));
    nodes.forEach((node: Node) => this.connectNode(graph, node, targetNode));
  }

  /**
   * 批量连线，从一个起始点连接多个目标节点
   * @param graph 画布实例
   * @param sourceNode 开始节点
   * @param codes 目标节点对应的code数组
   */
  connectEdgeToMutipleNodes(graph: Graph, sourceNode: Node, codes) {
    const nodes: Node[] = graph.getNodes().filter((node) => codes.includes(node.getData().code));
    nodes.forEach((node: Node) => this.connectNode(graph, sourceNode, node));
  }

  /**
   * 根据codes数组获取节点数组
   *
   */
  getNodesByCodes(graph: Graph, codes: string[]) {
    return graph.getNodes().filter((node) => codes.includes(node.getData().code));
  }

  /**
   * 获取在画布中的状态列表
   * @param graph
   * @param originDataStateList
   */
  getInGraphDataState(graph: Graph, originDataStateList = []) {
    const dataStateList = [];
    const stateNodes = graph.getNodes().filter((node) => node.getData().type === 'datastate');
    const stateCodes = stateNodes.map((node) => node.getData().code);
    originDataStateList.forEach((item) => {
      const states = item.dataStateList.filter((state) => stateCodes.includes(state.code));
      if (states.length > 0) {
        dataStateList.push({ ...item, dataStateList: states });
      }
    });
    return dataStateList;
  }

  /**
   * 删除线Tool
   * @param graph 画布实例
   * @param edge 线
   */
  toogleEdgeRemogeTool(graph: Graph, edge: Edge) {
    edge.addTools({
      name: 'button-remove',
      args: {
        x: 0,
        y: 0,
        offset: { x: 0, y: 0 },
        onClick: ({ view }: any) => {
          let loading = false;
          this.modal.confirm({
            nzTitle: this.translateService.instant('dj-删除状态tips'),
            nzContent: '',
            nzWrapClassName: 'vertical-center-modal',
            nzOkText: this.translateService.instant('dj-确定'),
            nzOkLoading: loading,
            nzOnOk: async () => {
              let stateMaps;
              const source = edge.getSourceCell() as Node;
              const target = edge.getTargetCell() as Node;
              // 删除线，加入画布历史
              await this.batchUpdate(graph, () => graph.removeCell(edge));

              if (source.getData().type === 'task') {
                stateMaps = this.refreshTaskStateMaps(graph, [source]);
              } else if (target.getData().type === 'task') {
                stateMaps = this.refreshTaskStateMaps(graph, [target]);
              }
              loading = true;
              try {
                await this.apiService.saveTaskDataState(stateMaps).toPromise();
                await this.saveGraph(graph, false);
                this.noticeDataStatesChanged(stateMaps);
                this.message.success(this.translateService.instant('dj-删除成功！'));
              } catch (error) {
                this.undo(graph);
              } finally {
                loading = false;
              }
              // 如果删除线连接目标为当前选中节点，刷新节点数据状态
              if (target.getData().code === this.panelParams?.code) {
                this.dtdDesignerService._isGetVariableByProTask$.next(true);
              }
            },
          });
        },
      },
    });
  }

  /**
   * 判断状态或任务是否
   */
  isInGraph(graph: Graph, code: string, type: 'task' | 'datastate') {
    return graph.getNodes().find((node: Node) => {
      const { type: t, code: c } = node.getData();
      return t === type && c === code;
    });
  }

  /**
   * 根据任务数据获取任务类型
   * @param taskData
   * @returns
   */
  getTaskType(taskData: any) {
    const { type, category, executeType } = taskData;
    for (const key in taskTypeMap) {
      if (isEqual(taskTypeMap[key], { type, category, executeType })) {
        return key;
      }
    }
  }

  /**
   * 界面设计基本数据
   * @param data
   * @returns
   */
  getBaseWorkData(data) {
    const app = this.appService?.selectedApp;
    const { name, lang } = app || {};
    let title = {
      en_US: `${lang?.name?.['en_US'] ?? name}-${data.lang?.name?.['en_US'] ?? data.name}`,
      zh_CN: `${lang?.name?.['zh_CN'] ?? name}-${data.lang?.name?.['zh_CN'] ?? data.name}`,
      zh_TW: `${lang?.name?.['zh_TW'] ?? name}-${data.lang?.name?.['zh_TW'] ?? data.name}`,
    };
    // 模本管理没有解决方案，导致解决方案名展示undefined，这里替换掉
    title = JSON.parse(JSON.stringify(title).replace(/undefined-/g, ''));

    return {
      title, // 解决方案名称 + 作业名
      code: data?.code,
      category: 'Activity',
      name: data.name,
    };
  }

  getWorkData(workData, data) {
    // 执行方式:人工 + 业务类型：DATA_ENTRY + 业务模式：BUSINESS  = 特殊的表单类型的界面设计器
    if ('manual' === data.executeType && 'DATA_ENTRY' === data.category && 'BUSINESS' === data.pattern) {
      return { ...workData, data };
    } else {
      // 在这里做判断
      return {
        ...workData,
        project: data.projectCode,
        descriptionLang: data.descriptionLang,
        data: {
          ...data,
          taskPattern: data['pattern'] || data.pattern,
        },
      };
    }
  }

  /**
   *
   * @param graph
   * @param deleted
   * @param nodeId
   * @param type
   * @returns
   */
  associateDeleted(graph: Graph, deleted, nodeId: string, type: 'source' | 'target') {
    if (!deleted?.length) return;
    const deleteCodes = deleted.map((item) => item.code);
    const taskNodes = this.getNodesByCodes(graph, deleteCodes);
    const taskNodesId = taskNodes.map((node: Node) => node.getProp().id);
    // 匹配要删除的连线
    const willRemovedEdges = graph.getEdges().filter((edge: Edge) => {
      const sourceId = edge.getSourceCellId();
      const targetId = edge.getTargetCellId();

      return (
        taskNodesId.includes(type === 'source' ? sourceId : targetId) &&
        (type === 'source' ? targetId : sourceId) === nodeId
      );
    });
    graph.removeCells(willRemovedEdges);
  }

  /**
   * 根据added新增状态的节点到画布并连线
   * @param graph
   * @param added
   * @param node
   * @param type
   * @returns
   */
  associateAdded(graph: Graph, added, node: Node, type: 'source' | 'target') {
    if (!added?.length) return;
    const nodeType = node.getData().type;
    // 添加新增状态的节点到画布
    added.forEach((item) => {
      // 如果在画布上则不再添加
      if (this.isInGraph(graph, item.code, nodeType === 'task' ? 'datastate' : 'task')) return;
      // 如果不在画布上则先添加到画布
      const position = this.getStatePosition({
        node: node,
        direction: type === 'source' ? 'left' : 'right',
      });
      // 新增节点
      if (nodeType === 'task') {
        this.addDataStateNodeToGraph(graph, item.code, item.dataDescriptionCode, item.name, position);
      } else {
        this.addTaskNodeToGraph(graph, item, position);
      }
    });
    // 连线
    const addCodes = added.map((item) => item.code);
    type === 'source'
      ? this.connectEdgeToNode(graph, addCodes, node)
      : this.connectEdgeToMutipleNodes(graph, node, addCodes);
  }

  /**
   * 设置节点校验状态
   * 将属性面板的验证结果在对应节点上通过样式体现出来
   * @param code 任务code或状态code
   * @param nodeType 'task'|'datastate' 节点类型 任务节点or数据状态节点
   * @param valid 验证结果
   */
  public setNodeValidateState(graph: Graph, code: string, nodeType: 'task' | 'datastate', valid: boolean): void {
    const node = graph.getNodes().find((node) => {
      const nodeData = node.getData();
      return code === nodeData.code && nodeType === nodeData.type;
    });
    if (node) {
      node.setData({
        ngArguments: {
          valid,
        },
      });
    }
  }

  public noticeDataStatesChanged(
    params: {
      code: string;
      stateMaps: {
        input: string;
        output: Record<string, string>;
      }[];
    }[],
  ): void {
    this._dataState$.next(params);
  }

  /**
   * 对接口返回的画布数据进行拦截处理
   * @param edge
   */
  intercept(c) {
    if (c.shape === 'edge') {
      c.attrs = { ...edgeAttrs };
    } else {
      // 对position保护
      const { x, y } = c.position;
      c.position = {
        x: parseInt(x),
        y: parseInt(y),
      };
    }
  }
}
