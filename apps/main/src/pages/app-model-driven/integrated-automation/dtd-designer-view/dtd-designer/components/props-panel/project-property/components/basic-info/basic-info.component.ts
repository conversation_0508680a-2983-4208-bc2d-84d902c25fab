import {
  AfterViewInit,
  ChangeDetectorRef,
  Component,
  ElementRef,
  EventEmitter,
  Input,
  OnInit,
  Output,
  ViewChild,
} from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { WorkDesignType } from 'pages/app-model-driven/integrated-automation/dtd-designer-view/dtd-designer/types';
import { businessType, dateType } from '../../../../project-widget/project-set';
import { cloneDeep, delay } from 'lodash';
import { AppService } from 'pages/apps/app.service';
import { to } from 'common/utils/core.utils';
import { ProjectPropertyApiService } from '../../project-property-api.service';
import { NzMessageService } from 'ng-zorro-antd/message';
import { CaseModalComponent } from 'components/bussiness-components/case-modal/case-modal.component';
import { GlobalService } from 'common/service/global.service';
import { DtdDesignerViewService } from 'pages/app-model-driven/integrated-automation/dtd-designer-view/service/dtd-designer-view.service';
import { AdModalService } from 'components/ad-ui-components/ad-modal/ad-modal.service';
import { AuthService } from 'common/service/auth.service';

@Component({
  selector: 'app-basic-info',
  templateUrl: './basic-info.component.html',
  styleUrls: ['./basic-info.component.less'],
})
export class BasicInfoComponent implements OnInit, AfterViewInit {
  @Input() currentProject: any;
  @Input() isMainPro: boolean;
  @Input() isRoot: boolean;
  @Input() modalFlag: string;
  @Input() contentRef: HTMLDivElement;
  @Input() checkContentChangeWithoutSave!: (msg?: Function) => boolean;

  @Output() openDesigner = new EventEmitter<any>();
  @Output() patchValue = new EventEmitter<any>();
  @Output() update = new EventEmitter<any>();
  @Output() initOrEndChanged: EventEmitter<any> = new EventEmitter();
  @Output() reload = new EventEmitter();
  @Output() refresh = new EventEmitter();
  @Output() triggerSave = new EventEmitter();
  @Output() saveAndRefresh = new EventEmitter();

  @ViewChild('caseModalRef') caseModalRef: CaseModalComponent;
  @ViewChild('manualRef') manualRef: ElementRef;

  WorkDesignType = WorkDesignType;
  patternData = [{ value: 'BUSINESS', label: 'BUSINESS' }];
  businessType = businessType;

  public typeList = [];
  public isMobile: boolean;
  public caseModalVisible: boolean;
  public caseData: any; // 个案开窗需要的数据

  public workVisible: any; // 界面设计
  public workData: any; // 任务界面设计数据
  public descriptionLang: any;
  cardJsonVisible: boolean = false;

  // 可追踪项目
  get isTraceable() {
    return ['user', 'solve'].includes(this.currentProject.meta?.executeType);
  }

  get isReference() {
    return this.appService.selectedApp?.tag?.sourceComponent === 'BC'; // 是否是中台推过来的
  }

  constructor(
    public translate: TranslateService,
    public appService: AppService,
    private projectPropertyApiService: ProjectPropertyApiService,
    private message: NzMessageService,
    private cdr: ChangeDetectorRef,
    public dtdDesignerViewService: DtdDesignerViewService,
    private modal: AdModalService,
    private authService: AuthService,
  ) {
    this.typeList = [
      {
        value: 'mainline',
        label: translate.instant('dj-数据预整理项目'),
      },
      {
        value: 'user',
        label: translate.instant('dj-可追踪项目'),
      },
      {
        value: 'solve',
        label: translate.instant('dj-可追踪项目'),
      },
    ];
  }

  // handlePageDesign(isMobile: boolean) {
  //   this.openDesigner.emit(isMobile);
  // }

  /**
   * 点击界面设计
   * @param projectData
   */
  async handlePageDesign(projectData) {
    if (this.authService.getAuth('update')) {
      const result = await new Promise<boolean>((resolve) => {
        this.triggerSave.emit(resolve); // 触发保存操作
      });

      if (!result) return;
    }
    this.isMobile = false;
    if (['dsl', 'new'].includes(this.currentProject.meta._pageModel)) {
      // new和dls都视为dsl，否则是tag模式
      // const param = {
      //   code: data.id,
      //   type: 'project',
      //   _pageModel: 'pageView',
      // };
      // this.apiService.setPageModel(param).subscribe((res) => {
      //   this.afterHandlePage({ ...res.data, _pageModel: 'pageView' });
      // });
      this.afterHandlePage({ ...this.currentProject.meta, _pageModel: 'dsl' });
      return;
    }
    this.afterHandlePage(
      '_pageModel' in this.currentProject.meta
        ? this.currentProject.meta
        : { ...this.currentProject.meta, _pageModel: 'pageView' },
    );
  }

  /**
   * 点击移动端页面设计
   * @param projectData
   */
  async handleMobilePageDesign(projectData) {
    if (this.authService.getAuth('update')) {
      const result = await new Promise<boolean>((resolve) => {
        this.triggerSave.emit(resolve); // 触发保存操作
      });

      if (!result) return;
    }

    this.isMobile = true;

    projectData.meta._pageModel = 'dsl';

    this.afterHandlePage(projectData.meta);
  }

  /**
   * 点击个案
   * @param projectData
   */
  handleCase(projectData): void {
    this.caseModalVisible = true;
    this.caseData = cloneDeep({ ...projectData.meta, code: projectData.meta.code });
  }

  // 保存个案
  async saveCase(data: any) {
    const params = {
      newProjectCode: data.newCode,
      newProjectName: data.newName,
      tenantIds: data.tenantIds,
      projectCode: this.caseData.id || this.caseData.code,
    };
    this.caseData = cloneDeep(data);
    this.caseData.needLocation = true;

    const [err, res] = await to(this.projectPropertyApiService.copyMonitorRule(params).toPromise());

    if (res?.code === 0) {
      this.message.success(this.translate.instant('dj-操作成功'));

      // this.reload.emit();
    }

    this.caseModalRef.closeLoad();
  }

  // 界面设计
  afterHandlePage(data: any): void {
    const app = this.appService?.selectedApp;
    const title = {
      en_US: `${app?.lang?.name?.['en_US'] ?? app?.name}-${data.lang?.name?.['en_US'] ?? data.name}`,
      zh_CN: `${app?.lang?.name?.['zh_CN'] ?? app?.name}-${data.lang?.name?.['zh_CN'] ?? data.name}`,
      zh_TW: `${app?.lang?.name?.['zh_TW'] ?? app?.name}-${data.lang?.name?.['zh_TW'] ?? data.name}`,
    };
    const descriptionLang = data.lang?.description;
    this.workData = {
      title, // 解决方案名称 + 作业名
      code: null,
      category: 'Activity',
      name: data.name,
      project: data?.id || data?.code,
      data: {
        ...data,
        taskPattern: data?.extendFields?.taskPattern || data.taskPattern,
      },
      descriptionLang,
    };
    this.workVisible = true;
    this.descriptionLang = descriptionLang;
    this.cdr.detectChanges();
  }

  // 界面设计关闭
  handlePageClose(): void {
    // 刷新项目
    // this.reload.emit();

    // if (this.workData.data?.extendFields?.manualAble || this.workData.data?.manualAble) {
    //   this.taskComponentRef?.fetchTasks();
    // }

    this.workData = null;
    this.workVisible = false;
  }

  // 项目卡自定义
  handleProjectCustomize() {
    if (this.authService.getAuth('update') && this.checkContentChangeWithoutSave()) {
      this.modal.info({
        nzTitle: this.translate.instant('dj-数据未保存，请先保存后再进行其他操作！'),
        nzOkText: this.translate.instant('dj-确定'),
        nzOnOk: () => {},
      });
      return;
    }
    const app = this.appService?.selectedApp;
    const projectData = this.currentProject.meta;
    const title = {
      en_US: `${app?.lang?.name?.['en_US'] ?? app?.name}-${projectData.lang?.name?.['en_US'] ?? projectData.name}`,
      zh_CN: `${app?.lang?.name?.['zh_CN'] ?? app?.name}-${projectData.lang?.name?.['zh_CN'] ?? projectData.name}`,
      zh_TW: `${app?.lang?.name?.['zh_TW'] ?? app?.name}-${projectData.lang?.name?.['zh_TW'] ?? projectData.name}`,
    };
    this.workData = {
      title, // 解决方案名称 + 作业名
      code: projectData?.id || projectData?.code,
      category: 'Activity',
      name: projectData.name,
      project: projectData?.id || projectData?.code,
      data: {
        ...projectData,
        taskPattern: projectData?.extendFields?.taskPattern,
      },
    };
    this.cardJsonVisible = true;
  }

  handlePatch(key: string, data: any): void {
    this.patchValue.emit({
      key,
      data,
    });

    if (key === 'mergeFields') {
      this.currentProject['meta'][key] = data?.value;
    }
    if (data.needLang) {
      if (key !== 'targetName') {
        this.currentProject.lang = {
          ...(this.currentProject.lang || {}),
          [key]: data.lang,
        };
      } else {
        this.currentProject['targetLang'] = {
          ...(this.currentProject['targetLang'] || {}),
          name: data.lang,
        };
      }
    }

    if (key === 'init' || key === 'end' || key === 'groupCode') {
      this.initOrEndChanged.emit();
    }
  }

  handleUpdate(evt) {
    this.update.emit(evt);
  }

  handleJsonSave(): void {
    this.saveAndRefresh.emit();
    this.refresh.emit();
  }

  handleJsonModalCancel() {
    this.cardJsonVisible = false;
  }

  scrollBottom() {
    this.currentProject['basicForm'].get('manualAble').valueChanges.subscribe((res) => {
      delay(() => {
        this.manualRef?.nativeElement?.scrollIntoView({
          behavior: 'smooth', // 平滑滚动
          block: 'end', // 按钮对齐视口底部
        });
      }, 0);
    });
  }

  ngOnInit() {}

  ngAfterViewInit(): void {
    this.scrollBottom();
  }
}
