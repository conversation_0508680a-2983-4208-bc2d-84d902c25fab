.variable-list {
  margin-bottom: 8px;
}
.variable-item {
  display: flex;
  align-items: center;
  border-radius: 5px;
  height: 50px;
  justify-content: space-between;
  padding: 10px;
  background-color: #eeeff9;
  &:hover {
    background-color: #e0e2f4;
  }
  .item-info {
    display: flex;
    align-items: center;
    .iconfont {
      font-size: 24px;
      margin-right: 8px;
    }
    .variable-name {
      display: flex;
      flex-direction: column;
      border-left: 1px solid #b3b9dc;
      line-height: 16px;
      padding-left: 8px;
      .name {
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
        max-width: 185px;
      }
      .description {
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
        max-width: 185px;
        color: #999999;
        font-size: 12px;
      }
    }
  }

  .options {
    display: flex;
    align-items: center;
    .iconfont {
      margin-left: 8px;
    }
    .see-more {
      transition: all 0.2s ease-in-out;
      cursor: pointer;
      &.active {
        transform: rotate(90deg);
        color: #8d79fe;
      }
    }
  }
}

.more-box {
  background-color: #f7f8fe;
  border-bottom-left-radius: 5px;
  border-bottom-right-radius: 5px;
  padding: 10px;
  .node-line-item {
    width: 100%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}

.popver-item {
  color: #333;
  cursor: pointer;
  font-size: 12px;
}
.add-btn {
  width: 100%;
  margin-top: 8px;
}
