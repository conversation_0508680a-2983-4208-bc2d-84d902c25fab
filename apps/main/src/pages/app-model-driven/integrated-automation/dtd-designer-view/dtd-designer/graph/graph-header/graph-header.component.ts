import { Component, ElementRef, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';
import { ModulePublishButtonComponent } from 'components/bussiness-components/module-publish-button/module-publish-button.component';
import { AppService } from 'pages/apps/app.service';
import { GlobalService } from 'common/service/global.service';
import { AddTemplatePopoverComponent } from '../components/flow/add-template-popover/add-template-popover.component';
import { Cell, Edge, Graph, Node } from '@antv/x6';
import { ApiService } from '../service/api.service';
import { GraphService } from '../service/graph.service';
import { GraphComponent } from '../graph.component';
import { ComponentRefKeys } from '../../types';
import { TranslateService } from '@ngx-translate/core';
import { NzMessageService } from 'ng-zorro-antd/message';
import { ClipboardDtdData, ClipboardType } from 'common/service/dtd-copy-paste/dtd-copy-paste.service.type';
import { DtdCopyPasteService } from 'common/service/dtd-copy-paste/dtd-copy-paste.service';
import { omit } from 'lodash';
import { AdModalService } from 'components/ad-ui-components/ad-modal/ad-modal.service';
import { DtdDesignerViewService } from '../../../service/dtd-designer-view.service';

interface AppDtdEdge extends Edge {
  source: {
    cell: string;
  };
  target: {
    cell: string;
  };
}

@Component({
  selector: 'dtd-designer-graph-header',
  templateUrl: './graph-header.component.html',
  styleUrls: ['./graph-header.component.less'],
})
export class GraphHeaderComponent implements OnInit {
  @Input() graph: Graph;
  @Input() applicationCodeProxy; // 解决方案code代理 用于模板管理
  @Input() favouriteCode; // 模板id 用于模板管理

  @Output() updatedTask = new EventEmitter(); // 通知外部更新任务
  @Output() updatedState = new EventEmitter(); // 通知外部更新状态
  @Output() loading = new EventEmitter(); // 通知外部更新状态
  @Output() showError = new EventEmitter(); // 通知展示报错信息

  @ViewChild('publishButton') publishButtonRef: ModulePublishButtonComponent;
  @ViewChild('templatePopover') templatePopoverRef: AddTemplatePopoverComponent;

  taskModalVisible: boolean = false;
  addedTask: any;
  dataStateModalVisible: boolean = false;
  dataStateModalTitle: string; // 数据开窗标题
  dataStateModalData: any; // 传入数据状态开窗的数据
  collectVisible: boolean;

  // 新T
  newTModalVisible: boolean = false;

  get isFromTemplate(): boolean {
    return !!this.applicationCodeProxy;
  }

  get graphComponent(): GraphComponent {
    return this.graphService.getComponentRef(ComponentRefKeys.GRAPH) as GraphComponent;
  }

  get dtdReference() {
    return !(this.appService?.selectedApp?.tag?.sourceComponent === 'BC');
  }

  // 模板节点
  _popoverVisible: boolean = false;
  set popoverVisible(visible: boolean) {
    this._popoverVisible = visible;
    if (visible) {
      this.templatePopoverRef.handleGetTempList();
    }
  }
  get popoverVisible() {
    return this._popoverVisible;
  }

  get isDisabled() {
    return this.graph?.getCellCount() === 0;
  }

  get isDraftVersion() {
    return this.dtdService.adpVersionHeaders.adpStatus !== 'effect';
  }

  // 模板节点

  historyModalProps = {
    transferModal: false,
    code: '',
    collection: 'dataGroup',
  }; // 修改历史弹窗展示
  isSkip: boolean = true;

  constructor(
    public appService: AppService,
    public globalService: GlobalService,
    private apiService: ApiService,
    private graphService: GraphService,
    private translateService: TranslateService,
    private message: NzMessageService,
    public dtdCopyPasteService: DtdCopyPasteService,
    private modal: AdModalService,
    private dtdService: DtdDesignerViewService,
  ) {}

  ngOnInit() {
    const type = this.isFromTemplate ? 'dtdTemplate' : 'dtd';
    this.apiService.guideIsSkip(type).subscribe((res) => {
      this.isSkip = res.data;
    });
  }

  /**
   * 点击新增任务
   * @param taskInfo
   */
  async handleAddTask(taskInfo): Promise<void> {
    if (await this.dtdService.checkPropsPanelCanContinue()) {
      this.dtdService.closePanel();
      this.taskModalVisible = true;
      this.addedTask = taskInfo;
    }
  }

  /**
   * 任务开窗保存后回调
   * @param info 保存后的任务数据 { flag: 'add'|'edit', param, taskType}
   * *保存后如果是新增任务，则新增任务节点至画布中间；如果是编辑任务，则更新画布上的任务节点
   */
  handleAfterSaveModalSave(info): void {
    const { param } = info;
    this.taskModalVisible = false;

    this.graphComponent?.addTaskNodeToGraph(param);
    // 通知外部更新任务
    this.updatedTask.emit();
  }

  async handleAddDataState(): Promise<void> {
    if (await this.dtdService.checkPropsPanelCanContinue()) {
      this.dtdService.closePanel();
      this.dataStateModalData = null;
      this.dataStateModalVisible = true;
    }
  }

  /**
   * 数据状态开窗点确定后回调
   * @param data
   * data.param 保存时的表单数据
   * data.type 保存类型，ADD/EDIT
   * data.beforeData 编辑前的数据
   * *对新增的状态需在画布中间添加状态节点，编辑的状态需更新状态节点名称
   */
  afterDataStateModalSave(info) {
    this.dataStateModalVisible = false;
    const { data } = info;
    const { dataStates } = data;
    // 新增状态节点至画布中间
    dataStates.forEach((state) => {
      const { name, code, dataCode } = state;
      this.graphComponent?.addDataStateNodeToGraph(code, dataCode, name);
      // 更新graphComponent?.stateNodeMap
      this.graphComponent?.addToTaskStateMap(state, 'datastate');
    });
    this.updatedState.emit();
  }

  /**
   * 设置100%大小
   */
  handleSetScaleDefault(): void {
    this.graph.zoomTo(1);
  }
  /**
   * 放大
   */
  handleZoomUp(): void {
    this.graph?.zoom(0.1);
  }

  /**
   * 缩小
   */
  handleZoomDown(): void {
    this.graph?.zoom(-0.1);
  }

  /**
   * 复制
   */
  handleCopy(): void {
    const cells = this.graph.getSelectedCells().filter((cell) => cell.shape !== 'edge');
    if (cells.length === 0) {
      this.message.error(this.translateService.instant('dj-请先选中节点'));
      return;
    }

    let taskCode = [];
    let dataStatueCode = [];

    cells.forEach((cell: Cell<Cell.Properties>) => {
      const { type, code } = cell.getProp('data');
      if (type === 'task') {
        taskCode.push(code);
      } else if (type === 'datastate') {
        dataStatueCode.push(code);
      }
    });

    this.loading.emit({ loading: true });
    let params = {
      taskCode,
      dataStatueCode,
    };
    this.apiService.completeDTDValidate(params).subscribe(
      (res) => {
        this.loading.emit({ loading: false });
        if (res.code === 0) {
          this.modal.confirm({
            nzTitle: this.translateService.instant('dj-是否先保存当前画布？'),
            nzContent: this.translateService.instant('dj-先进行保存操作可以保证复制的数据是最新的'),
            nzWrapClassName: 'vertical-center-modal',
            nzOkText: this.translateService.instant('dj-保存并复制'),
            nzCancelText: this.translateService.instant('dj-直接复制'),
            nzOnOk: () => {
              this.graphComponent?.saveGraph(false);
              // if (this.activityFlowVisible) this.handleActivityFlowSave(true);
              this.handleOperateCopy(cells);
            },
            nzOnCancel: () => {
              this.handleOperateCopy(cells);
            },
          });
        } else {
          this.message.error(this.translateService.instant('dj-复制失败，请重试'));
        }
      },
      () => {
        this.loading.emit({ loading: false });
      },
    );
  }

  /**
   * dtd画布的复制
   */
  handleOperateCopy(cells: Cell<Cell.Properties>[] = []) {
    // 后来画布的配置 进行了 调整，虽然线段 是 可以被 选中的了，但 dtd的 对应关系 却不由 线段决定，所以该逻辑 保留
    // 所以需要根据cells 从所有画布线段中取 所需线段，规则为，线段的source 和 target 都在 cells 中
    const cellIdList = cells.map((cell) => cell.id);
    const edges = this.graph.getEdges().filter((edge: AppDtdEdge) => {
      return cellIdList.includes(edge.source.cell) && cellIdList.includes(edge.target.cell);
    });

    // 转换成与dtd规划工具一致的标准结构
    const dtdDataList = this.graphComponent?.originDataStateList?.map((data) => {
      return {
        dataDescription: {
          code: data.dataDescription.code,
        },
        dataStateList: data.dataStateList,
      };
    });

    // 复制到剪切板
    this.dtdCopyPasteService.copy(
      [...cells, ...edges],
      this.graphComponent?.taskList,
      dtdDataList,
      [],
      ClipboardType.APP_DTD,
    );
  }

  /**
   * 粘贴
   */
  async handlePaste() {
    this.loading.emit({ loading: true });
    try {
      const clipboardDtdData: ClipboardDtdData = await this.dtdCopyPasteService.paste();
      if (clipboardDtdData.type === ClipboardType.DTDInfo || clipboardDtdData.type === ClipboardType.TOOL_DTD) {
        console.log('粘贴来自工具的dtd信息');
        await this.dtdToolBatchSave(clipboardDtdData);
      }
      if (clipboardDtdData.type === ClipboardType.APP_DTD) {
        console.log('粘贴来自解决方案的dtd信息');
        // 复制task 与 data，获取 映射关系
        const { data } = await this.dtdToolPaste(clipboardDtdData);
        await this.graphComponent?.taskComponent?.fetchTasks();
        await this.graphComponent?.dataStateComponent?.fetchDataList();
        const dataListFlat = this.graphComponent?.originDataStateList?.flatMap((obj) => obj.dataStateList);
        // 处理粘贴的dtd数据节点信息
        const pasteCellConvert = this.handlePasteCell(
          clipboardDtdData.datdData.cellList,
          new Map(Object.entries(data.taskMapping)),
          new Map(Object.entries(data.dataMapping)),
          dataListFlat,
        );

        // 解决方案 粘贴的dtd数据节点
        this.handleClipboardDtdData(pasteCellConvert);
      }
    } catch (err) {
      this.message.error(err.message);
    }
    this.loading.emit({ loading: false });
  }

  // 请求接口 复制 任务和数据状态，并获取 映射关系
  async dtdToolPaste(clipboardDtdData: ClipboardDtdData) {
    let params = {
      application: this.appService?.selectedApp?.code,
      data: {
        task: [...clipboardDtdData.datdData.taskList],
        data: [...clipboardDtdData.datdData.dataList],
      },
    };

    return this.apiService.dtdToolPaste(params).toPromise();
  }

  // dtd规划工具的数据粘贴保存
  async dtdToolBatchSave(clipboardDtdData: ClipboardDtdData) {
    const param = {
      dataList: clipboardDtdData.datdData.dataList.map((data) => {
        return {
          dataDescription: data.dataDescription,
          dataFeatures: data.dataFeatures,
          dataStates: data.dataStateList,
        };
      }), // 数据List
      taskList: clipboardDtdData.datdData.taskList, // 任务List
      flowList: clipboardDtdData.datdData.flowList, // flowList 包括 活动设计，api设计
      relationshipData: Object.assign(this.graph.toJSON(), { autoLayout: true }),
      application: this.appService?.selectedApp?.code,
    };

    const dataStateList = clipboardDtdData.datdData.dataList.reduce((pre, cur) => {
      return [...pre, ...cur.dataStateList];
    }, []);

    const cellList = clipboardDtdData.datdData.cellList.map((cell) => {
      const cellForGraph = omit(cell, ['tools']);
      const { data } = cellForGraph;
      if (data && data.type === 'task') {
        const task = clipboardDtdData.datdData.taskList.find((task) => task.code === data.code);
        cellForGraph.data = { ...task, ...data };
      }
      if (data && data.type === 'datastate') {
        const dataState = dataStateList.find((dataState) => dataState.code === data.code);
        cellForGraph.data = { ...dataState, ...data };
      }

      return cellForGraph;
    });

    param.relationshipData.cells.forEach((cell) => (cell.visible = true));
    param.relationshipData.cells = [...param.relationshipData.cells, ...(cellList as Cell.Properties[])];

    this.apiService.dtdToolBatchSave(param).subscribe(
      (res) => {
        this.loading.emit({ loading: true });
        if (res.code === 0) {
          const pasteCellIdList = cellList.map((cell) => cell.id);
          this.graph.resetSelection(pasteCellIdList);
          console.log(`graph Header fetchGraphData`);
          this.graphComponent?.fetchGraphData();
        } else {
          this.message.error(res.msg);
        }
        this.loading.emit({ loading: false });
      },
      () => {
        this.loading.emit({ loading: false });
      },
    );
  }

  showCompareDialog() {
    this.dtdService.notifyShowCompareGraph(); //  通知生成对比的画布的图，并在弹框展示
  }
  /**
   * 删除
   */
  async handleRemove(): Promise<void> {
    const cells = this.graph.getSelectedCells();
    if (cells.length === 0) {
      this.message.error(this.translateService.instant('dj-请先选中节点'));
      return;
    }
    this.graphService.handleRemoveNodes(this.graph, cells as Node[]);
  }

  /**
   * 收藏
   */
  handleCollect(): void {
    const cells = this.graph.getSelectedCells();
    if (cells.length === 0) {
      this.message.error(this.translateService.instant('dj-请先选中节点'));
      return;
    }
    this.modal.confirm({
      nzTitle: this.translateService.instant('dj-是否先保存当前画布？'),
      nzContent: this.translateService.instant('dj-先进行保存操作可以保证复制的数据是最新的'),
      nzWrapClassName: 'vertical-center-modal',
      nzOkText: this.translateService.instant('dj-保存并收藏'),
      nzCancelText: this.translateService.instant('dj-直接收藏'),
      nzOnOk: () => {
        this.graphComponent?.saveGraph(false);
        this.collectVisible = true;
      },
      nzOnCancel: () => {
        this.collectVisible = true;
      },
    });
  }

  /**
   * 确认收藏
   */
  async handleCollectConfirm(e: any): Promise<void> {
    this.collectVisible = false;
    let param = {};
    const cells = this.graph.getSelectedCells().filter((cell) => cell.shape !== 'edge');
    const cellIdList = cells.map((cell) => cell.id);
    const edges = this.graph.getEdges().filter((edge: AppDtdEdge) => {
      return cellIdList.includes(edge.source.cell) && cellIdList.includes(edge.target.cell);
    });
    const taskCode = [];
    const dataStatueCode = [];
    cells.forEach((cell) => {
      const { type, code } = cell.getProp('data');
      if (type === 'task') {
        taskCode.push(code);
      } else {
        dataStatueCode.push(code);
      }
    });
    param = {
      application: this.appService?.selectedApp?.code,
      canvasGraph: { cellList: [...cells, ...edges] },
      taskCode,
      dataStatueCode,
      ...e,
    };

    this.apiService.saveFavouriteDTD(param).subscribe(
      (res) => {
        if (res.code === 0) {
          this.message.success(this.translateService.instant('dj-收藏成功'));
        }
      },
      () => {},
    );
  }

  handleCollectCancel() {
    this.collectVisible = false;
  }

  /**
   * 发布
   */
  async handlePublish() {
    await this.graphComponent?.saveGraph(false);
    this.publishButtonRef.startPublish();
  }

  /**
   * 保存
   */
  handleSave(): void {
    this.graphComponent?.saveGraph();
  }

  handlePublishAction(e): void {
    this.graphComponent.loading = e;
  }

  openModifyHistoryModal(): void {
    this.historyModalProps.code = this.isFromTemplate ? this.favouriteCode : this.appService?.selectedApp?.code;
    this.historyModalProps.collection = this.isFromTemplate ? 'favourite' : 'dataGroup';
    this.historyModalProps.transferModal = true;
  }

  handleHelp(): void {
    const type = this.isFromTemplate ? 'dtdTemplate' : 'dtd';
    this.apiService.setSkip({ isSkip: true, type }).subscribe((res) => {
      if (res.code === 0) {
        this.isSkip = true;
      }
    });
  }

  /**
   * 处理dtd粘贴
   * @param data
   */
  async handlePasteDtd(data: any) {
    this.popoverVisible = false;
    const tips = this.translateService.instant('dj-正在添加DTD节点，请勿关闭当前页面...');
    this.loading.emit({ loading: true, tips });
    try {
      await this.graphComponent?.taskComponent?.fetchTasks();
      await this.graphComponent?.dataStateComponent?.fetchDataList();

      const dataListFlat = this.graphComponent?.originDataStateList?.flatMap((obj) => obj.dataStateList);
      // 处理粘贴的dtd数据节点信息
      const pasteCellConvert = this.handlePasteCell(
        data.canvasGraph.cellList,
        new Map(Object.entries(data.taskMapping)),
        new Map(Object.entries(data.dataMapping)),
        dataListFlat,
      );
      // 解决方案 粘贴的dtd数据节点
      this.handleClipboardDtdData(pasteCellConvert);
    } catch (err) {
      console.error(err);
      this.message.error(err.message);
    } finally {
      this.loading.emit({ loading: false, tips: undefined });
    }
  }

  /**
   * 处理粘贴的dtd数据的节点数据
   * @param pasteCell  粘贴的 节点 信息
   * @param pasteTaskMap 粘贴的 task code 映射关系
   * @param pasteDataMap 粘贴的 data code 映射关系
   * @param dataListFlat 当前 组件 平铺的dataList
   */
  handlePasteCell(
    pasteCell: (Node.Metadata | Edge.Metadata)[],
    pasteTaskMap: Map<string, string>,
    pasteDataMap: Map<string, string>,
    dataListFlat: any[],
  ): Cell.Properties[] {
    const offset = 20;
    const graphCellClone = this.graph.cloneCells(this.graph.parseJSON(pasteCell));
    const pasteCellConvert: Cell.Properties[] = [];
    for (const i in graphCellClone) {
      const cell = graphCellClone[i];
      if (cell.isNode()) {
        let { x, y } = cell.getProp<{ x: number; y: number }>('position');
        x += offset;
        y += offset;
        cell.setProp('position', { x, y });
        const cellData = cell.getProp('data');
        if (cellData.type === 'task') {
          if (!pasteTaskMap.has(cellData.code)) throw new Error(`type:${cellData.code} not found`);
          cell.setProp('data', { ...cellData, code: pasteTaskMap.get(cellData.code) });
        }
        if (cellData.type === 'datastate') {
          if (!pasteDataMap.has(cellData.code)) throw new Error(`data:${cellData.code} not found`);
          const state = dataListFlat.find((data) => data.code === pasteDataMap.get(cellData.code));
          const { code, dataCode } = state || {};
          cell.setProp('data', { ...cellData, code, dataCode });
        }
      }
      pasteCellConvert.push(cell.toJSON());
    }
    return pasteCellConvert;
  }

  /**
   * 解决方案 粘贴的dtd数据节点
   * @param pasteCellConvert  处理过的 粘贴dtd数据节点
   */
  handleClipboardDtdData(pasteCellConvert: Cell.Properties[]) {
    try {
      const cellList = [...this.graph.toJSON().cells, ...pasteCellConvert];
      const pasteCellIdList = pasteCellConvert.map((cell) => cell.id);

      this.addToMap(pasteCellConvert);
      this.graph.fromJSON(cellList);
      this.graph.resetSelection(pasteCellIdList);
      const selectedCells = this.graph.getSelectedCells();
      const selectedCellsBBox = this.graph.getCellsBBox(selectedCells);
      this.graph.positionRect(selectedCellsBBox, 'center');
      navigator.clipboard.writeText('');
      this.message.success(this.translateService.instant('dj-粘贴成功'));
    } catch (error) {
      this.message.error(error.message);
    }
  }

  /**
   * 添加任务或状态code到map中
   * @param cells
   */
  addToMap(cells) {
    cells.forEach((cell) => {
      if (cell.data?.type === 'task') {
        this.graphComponent?.taskNodeMap?.set(cell.data.code, cell.data);
        return;
      }
      if (cell.data?.type === 'datastate') {
        this.graphComponent?.stateNodeMap?.set(cell.data.code, cell.data);
      }
    });
  }

  /**
   * 点击任务节点new
   */
  async handleNewTaskClick() {
    if (await this.dtdService.checkPropsPanelCanContinue()) {
      this.dtdService.closePanel();
      this.newTModalVisible = true;
    }
  }

  handleAfterSaveFlow(info: any): void {
    const { param } = info;
    this.newTModalVisible = false;
    this.graphComponent?.addTaskNodeToGraph(param);
    // 通知外部更新任务
    this.updatedTask.emit();
  }

  // 展示报错信息
  onShowError() {
    this.showError.emit();
  }
}
