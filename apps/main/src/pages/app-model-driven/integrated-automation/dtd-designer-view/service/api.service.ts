import { Inject, Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';
import { SystemConfigService, DW_APP_AUTH_TOKEN } from 'common/service/system-config.service';
import { GlobalService } from 'common/service/global.service';
import { DtdDesignerViewService } from './dtd-designer-view.service';

@Injectable()
export class DtdDesignerViewApiService {
  headerConfig = {};
  adesignerUrl: string;
  constructor(
    private http: HttpClient,
    private configService: SystemConfigService,
    private dtdDesignerViewService: DtdDesignerViewService,
  ) {
    this.configService.get('adesignerUrl').subscribe((url) => {
      this.adesignerUrl = url;
      // this.adesignerUrl = 'http://localhost:4201';
    });
  }

  /**
   * 查询流程版本列表 dropdown
   */
  loadDtdVersionList(params: any): Observable<any> {
    return this.http.get(`${this.adesignerUrl}/athena-designer/groupHistory/getDataGroupHistory`, {
      params,
      headers: Object.assign({}, this.headerConfig),
    });

    // const url = '../../../assets/api/getDataGroupHistory.json';
    // return this.http.get(url);
  }

  // table带搜索
  queryVersionList(params: any): Observable<any> {
    return this.http.get(`${this.adesignerUrl}/athena-designer/groupHistory/queryList`, {
      params,
      headers: Object.assign({}, this.headerConfig, this.dtdDesignerViewService.adpVersionHeaders),
    });
  }

  /**
   * 新增流程版本
   * @param params
   * @returns
   */
  createDtdVersion(params: any): Observable<any> {
    const headers = Object.assign({}, this.headerConfig, this.dtdDesignerViewService.adpVersionHeaders);
    console.log(headers);
    return this.http.post(`${this.adesignerUrl}/athena-designer/groupHistory/create`, params, {
      headers,
    });
  }

  /**
   * 更新流程版本
   * @param params
   * @returns
   */
  updateDtdRemark(params: any): Observable<any> {
    return this.http.post(`${this.adesignerUrl}/athena-designer/groupHistory/updateRemark`, params, {
      headers: this.headerConfig,
    });
  }

  /**
   * 删除流程版本
   * @param params
   * @returns
   */
  removeDtdVersion(params: any): Observable<any> {
    return this.http.post(`${this.adesignerUrl}/athena-designer/groupHistory/remove`, params, {
      headers: this.headerConfig,
    });
  }
}
