import { Injectable } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { AdModalService } from 'components/ad-ui-components/ad-modal/ad-modal.service';
import type { DtdDesignerViewComponent } from '../dtd-designer-view.component';
import { PropsPanelComponent, TPropsContentRef } from '../dtd-designer/components/props-panel/props-panel.component';
import { BehaviorSubject, Observable, Subject } from 'rxjs';
import { distinctUntilChanged, filter } from 'rxjs/operators';

interface CheckObject {
  contentComponent?: any; // 渲染组件的实例
  checkFunction?: string; // 检测方法checkFunction
}

interface ContentChangeCheckObject {
  checkObject?: CheckObject;
  isContentChangeWithoutSave?: boolean;
}

@Injectable()
export class DtdDesignerViewService {
  private _contentChangeCheckObject: ContentChangeCheckObject = null;
  private _dtdDesignerComponent: DtdDesignerViewComponent | null = null;
  public propsComponentRef: PropsPanelComponent = null;
  private currentPropsCode: string = null;

  _showNodesError$ = new BehaviorSubject<any>({}); // 是否展示错误弹窗
  get showNodesError$(): Observable<any> {
    return this._showNodesError$.asObservable().pipe(distinctUntilChanged());
  }

  private showCompareGraphSubject = new Subject<void>();
  showCompareGraphSubject$ = this.showCompareGraphSubject.asObservable(); // 暴露为 observable
  notifyShowCompareGraph() {
    this.showCompareGraphSubject.next(); // 主动触发
  }

  private adpVersionHeaders$ = new BehaviorSubject<{ adpVersion?: string; adpStatus?: string }>({});
  updateAdpVersionHeaders(headers: { adpVersion: string; adpStatus: string }) {
    this.adpVersionHeaders$.next(headers);
  }
  get adpVersionHeaders(): { [key: string]: string } {
    return this.adpVersionHeaders$.value;
  }

  isChangeVersionCanNotSave = false; // 切换版本的时候 不要调保存版本的接口

  isSaveLoading: boolean = false; // 保存按钮loading
  _isGetVariableByProTask$ = new BehaviorSubject<any>(false); // 是否根据前序流程任务获取变量
  get isGetVariableByProTask$(): Observable<any> {
    return this._isGetVariableByProTask$.asObservable();
  }

  constructor(private modal: AdModalService, private t: TranslateService) {}

  currentSelectedVersion: any = {};
  beforeSelectedVersion: any = {}; //接口报错回滚

  public get defaultDtdVariable() {
    return [
      {
        code: 'init_b1f78dc74c0c3c019c902e3d3375e8ca',
        dataType: 'DTDVar',
        description: '发起流程数据',
        isArray: false,
        mappingCode: 'data',
        required: false,
        varName: 'data',
      },
    ];
  }

  public setCurSelectedVerison(newVersion) {
    this.beforeSelectedVersion = this.currentSelectedVersion;
    this.currentSelectedVersion = newVersion;
  }

  //更新备注信息的时候用
  public updateCurSelectedVerison(curVersion) {
    this.currentSelectedVersion = curVersion;
  }

  public rebackSelectedVersion() {
    this.currentSelectedVersion = this.beforeSelectedVersion;
  }

  public resetContentChangeCheckObject() {
    this._contentChangeCheckObject = null;
  }

  public setContentChangeCheckObject(checkObject: CheckObject = null, isContentChangeWithoutSave: boolean = null) {
    this._contentChangeCheckObject = { checkObject, isContentChangeWithoutSave };
  }

  private getIsContentChangeWithoutSave(): boolean {
    const { checkObject, isContentChangeWithoutSave } = this._contentChangeCheckObject || {};
    return isContentChangeWithoutSave ?? checkObject?.contentComponent?.[checkObject?.checkFunction]?.() ?? false;
  }

  /**
   * 检测属性面板的值是否变化
   * @returns
   */
  public checkPropsPanelCanContinue(
    widthModal?: boolean,
  ): Promise<boolean | { result: boolean; confirmOpen?: boolean }> {
    return new Promise<boolean | { result: boolean; confirmOpen?: boolean }>((resolve) => {
      const isChanged = this.getIsContentChangeWithoutSave();
      if (!isChanged) {
        resolve(true);
      } else {
        this.modal.confirm({
          nzTitle: this.t.instant('dj-当前页面存在未保存的修改，是否确认离开？'),
          nzOkText: this.t.instant('dj-离开'),
          nzCancelText: this.t.instant('dj-取消'),
          nzOnOk: () => {
            const prosContentComponentRef = this.propsComponentRef?.getPropsContentRef();
            // 设置离开标识
            prosContentComponentRef?.setFlagLeave(true);
            resolve(widthModal ? { result: true, confirmOpen: true } : true);
          },

          nzOnCancel: () => resolve(false),
        });
      }
    });
  }

  public setDesignerComponent(comp: DtdDesignerViewComponent): void {
    this._dtdDesignerComponent = comp;
  }

  /**
   * 关闭属性面板
   */
  public closePanel(): void {
    this._dtdDesignerComponent?.dtdDesignerRef?.handleClosePanel();
  }

  /**
   * 获取属性面板的实例
   * @param comp
   */
  public setPropsComponentRef(comp: PropsPanelComponent) {
    this.propsComponentRef = comp;
  }

  /**
   * 设置当前属性面板的code
   * 项目code、任务code、数据状态code之一（根据展示内容）
   * @param code
   */
  public setPropsCode(code: string) {
    this.currentPropsCode = code;
  }

  public getPropsCode() {
    return this.currentPropsCode;
  }
}
