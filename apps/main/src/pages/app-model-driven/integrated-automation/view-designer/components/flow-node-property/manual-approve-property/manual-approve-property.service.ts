import { Injectable } from '@angular/core';
import { SystemConfigService } from 'common/service/system-config.service';
import { HttpClient } from '@angular/common/http';
import { Observable, Subject } from 'rxjs';
import { AppService } from '../../../../../../apps/app.service';

@Injectable()
export class ManualApprovePropertyService {
  private adesignerUrl: string;
  bindFormChange$: Subject<any> = new Subject();
  constructor(
    private systemConfigService: SystemConfigService,
    public appService: AppService,
    private http: HttpClient,
  ) {
    this.systemConfigService.get('adesignerUrl').subscribe((url) => {
      this.adesignerUrl = url;
    });
  }
  /**
   * 查询当前解决方案下的基础资料
   * @returns
   */
  loadBasicByAppCode(): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/activityConfigs/getActivityListByPatternAndApplication`;
    return this.http.get(url, { params: { pattern: 'DATA_ENTRY', application: this.appService?.selectedApp?.code } });
  }

  /**
   * 根据作业code去查询actionId
   * @returns
   */
  getActionIdByCode(code): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/pageDesign/getActionIdByCode`;
    return this.http.get(url, { params: { code: code } });
  }
}
