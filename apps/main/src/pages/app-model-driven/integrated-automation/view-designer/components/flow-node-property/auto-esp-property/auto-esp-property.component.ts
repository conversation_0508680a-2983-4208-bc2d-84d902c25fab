import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NameValidators } from '../../../config/utils';
import { debounce, omit } from 'lodash';
import { ViewApiService } from '../../../service/api.service';
import { ViewStoreService } from '../../../service/store.service';

@Component({
  selector: 'app-auto-esp-property',
  templateUrl: './auto-esp-property.component.html',
  styleUrls: ['./auto-esp-property.component.less'],
})
export class AutoEspPropertyComponent implements OnInit, OnChanges {
  // 输入的渲染数据
  @Input() data;
  @Input() nodeId;
  // 通知父组件关闭属性面板
  @Output() close: EventEmitter<any> = new EventEmitter();
  // 表单数据改变通过校验后向父组件通知更新数据
  @Output() changeData: EventEmitter<any> = new EventEmitter();
  dataFormGroup;
  scriptData: any;
  errorTips: Record<string, Record<string, string>> = {
    default: {
      required: this.translate.instant('dj-必填'),
      pattern: this.translate.instant('dj-超长'),
    },
  };
  currScriptControlName: string;
  scriptModal: boolean;
  // action开窗是否显示
  espActionVisible: boolean;
  nameLang: any;
  formGroupValidityFlag = false;
  produces: any;
  constructor(
    private translate: TranslateService,
    private fb: FormBuilder,
    private service: ViewApiService,
    public viewStoreService: ViewStoreService,
  ) {}

  ngOnInit(): void {
    this.handleInit();
    this.getCurrentData();
    this.dataFormGroup.valueChanges.subscribe(() => {
      if (!this.formGroupValidityFlag) {
        this.getCurrentData();
      }
    });
  }
  ngOnChanges(changes: SimpleChanges): void {
    if (Object.keys(changes).includes('nodeId')) {
      if (!changes.nodeId.firstChange) {
        this.handleChangeValue();
      }
    }
  }

  handleChangeValue() {
    // 为了patchValue不触发valueChanges
    this.formGroupValidityFlag = true;
    this.dataFormGroup.patchValue({
      id: this.data?.id,
      name: this.data?.name,
      scheduleRule:
        this.data?.serviceConfig?.scheduleRule?.schedule_type === '0'
          ? {
              schedule_type: this.data?.serviceConfig?.scheduleRule?.schedule_type,
              delay_seconds: this.data?.serviceConfig?.scheduleRule?.delay_seconds,
            }
          : {
              schedule_type: this.data?.serviceConfig?.scheduleRule?.schedule_type,
            },
      header:
        typeof this.data?.serviceConfig.header === 'string'
          ? this.data?.serviceConfig.header
          : JSON.stringify(this.data?.serviceConfig.header),
      prod: this.data?.serviceConfig.prod,
      serviceName: this.data?.serviceConfig.serviceName,
      method: this.data?.serviceConfig.method,
      requestScript: this.data?.serviceConfig.requestScript,
      responseScript: this.data?.serviceConfig.responseScript,
      isAsync: this.data?.serviceConfig.isAsync,
    });
    this.formGroupValidityFlag = false;
    this.nameLang = this.data.lang;
    this.getCurrentData();
  }

  handleClosePanel(): void {
    this.close.emit();
  }
  showScriptModal(controlName) {
    this.currScriptControlName = controlName;
    this.scriptData = this.dataFormGroup.get(controlName)?.value;
    this.scriptModal = true;
  }
  handleCloseSript(flag, value) {
    if (flag === 'confirm') {
      this.dataFormGroup.patchValue({
        [this.currScriptControlName]: value,
      });
    }
    this.scriptModal = false;
  }
  // 显示出action选择框
  showEspAction() {
    this.espActionVisible = true;
  }
  // action选择后选择回填更新
  handleSelectAction(data, type) {
    if (data.actionId) {
      const actionId = data.actionId.startsWith('esp_') ? data.actionId.replace('esp_', '') : data.actionId;
      this.dataFormGroup.get(type).setValue(actionId);
      this.espActionVisible = false;
      // 获取产品名称,并填入第一个值
      this.getAPiProvider(actionId, () => {
        if (this.produces?.length) {
          this.handleSelect(this.produces[0]);
        }
      });
    }
  }

  /**
   * 获取产品名称
   * @param apiName 服务名称
   */
  getAPiProvider(apiName, callback?) {
    this.service.getApiProvider(apiName).subscribe((res: any) => {
      if (res.code === 0) {
        this.produces = res.data;
        callback && callback();
      }
    });
  }

  handleSelect(item) {
    this.dataFormGroup.get('prod').setValue(item);
  }

  // 根据输入的组装fromgroup
  handleInit(): void {
    // 初始化fromgroup
    // 直接校验
    // 对父组件发出change事件主要里面混入isVerificationPassed
    let scheduleRule = this.fb.group({
      schedule_type: [this.data?.serviceConfig?.scheduleRule?.schedule_type || ''],
    });
    if (this.data?.serviceConfig?.scheduleRule?.schedule_type === '0') {
      scheduleRule = this.fb.group({
        schedule_type: [this.data?.serviceConfig?.scheduleRule?.schedule_type || ''],
        delay_seconds: [this.data?.serviceConfig?.scheduleRule?.delay_seconds || '', [Validators.required]],
      });
    }
    this.dataFormGroup = this.fb.group({
      id: [this.data?.id || null, [Validators.required]],
      name: [this.data?.name || '', [NameValidators.trim, Validators.required, Validators.pattern('^.{0,255}$')]],
      scheduleRule: scheduleRule,
      header: [
        typeof this.data?.serviceConfig.header === 'string'
          ? this.data?.serviceConfig.header
          : JSON.stringify(this.data?.serviceConfig.header),
      ],
      prod: [this.data?.serviceConfig?.prod || ''],
      serviceName: [this.data?.serviceConfig?.serviceName || ''],
      method: [this.data?.serviceConfig?.method || ''],
      requestScript: [this.data?.serviceConfig?.requestScript],
      responseScript: [this.data?.serviceConfig?.responseScript],
      isAsync: [this.data?.serviceConfig?.isAsync || false],
    });
    this.nameLang = this.data.lang;
    if (this.data?.serviceConfig?.serviceName && this.data?.serviceConfig?.serviceName !== '') {
      this.getAPiProvider(this.data?.serviceConfig?.serviceName, () => {});
    }
  }

  handleScheduleTypeChange(value) {
    const scheduleRuleGroup = this.dataFormGroup.get('scheduleRule') as FormGroup;
    if (value === '0') {
      scheduleRuleGroup.addControl('delay_seconds', this.fb.control(null, Validators.required));
      return;
    }
    scheduleRuleGroup.removeControl('delay_seconds');
  }

  handlePatchApp(key: any, data: any): void {
    if (data.needLang) {
      this.nameLang = {
        ...(this.nameLang || {}),
        [key]: data.lang,
      };
    }
    this.dataFormGroup.patchValue({
      [key]: data?.value,
    });
  }

  // 获取当前最新的数据
  getCurrentData = debounce(
    () => {
      this.formGroupValidityFlag = true;
      for (const i of Object.keys(this.dataFormGroup?.controls)) {
        this.dataFormGroup.controls[i].markAsDirty();
        this.dataFormGroup.controls[i].updateValueAndValidity();
      }
      const deepValidateKey = ['schedule_type', 'delay_seconds'];
      for (let i of deepValidateKey) {
        this.dataFormGroup.get('scheduleRule').get(i)?.markAsDirty();
        this.dataFormGroup.get('scheduleRule').get(i)?.updateValueAndValidity();
      }
      this.formGroupValidityFlag = false;
      const currentData = this.dataFormGroup.getRawValue();
      // 混入isVerificationPassed 是否校验通过
      const isVerificationPassed = this.viewStoreService.transformVerificationPassed(this.dataFormGroup.valid);
      if (currentData.header) {
        try {
          currentData.header = JSON.parse(currentData.header);
        } catch (error) {}
      }
      let serviceConfigData = { serviceConfig: { ...this.data.serviceConfig, ...omit(currentData, 'name') } };
      const returnData = {
        data: Object.assign(this.data, serviceConfigData, { name: currentData.name, lang: this.nameLang }),
        isVerificationPassed,
      };
      this.changeData.emit(returnData);
      return returnData;
    },
    150,
    { leading: false, trailing: true },
  );
}
