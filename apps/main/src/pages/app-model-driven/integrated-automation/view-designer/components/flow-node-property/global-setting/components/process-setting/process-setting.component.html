<form nz-form class="global-content" [formGroup]="dataFormGroup" [nzNoColon]="true">
  <nz-collapse [nzBordered]="false">
    <!--业务流入参设置-->
    <nz-collapse-panel
      style="padding-bottom: 6px"
      [nzHeader]="'dj-业务流入参设置' | translate"
      [nzActive]="true"
      [nzExpandedIcon]="'caret-right'"
    >
      <nz-form-item class="nz-form-item-content" formGroupName="bindForm">
        <nz-form-control>
          <!--节点名称-->
          <div class="form-item">
            <div class="item-title">
              {{ 'dj-开始回调类型' | translate }}
              <i
                adIcon
                iconfont="iconshuomingwenzi"
                aria-hidden="true"
                class="question-icon"
                nzTooltipTrigger="hover"
                nz-tooltip
                [nzTooltipTitle]="'dj-流程启动后是否需要回写业务数据状态，例：更新模型业务数据状态为审批中' | translate"
              >
              </i>
            </div>
            <ad-select
              [nzAllowClear]="false"
              formControlName="type"
              [nzPlaceHolder]="'dj-请选择' | translate"
              style="width: 100%"
            >
              <ad-option [nzLabel]="'dj-无' | translate" [nzValue]="EType.NONE"></ad-option>
              <ad-option [nzLabel]="'dj-模型' | translate" [nzValue]="EType.MODEL"></ad-option>
              <ad-option [nzLabel]="'ESP'" [nzValue]="EType.ESP"></ad-option>
            </ad-select>
          </div>
        </nz-form-control>
      </nz-form-item>

      <nz-form-item class="nz-form-item-content" formGroupName="bindForm" *ngIf="EType.MODEL === currentType">
        <nz-form-control [nzAutoTips]="errorTips">
          <!--节点名称-->
          <div class="form-item">
            <div class="item-title">
              {{ 'dj-开始回调' | translate }}
              <span class="item-required">*</span>
              <i
                adIcon
                iconfont="iconshuomingwenzi"
                aria-hidden="true"
                class="question-icon"
                nzTooltipTrigger="hover"
                nz-tooltip
                [nzTooltipTitle]="
                  'dj-根据回调类型，选择相应内容。若选择回调ESP，则对应调用的ESP需包含相应的业务逻辑' | translate
                "
              >
              </i>
            </div>

            <ad-select
              formControlName="modelCode"
              [nzPlaceHolder]="'dj-选择模型' | translate"
              style="width: 100%"
              nzAllowClear="true"
            >
              <ad-option
                [nzLabel]="modal.lang?.name?.[('dj-LANG' | translate)] || modal.name"
                [nzValue]="modal.code"
                *ngFor="let modal of modelList"
              ></ad-option>
            </ad-select>
          </div>
        </nz-form-control>
      </nz-form-item>

      <nz-form-item class="nz-form-item-content" formGroupName="bindForm" *ngIf="EType.ESP === currentType">
        <nz-form-control [nzAutoTips]="errorTips">
          <!--节点名称-->
          <div class="form-item">
            <div class="item-title">
              {{ 'dj-开始回调' | translate }}
              <span class="item-required">*</span>
              <i
                adIcon
                iconfont="iconshuomingwenzi"
                aria-hidden="true"
                class="question-icon"
                nzTooltipTrigger="hover"
                nz-tooltip
                [nzTooltipTitle]="
                  'dj-根据回调类型，选择相应内容。若选择回调ESP，则对应调用的ESP需包含相应的业务逻辑' | translate
                "
              >
              </i>
            </div>
            <nz-input-group [nzSuffix]="suffixIcon">
              <input readonly nz-input formControlName="actionId" [placeholder]="'dj-请选择' | translate" />
            </nz-input-group>
            <ng-template #suffixIcon>
              <i
                adIcon
                iconfont="iconkaichuang"
                aria-hidden="true"
                class="window-icon iconfont"
                (click)="handleOpenAction()"
              >
              </i>
            </ng-template>
          </div>
        </nz-form-control>
      </nz-form-item>

      <nz-form-item class="nz-form-item-content" formGroupName="bindForm" *ngIf="EType.ESP === currentType">
        <nz-form-control [nzAutoTips]="errorTips">
          <div class="form-item">
            <div class="item-title">
              {{ 'dj-产品名' | translate }}
              <span class="item-required">*</span>
            </div>
            <ad-select
              formControlName="serviceCode"
              [nzPlaceHolder]="'dj-请选择' | translate"
              style="width: 100%"
              [nzAllowClear]="false"
            >
              <ad-option [nzLabel]="item" [nzValue]="item" *ngFor="let item of produces"></ad-option>
            </ad-select>
          </div>
        </nz-form-control>
      </nz-form-item>

      <nz-form-item class="nz-form-item-content" formGroupName="bindForm">
        <nz-form-control [nzErrorTip]="translate.instant('dj-请选择界面')">
          <div class="form-item flex-form-item">
            <div class="item-title">
              {{ 'dj-任务卡初始化界面默认绑定' | translate }}
              <i
                adIcon
                iconfont="iconshuomingwenzi"
                aria-hidden="true"
                class="question-icon"
                nzTooltipTrigger="hover"
                nz-tooltip
                [nzTooltipTitle]="
                  'dj-签核节点将根据该界面初始化任务卡，如需调整单个节点的初始化界面，可前往相应签核节点调整'
                    | translate
                "
              >
              </i>
            </div>
            <div class="page-select">
              <nz-spin *ngIf="loadingBasic" nzSize="small" nzSimple class="select-loading"> </nz-spin>
              <ad-select
                formControlName="formCode"
                [nzPlaceHolder]="'dj-请选择界面' | translate"
                style="width: 100%"
                nzAllowClear="true"
              >
                <ad-option
                  [nzLabel]="page.lang?.name?.[('dj-LANG' | translate)] || page.name"
                  [nzValue]="page.code"
                  *ngFor="let page of formList"
                ></ad-option>
              </ad-select>
            </div>
          </div>
        </nz-form-control>
      </nz-form-item>

      <nz-form-item>
        <div class="form-item-tip">
          <i style="margin: 4px 4px 0 0" adIcon type="bulb" theme="outline"></i>
          <span>{{ 'dj-选择绑定界面后，后续的签核节点将会默认使用该方案' | translate }}</span>
        </div>
      </nz-form-item>
    </nz-collapse-panel>
  </nz-collapse>
</form>

<!--action开窗组件-->
<app-action-modal
  *ngIf="actionModal"
  [transferModal]="actionModal"
  [transferData]="actionData"
  [favouriteCode]="dataSourceService.favouriteCode"
  [applicationCodeProxy]="dataSourceService.applicationCodeProxy"
  labelType="EspAction"
  (callBack)="handleConfirmAction($event)"
  (closeModal)="actionModal = false"
>
</app-action-modal>
