<form nz-form class="card-setting-content" [formGroup]="dataFormGroup">
  <nz-collapse [nzBordered]="false">
    <!--基本信息的面板-->
    <nz-collapse-panel [nzHeader]="'dj-基本信息' | translate" [nzActive]="true" [nzExpandedIcon]="'caret-right'">
      <nz-form-item class="nz-form-item-content">
        <nz-form-control [nzAutoTips]="errorTips">
          <!--节点id-->
          <div class="form-item">
            <div class="item-title">
              {{ 'dj-节点id' | translate }}
              <span class="item-required">*</span>
            </div>
            <app-component-input
              [attr]="{
                name: '节点id',
                required: true,
                needLang: false,
                readOnly: true
              }"
              style="width: 100%"
              formControlName="id"
              [value]="dataFormGroup.get('id')?.value"
              ngDefaultControl
            >
            </app-component-input>
          </div>
        </nz-form-control>
      </nz-form-item>

      <nz-form-item class="nz-form-item-content">
        <nz-form-control [nzAutoTips]="errorTips">
          <!--节点名称-->
          <div class="form-item">
            <div class="item-title">
              {{ 'dj-节点名称' | translate }}
              <span class="item-required">*</span>
            </div>
            <app-modal-input
              formControlName="name"
              ngDefaultControl
              [innerLabel]="false"
              [attr]="{
                code: 'name',
                name: '节点名称',
                required: true,
                needLang: true,
                lang: lang?.name
              }"
              style="width: 100%"
              [innerLabel]="false"
              [value]="lang?.name?.[('dj-LANG' | translate)]"
              (callBack)="handlePatchLang('name', $event)"
            >
            </app-modal-input>
          </div>
        </nz-form-control>
      </nz-form-item>
    </nz-collapse-panel>
    <!-- 任务信息 -->
    <nz-collapse-panel [nzHeader]="'dj-任务信息' | translate" [nzActive]="true" [nzExpandedIcon]="'caret-right'">
      <nz-form-item class="nz-form-item-content">
        <nz-form-control [nzAutoTips]="errorTips">
          <!--展示标题-->
          <div class="form-item">
            <div class="item-title">
              {{ 'dj-展示标题' | translate }}
              <span class="item-required">*</span>
              <i
                adIcon
                iconfont="iconshuomingwenzi"
                aria-hidden="true"
                class="question-icon"
                nzTooltipTrigger="hover"
                nz-tooltip
                [nzTooltipTitle]="'dj-节点的展示标题，支持变量传参。配置后将展示为任务卡标题' | translate"
              >
              </i>
            </div>
            <app-modal-input
              [innerLabel]="false"
              [attr]="{
                name: '展示标题',
                required: true,
                needLang: true,
                lang: lang?.taskName
              }"
              style="width: 100%"
              [innerLabel]="false"
              formControlName="taskName"
              [value]="lang?.taskName?.[('dj-LANG' | translate)]"
              ngDefaultControl
              (callBack)="handlePatchLang('taskName', $event)"
            >
            </app-modal-input>
          </div>
        </nz-form-control>
      </nz-form-item>

      <nz-form-item class="nz-form-item-content">
        <nz-form-control [nzAutoTips]="errorTips">
          <!--流程表单-->
          <div class="form-item">
            <div class="item-title">
              {{ 'dj-任务界面' | translate }}
              <span class="item-required">*</span>
              <span (click)="handlePreview()" style="color: #6a4cff; cursor: pointer" *ngIf="!isCustomizeCard">{{
                'dj-预览' | translate
              }}</span>
              <span (click)="handleEdit('detail')" style="color: #6a4cff; cursor: pointer" *ngIf="isCustomizeCard">{{
                'dj-编辑' | translate
              }}</span>
            </div>
            <div style="display: flex; justify-content: space-between; align-items: center">
              <div class="page-select">
                <nz-spin *ngIf="loadingBasic" nzSize="small" nzSimple class="select-loading"> </nz-spin>
                <ad-select
                  [required]="true"
                  formControlName="formCode"
                  [nzPlaceHolder]="'dj-请选择界面' | translate"
                  style="width: 100%"
                  nzAllowClear="true"
                  [nzDropdownRender]="cursomSelectTemplate"
                >
                  <ad-option
                    [nzLabel]="(page.lang?.name?.[('dj-LANG' | translate)] || page.name)"
                    [nzValue]="page.code"
                    *ngFor="let page of formList"
                  ></ad-option>
                  <ad-option
                    *ngIf="pageView?.code"
                    [nzLabel]="pageView?.lang?.name?.[('dj-LANG' | translate)] ?? ''"
                    [nzValue]="pageView?.code"
                    nzCustomContent
                  >
                    <div class="custom-select-item">
                      <div class="custom-select-item-label">
                        {{ pageView?.lang?.name?.[('dj-LANG' | translate)] ?? '' }}
                      </div>
                      <div class="custom-select-item-tag">
                        {{ 'dj-自定义任务' | translate }}
                      </div>
                    </div>
                  </ad-option>
                </ad-select>
              </div>
            </div>
          </div>
        </nz-form-control>
      </nz-form-item>
    </nz-collapse-panel>
    <!-- 任务卡设置面板 -->
    <nz-collapse-panel [nzHeader]="'dj-高级设置' | translate" [nzActive]="true" [nzExpandedIcon]="'caret-right'">
      <!--任务卡摘要设置-->
      <div class="form-item switch-form-item">
        <div class="item-title switch-item-title">
          {{ 'dj-自定义摘要' | translate }}
          <i
            adIcon
            iconfont="iconshuomingwenzi"
            aria-hidden="true"
            class="question-icon"
            nzTooltipTrigger="hover"
            nz-tooltip
            [nzTooltipTitle]="'dj-开启任务卡摘要设计，运行态将根据所设定的卡面摘要进行卡片展示' | translate"
          >
          </i>
        </div>
        <nz-switch class="custom-project-card-switch" formControlName="enableCustomDigest" nzSize="small"></nz-switch>
        <span *ngIf="dataFormGroup.get('enableCustomDigest').value" (click)="handleEdit('card')" class="edit-icon">{{
          'dj-编辑' | translate
        }}</span>
      </div>

      <!--里程碑设置-->
      <div class="form-item switch-form-item">
        <div class="item-title switch-item-title">
          {{ 'dj-设为里程碑节点' | translate }}
          <span class="item-required">*</span>
          <i
            adIcon
            iconfont="iconshuomingwenzi"
            aria-hidden="true"
            class="question-icon"
            nzTooltipTrigger="hover"
            nz-tooltip
            [nzTooltipTitle]="'dj-勾选后，该节点将会作为项目里程碑节点展示' | translate"
          >
          </i>
        </div>
        <nz-switch class="custom-project-card-switch" formControlName="milestone" nzSize="small"></nz-switch>
      </div>

      <!-- 无任务隐藏里程碑 -->
      <div class="form-item switch-form-item" *ngIf="dataFormGroup.get('milestone').value">
        <div class="item-title switch-item-title">
          {{ 'dj-里程碑节点隐藏判断' | translate }}
          <i
            adIcon
            iconfont="iconshuomingwenzi"
            aria-hidden="true"
            class="question-icon"
            nzTooltipTrigger="hover"
            nz-tooltip
            [nzTooltipTitle]="
              'dj-开启后，当里程碑关联的任务未从产生的情况下，该里程碑节点将项目详情页的里程碑上展示' | translate
            "
          >
          </i>
        </div>
        <nz-switch class="custom-project-card-switch" formControlName="isHideKey" nzSize="small"></nz-switch>
      </div>

      <!-- 任务卡合并 -->
      <div class="form-item switch-form-item">
        <div class="item-title switch-item-title">
          {{ 'dj-任务卡合并' | translate }}
          <i
            adIcon
            iconfont="iconshuomingwenzi"
            aria-hidden="true"
            class="question-icon"
            nzTooltipTrigger="hover"
            nz-tooltip
            [nzTooltipTitle]="'dj-任务卡合并tips' | translate"
          >
          </i>
        </div>
        <nz-switch class="custom-project-card-switch" formControlName="openTaskMerge" nzSize="small"></nz-switch>
      </div>

      <!-- 分批提交 -->
      <div class="form-item switch-form-item">
        <div class="item-title switch-item-title">
          {{ 'dj-分批提交' | translate }}
          <i
            adIcon
            iconfont="iconshuomingwenzi"
            aria-hidden="true"
            class="question-icon"
            nzTooltipTrigger="hover"
            nz-tooltip
            [nzTooltipTitle]="'dj-分批提交tips' | translate"
          >
          </i>
        </div>
        <nz-switch class="custom-project-card-switch" formControlName="openGroupSubmit" nzSize="small"></nz-switch>
      </div>

      <!-- 拓展信息配置 -->
      <div class="form-item switch-form-item">
        <div class="item-title switch-item-title">
          {{ 'dj-扩展信息配置' | translate }}
          <i
            adIcon
            iconfont="iconshuomingwenzi"
            aria-hidden="true"
            class="question-icon"
            nzTooltipTrigger="hover"
            nz-tooltip
            [nzTooltipTitle]="'dj-针对任务卡扩展信息进行配置， 如任务卡合并规则等' | translate"
          >
          </i>
        </div>
        <span class="extend-setting-icon" (click)="handleExtendedInfo($event)">
          <i adIcon iconfont="iconsheding" aria-hidden="true"></i>
          {{ 'dj-配置' | translate }}
        </span>
      </div>

      <!--任务催办-->
      <div class="form-item switch-form-item">
        <div class="item-title switch-item-title">
          {{ 'dj-任务催办' | translate }}
          <i
            adIcon
            iconfont="iconshuomingwenzi"
            aria-hidden="true"
            class="question-icon"
            nzTooltipTrigger="hover"
            nz-tooltip
            [nzTooltipTitle]="'dj-开启后需前往交付设计器进行催办逻辑设置' | translate"
          >
          </i>
        </div>
        <nz-switch class="custom-project-card-switch" formControlName="urging" nzSize="small"></nz-switch>
      </div>

      <!-- 转派 -->
      <div class="form-item switch-form-item" *ngIf="isCustomizeCard">
        <div class="item-title switch-item-title">{{ 'dj-转派' | translate }}</div>
        <nz-switch class="custom-project-card-switch ml4" formControlName="transferButton" nzSize="small"></nz-switch>
      </div>

      <!-- <div formGroupName="extendFields" class="form-item switch-form-item">
        <div class="item-title switch-item-title">
          {{ 'dj-是否卡控' | translate }}
          <i
            adIcon
            iconfont="iconshuomingwenzi"
            aria-hidden="true"
            class="question-icon"
            nzTooltipTrigger="hover"
            nz-tooltip
            [nzTooltipTitle]="'dj-是否需要在关闭一级功能页签等设置' | translate"
          >
          </i>
        </div>
        <nz-switch class="custom-project-card-switch ml4" formControlName="closeNeedConfirm" nzSize="small"></nz-switch>
      </div> -->
    </nz-collapse-panel>

    <!--到期策略-->
    <nz-collapse-panel
      style="padding-bottom: 6px"
      [nzHeader]="'dj-到期策略' | translate"
      [nzActive]="true"
      [nzExpandedIcon]="'caret-right'"
    >
      <nz-form-item class="nz-form-item-content" formGroupName="planEndTime">
        <nz-form-control [nzErrorTip]="errorTips">
          <div class="form-item">
            <div class="item-title">
              {{ 'dj-任务预计完成日设定' | translate }}
              <span class="item-required">*</span>
            </div>
            <nz-radio-group formControlName="settingType" [required]="true">
              <label nz-radio nzValue="no">{{ 'dj-不启用' | translate }}</label>
              <label nz-radio nzValue="calculation">{{ 'dj-计算到期日' | translate }}</label>
              <label nz-radio nzValue="specify">{{ 'dj-指定到期日' | translate }}</label>
            </nz-radio-group>
          </div>
        </nz-form-control>
      </nz-form-item>

      <div
        class="form-item-time form-item"
        *ngIf="dataFormGroup.get('planEndTime').get('settingType')?.value === 'calculation'"
      >
        <div class="item-title">
          {{ 'dj-到期日设置' | translate }}
          <span class="item-required">*</span>
        </div>
        <div class="flex-form-item">
          <span style="margin-right: 3px"> {{ 'dj-发卡后' | translate }}</span>
          <nz-form-item class="nz-form-item-content" formGroupName="planEndTime">
            <nz-form-control [nzErrorTip]="translate.instant('dj-请输入数值')">
              <div class="flex-form-item multi-form-item">
                <nz-input-group nzCompact style="display: flex">
                  <ad-input-number
                    formControlName="dateValue"
                    [ngModelOptions]="{ standalone: true }"
                    [min]="1"
                    [step]="1"
                    [max]="9999"
                    [precision]="0"
                    precisionMode="cut"
                    width="90px"
                  >
                  </ad-input-number>
                  <ad-select
                    class="time-type-select"
                    formControlName="dateType"
                    [ngModelOptions]="{ standalone: true }"
                    style="width: 96px"
                    [nzPlaceHolder]="'dj-请选择' | translate"
                    nzAllowClear="false"
                  >
                    <ad-option [nzLabel]="'dj-分钟' | translate" [nzValue]="'minute'"></ad-option>
                    <ad-option [nzLabel]="'dj-小时' | translate" [nzValue]="'hour'"></ad-option>
                    <ad-option [nzLabel]="'dj-天' | translate" [nzValue]="'day'"></ad-option>
                    <ad-option [nzLabel]="'dj-周' | translate" [nzValue]="'week'"></ad-option>
                  </ad-select>
                </nz-input-group>
              </div>
            </nz-form-control>
          </nz-form-item>

          <span style="margin-left: 3px">{{ 'dj-到期' | translate }}</span>
        </div>
      </div>

      <div
        class="form-item-time form-item"
        *ngIf="dataFormGroup.get('planEndTime').get('settingType')?.value === 'specify'"
      >
        <div class="flex-form-item">
          <nz-form-item class="nz-form-item-content form-item-planEndTime" formGroupName="planEndTime">
            <nz-form-control
              [nzErrorTip]="'dj-请输入' | translate"
              *ngIf="dataFormGroup.get('planEndTime')?.get('sourceType')?.value == 'constant'"
            >
              <nz-input-group [nzAddOnBefore]="addOnBeforeTemplate">
                <ad-date-picker
                  nzFormat="yyyy/MM/dd HH:mm:ss"
                  [nzPlaceHolder]="'dj-请选择' | translate"
                  [nzShowToday]="false"
                  [nzShowTime]="true"
                  [nzAllowClear]="true"
                  class="end-type-picker"
                  formControlName="sourceValue"
                  style="width: 220px; min-width: 220px"
                ></ad-date-picker>
              </nz-input-group>
            </nz-form-control>
            <nz-form-control
              [nzErrorTip]="'dj-请输入' | translate"
              *ngIf="dataFormGroup.get('planEndTime')?.get('sourceType')?.value == 'variable'"
            >
              <nz-input-group [nzAddOnBefore]="addOnBeforeTemplate">
                <app-variable-select-input
                  *ngIf="dataFormGroup.get('planEndTime')?.get('sourceType')?.value == 'variable'"
                  style="width: 220px; min-width: 220px"
                  [inputable]="true"
                  [variableRange]="variableRange"
                  [nodeId]="nodeId"
                  formControlName="variableCode"
                  [item]="item"
                  [sufixKey]="'variableSuffix'"
                  [maxWidth]="220"
                  (onChanged)="handleValueChanged($event)"
                >
                </app-variable-select-input>
              </nz-input-group>
            </nz-form-control>
          </nz-form-item>
        </div>
      </div>
    </nz-collapse-panel>
  </nz-collapse>
</form>

<ng-template #cursomSelectTemplate>
  <div class="customSelect">
    <div class="divider"></div>
    <div
      [class.customSelectItem]="true"
      [class.customSelectDisabled]="isCustomSelectDisabled"
      (click)="showAddCustomTaskModal()"
    >
      <span class="font-class" adIcon iconfont="iconxinzeng2"></span>
      {{ 'dj-新增自定义任务' | translate }}
    </div>
  </div>
</ng-template>

<!-- 预览 -->
<app-component-preview-modal
  *ngIf="previewVisible"
  [(visible)]="previewVisible"
  [hiddenPreviewLeft]="true"
  [hiddenPreviewOperation]="true"
  [params]="params"
></app-component-preview-modal>

<!-- 新的卡面设计器 -->
<app-task-project-card-design
  *ngIf="workVisible && designType !== 'taskOnlyDetail'"
  [workVisible]="workVisible"
  [type]="designType"
  [workData]="workData"
  [uiKey]="uiKey"
  [hiddenPublishAndPreviewButton]="true"
  [extendHeader]="service.combineHeaders"
  [isTenantProcessId]="isTenantProcessId"
  [isFromDtdReference]="state.isFromDtdReference"
  (close)="handleWorkPageClose($event)"
></app-task-project-card-design>

<!-- 新的任务界面设计器 -->
<app-task-work-design-new
  *ngIf="workVisible && designType === 'taskOnlyDetail'"
  [workVisible]="workVisible"
  [type]="'taskOnlyDetail'"
  [workData]="workData"
  [uiKey]="uiKey"
  [hiddenPublishAndPreviewButton]="true"
  [extendHeader]="service.combineHeaders"
  [isTenantProcessId]="isTenantProcessId"
  [isFromDtdReference]="state.isFromDtdReference"
  (close)="handleWorkPageClose($event)"
></app-task-work-design-new>

<app-extended-info
  #extendedInfoRef
  class="icon"
  style="position: absolute; left: -99999px"
  [code]="data.id"
  [sceneCode]="'datamapTask'"
  [appCode]="appService.selectedApp.code"
  [extendHeader]="service.combineHeaders"
  [isTenantProcessId]="isTenantProcessId"
  [isFromDtdReference]="state.isFromDtdReference"
  [showName]="false"
  [notShowIcon]="false"
></app-extended-info>

<ng-template #addOnBeforeTemplate>
  <span class="field-paramType" nz-button nz-dropdown [nzDropdownMenu]="paramTypeDropDown">
    <i
      adIcon
      [iconfont]="fieldType[dataFormGroup.get('planEndTime')?.get('sourceType')?.value || 'constant']?.icon"
    ></i>
    <span style="margin-left: 4px" adIcon type="down" theme="outline"></span>
  </span>
  <nz-dropdown-menu #paramTypeDropDown="nzDropdownMenu">
    <ul nz-menu>
      <li
        nz-menu-item
        [ngClass]="{
          'field-menu-item': true,
          'field-menu-item-active': field === dataFormGroup.get('planEndTime')?.get('sourceType')?.value
        }"
        *ngFor="let field of fieldTypeEum"
        (click)="changeParamType(field)"
      >
        <i adIcon [iconfont]="fieldType[field].icon"></i
        ><span class="field-name" style="margin-left: 4px">{{ fieldType[field].name }}</span>
      </li>
    </ul>
  </nz-dropdown-menu>
</ng-template>
