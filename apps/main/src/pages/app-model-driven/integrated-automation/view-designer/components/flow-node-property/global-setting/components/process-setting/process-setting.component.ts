import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { GlobalSettingService } from '../../global-setting.service';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { EType } from '../../../../../config/typings';
import { NameValidators } from 'pages/app-model-driven/integrated-automation/view-designer/config/utils';
import { cloneDeep, isEmpty } from 'lodash';
import { ViewApiService } from 'pages/app-model-driven/integrated-automation/view-designer/service/api.service';
import { AppService } from 'pages/apps/app.service';
import { DataSourceService } from 'components/page-design/components/data-source/data-source.service';

@Component({
  selector: 'app-process-setting',
  templateUrl: './process-setting.component.html',
  styleUrls: ['./process-setting.component.less'],
  providers: [GlobalSettingService],
})
export class ProcessSettingComponent implements OnInit {
  // 输入的渲染数据
  @Input() data;
  // 表单数据改变通过校验后向父组件通知更新数据
  @Output() changeData: EventEmitter<any> = new EventEmitter();

  dataFormGroup;
  errorTips: Record<string, Record<string, string>> = {
    default: {
      required: this.translate.instant('dj-必填'),
      pattern: this.translate.instant('dj-超长'),
    },
  };

  modelList = []; // 模型列表
  formList = []; // 作业列表
  bindForm = {
    formCode: '', // 作业code
    modelCode: '', // 模型code
    serviceCode: '', // 服务code
    type: EType.MODEL, // 回掉类型dp 0:无 1:模型 2:ESP
    actionId: '', // api
  };
  get currentType() {
    return this.dataFormGroup?.get('bindForm')?.get('type')?.value;
  }

  EType = EType;
  actionModal: boolean;
  actionData: any; // 传递action数据
  produces: string[] = [];

  // 多语言单独处理，name title
  lang: any;

  loadingBasic: boolean = true;

  // updateValueAndValidity 会触发form的改变，误走单改变的监听事件中，导致再次调用getCurrentData造成死循环
  formGroupValidityFlag = false;

  constructor(
    public translate: TranslateService,
    private globalSettingService: GlobalSettingService,
    public dataSourceService: DataSourceService,
    private fb: FormBuilder,
    private service: ViewApiService,
  ) {}

  ngOnInit() {
    this.getModelAssignList();
    this.handleInit();
    this.getPageData(this.data.bindForm);
    this.handleDynamicRule(this.data?.bindForm.type ?? EType.MODEL);
    this.handleQueryApiIfNeed();
  }

  getModelAssignList(): void {
    this.globalSettingService.getModelDesignList().subscribe((res) => {
      this.modelList = res.data;
    });
  }

  // 根据输入的组装fromgroup
  handleInit(): void {
    const type = this.data?.bindForm.type ?? EType.MODEL;
    // 初始化fromgroup
    // 直接校验
    // 对父组件发出change事件主要里面混入isVerificationPassed
    this.dataFormGroup = this.fb.group({
      bindForm: this.fb.group({
        type: [type, [Validators.required]],
        modelCode: [this.data?.bindForm?.modelCode, type === EType.MODEL ? [Validators.required] : null],
        formCode: [this.data?.bindForm?.formCode],
        serviceCode: [this.data?.bindForm.serviceCode, type === EType.ESP ? [Validators.required] : null],
        actionId: [this.data?.bindForm.actionId, type === EType.ESP ? [Validators.required] : null],
      }),
    });
    this.dataFormGroup.valueChanges.subscribe(() => {
      if (!this.formGroupValidityFlag) {
        this.getCurrentData();
      }
    });
    this.dataFormGroup
      .get('bindForm')
      .get('modelCode')
      .valueChanges.subscribe((value) => {
        if (!this.formGroupValidityFlag) {
          this.handleChangeModelCode(value);
        }
      });
    this.dataFormGroup
      .get('bindForm')
      .get('type')
      .valueChanges.subscribe((value) => {
        this.handleDynamicRule(value);
        this.dataFormGroup.get('bindForm').patchValue({
          actionId: undefined,
          serviceCode: undefined,
          modelCode: undefined,
        });
      });
  }

  ngAfterViewInit() {
    this.getCurrentData();
  }

  /**
   * 改变模型的回调
   * @param modelCode
   */
  handleChangeModelCode(modelCode: any): void {
    const modelItem = this.modelList.find((data) => data.code === modelCode);
    if (!isEmpty(modelItem)) {
      this.dataFormGroup.get('bindForm').patchValue({
        formCode: '',
        serviceCode: modelItem.serviceCode,
      });
      this.getPageData({
        modelCode: modelCode,
        serviceCode: modelItem.serviceCode,
      });
    } else {
      // this.formList = [];
      this.dataFormGroup.get('bindForm').patchValue({
        formCode: '',
        serviceCode: '',
      });
    }
    this.dataFormGroup.get('bindForm').get('formCode').reset();
    this.getCurrentData();
  }

  getPageData(data: any = {}) {
    this.loadingBasic = true;
    this.globalSettingService.loadBasicByAppCode().subscribe(
      (res) => {
        this.formList = res.data;
        this.loadingBasic = false;
      },
      () => {
        this.loadingBasic = false;
      },
    );
  }

  /**
   * 动态设置FormRule
   * @param type
   */
  handleDynamicRule(type: EType): void {
    const form = this.dataFormGroup.get('bindForm');
    if (type === EType.ESP) {
      form.get('actionId').setValidators(Validators.required);
      form.get('serviceCode').setValidators(Validators.required);
      form.get('modelCode').setValidators(null);
    } else if (type === EType.MODEL) {
      form.get('actionId').setValidators(null);
      form.get('serviceCode').setValidators(null);
      form.get('modelCode').setValidators(Validators.required);
    } else {
      form.get('actionId').setValidators(null);
      form.get('serviceCode').setValidators(null);
      form.get('modelCode').setValidators(null);
    }
  }

  /**
   * 当type是ESP的时候，查询产品明
   */
  handleQueryApiIfNeed(): void {
    if (this.data.bindForm?.type === EType.ESP) {
      this.handleQueryApiProvider(this.data.bindForm?.actionId);
    }
  }

  // 打开ESP开窗
  handleOpenAction(): void {
    this.actionModal = true;
    this.actionData = {
      actionId: this.dataFormGroup?.get('bindForm')?.get('actionId')?.value,
      actionName: '',
      useApp: 'true',
    };
  }

  // 回填action
  handleConfirmAction(data: any): void {
    this.dataFormGroup.get('bindForm').patchValue({
      actionId: data.actionId,
    });
    this.actionModal = false;
    this.handleQueryApiProvider(data.actionId);
  }

  /**
   * 查询ESP的产品名
   * @param actionId
   */
  handleQueryApiProvider(actionId: string): void {
    const _actionId = actionId.startsWith('esp_') ? actionId.replace('esp_', '') : actionId;
    this.service.getApiProvider(_actionId).subscribe((res: any) => {
      this.produces = res.data || [];
      if (this.produces.length) {
        this.dataFormGroup.get('bindForm').patchValue({
          serviceCode: this.produces?.[0],
        });
      }
    });
  }

  /**
   *
   * @returns
   */
  getFormValidate() {
    this.formGroupValidityFlag = true;
    for (const i of Object.keys(this.dataFormGroup?.controls)) {
      this.dataFormGroup.controls[i].markAsDirty();
      this.dataFormGroup.controls[i].updateValueAndValidity();
    }
    const bindForm = this.dataFormGroup.get('bindForm') as FormGroup;
    for (const i of Object.keys(bindForm.controls)) {
      bindForm.controls[i].markAsDirty();
    }
    this.formGroupValidityFlag = false;
    return this.dataFormGroup.valid;
  }

  getCurrentData() {
    this.getFormValidate();
    let currentData = this.dataFormGroup.value;
    const returnData = {
      data: Object.assign(this.data, currentData),
      valid: this.dataFormGroup.valid,
      componentType: 'processSetting',
    };
    this.changeData.emit(returnData);
    return returnData;
  }
}
