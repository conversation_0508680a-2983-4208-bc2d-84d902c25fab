<div class="auto-wait-property-root submit-form">
  <div class="header">
    <span>{{ 'node-等待' | translate }}</span>
    <i adIcon iconfont="icondanchuxiaoxiguanbi" class="iconfont" aria-hidden="true" (click)="handleClosePanel()"></i>
  </div>
  <form nz-form class="content" [formGroup]="dataFormGroup">
    <nz-collapse [nzBordered]="false">
      <!--基本信息的面板-->
      <nz-collapse-panel [nzHeader]="'dj-基本信息' | translate" [nzActive]="true" [nzExpandedIcon]="'caret-right'">
        <div class="content-tip">
          <i adIcon iconfont="iconshuoming" class="iconfont" aria-hidden="true"></i>
          <span style="margin-left: 4px">{{
            'dj-等待节点用于将当前flow挂起，当触发继续执行时，则继续向下执行' | translate
          }}</span>
        </div>
        <nz-form-item class="nz-form-item-content">
          <nz-form-control [nzAutoTips]="errorTips">
            <!--节点id-->
            <div class="form-item">
              <div class="item-title">
                {{ 'dj-节点id' | translate }}
                <span class="item-required">*</span>
              </div>
              <app-component-input
                [attr]="{
                  name: '节点id',
                  required: true,
                  needLang: false,
                  readOnly: true
                }"
                style="width: 100%"
                formControlName="id"
                [value]="dataFormGroup.get('id')?.value"
                ngDefaultControl
              >
              </app-component-input>
            </div>
          </nz-form-control>
        </nz-form-item>

        <nz-form-item class="nz-form-item-content">
          <nz-form-control [nzAutoTips]="errorTips">
            <!--节点名称-->
            <div class="form-item">
              <div class="item-title">
                {{ 'dj-节点名称' | translate }}
                <span class="item-required">*</span>
              </div>
              <app-modal-input
                class="language-input"
                [attr]="{
                  name: 'name',
                  required: true,
                  lang: this.nameLang?.name,
                  needLang: true
                }"
                [innerLabel]="false"
                [value]="nameLang?.name?.[('dj-LANG' | translate)] || dataFormGroup.get('name')?.value"
                (callBack)="handlePatchApp('name', $event)"
                [formControlName]="'name'"
                ngDefaultControl
              ></app-modal-input>
            </div>
          </nz-form-control>
        </nz-form-item>
      </nz-collapse-panel>
      <nz-collapse-panel [nzHeader]="'dj-节点设置' | translate" [nzActive]="true" [nzExpandedIcon]="'caret-right'">
        <nz-form-item class="nz-form-item-content">
          <nz-form-control [nzAutoTips]="errorTips">
            <div class="form-item">
              <div class="item-title">
                {{ 'dj-等待唤起策略' | translate }}
                <span class="item-required">*</span>
              </div>
              <div>
                <nz-radio-group formControlName="wakeupStrategy">
                  <label nz-radio nzValue="manual">{{ 'dj-手动唤起' | translate }}</label>
                  <label nz-radio nzValue="auto"
                    >{{ 'dj-自动唤起' | translate
                    }}<i
                      adIcon
                      iconfont="iconshuomingwenzi"
                      aria-hidden="true"
                      class="question-icon"
                      nzTooltipTrigger="hover"
                      nz-tooltip
                      [nzTooltipTitle]="'dj-设定等待时间，达成时间后将结束等待，向下执行流程' | translate"
                    >
                    </i
                  ></label>
                </nz-radio-group>
              </div>
            </div>
          </nz-form-control>
        </nz-form-item>
        <div class="form-item-time" *ngIf="dataFormGroup.get('wakeupStrategy')?.value === 'auto'">
          <div class="item-title">
            {{ 'dj-唤起时间' | translate }}
            <span class="item-required">*</span>
          </div>
          <div class="flex-form-item dateValue">
            <span style="margin-right: 3px"> {{ 'dj-等待' | translate }}</span>
            <nz-form-item class="nz-form-item-content">
              <nz-form-control [nzErrorTip]="translate.instant('dj-请输入数值')">
                <div class="flex-form-item multi-form-item">
                  <nz-input-group nzCompact style="display: flex">
                    <ad-input-number
                      formControlName="dateValue"
                      [min]="1"
                      [step]="1"
                      [max]="9999"
                      [precision]="0"
                      precisionMode="cut"
                      width="120px"
                    >
                    </ad-input-number>
                  </nz-input-group>
                </div>
              </nz-form-control>
            </nz-form-item>
            <nz-form-item class="nz-form-item-content">
              <nz-form-control [nzErrorTip]="translate.instant('dj-请选择')">
                <div class="flex-form-item multi-form-item">
                  <nz-input-group nzCompact style="display: flex">
                    <ad-select
                      class="time-type-select"
                      formControlName="dateType"
                      style="width: 100px"
                      [nzPlaceHolder]="'dj-请选择' | translate"
                    >
                      <ad-option [nzLabel]="'dj-分钟' | translate" [nzValue]="'minute'"></ad-option>
                      <ad-option [nzLabel]="'dj-小时' | translate" [nzValue]="'hour'"></ad-option>
                      <ad-option [nzLabel]="'dj-天' | translate" [nzValue]="'day'"></ad-option>
                      <ad-option [nzLabel]="'dj-周' | translate" [nzValue]="'week'"></ad-option>
                    </ad-select>
                  </nz-input-group>
                </div>
              </nz-form-control>
            </nz-form-item>
            <span style="margin-left: 3px">{{ 'dj-结束' | translate }}</span>
          </div>
        </div>
        <div class="form-item-time" *ngIf="dataFormGroup.get('wakeupStrategy')?.value === 'manual'">
          <div style="padding: 8px; background-color: #f7f8fe; margin-bottom: 0px">
            {{ 'dj-手动唤起策略需指定该等待任务的唯一标识' | translate }}
          </div>
          <nz-form-item class="nz-form-item-content">
            <nz-form-control [nzAutoTips]="errorTips">
              <!--业务主键来源-->
              <div class="form-item">
                <div class="item-title">
                  {{ 'dj-业务主键来源' | translate }}
                </div>
                <app-variable-select-input
                  formControlName="sourceKey"
                  [modelRoot]="true"
                  [variableRange]="variableRange"
                  [nodeId]="nodeId"
                  (onChanged)="handleValueChanged($event)"
                  [item]="item"
                >
                </app-variable-select-input>
              </div>
            </nz-form-control>
          </nz-form-item>
          <nz-radio-group formControlName="type" (ngModelChange)="handleChangeType()">
            <label nz-radio nzValue="mapping">{{ 'dj-UI模式' | translate }}</label>
            <label nz-radio nzValue="script">{{ 'dj-表达式模式' | translate }}</label>
          </nz-radio-group>
          <nz-form-item *ngIf="'mapping' === dataFormGroup.get('type').value">
            <!--活动排程-->
            <nz-form-control [nzErrorTip]="'dj-请选择' | translate">
              <div class="form-item">
                <div class="item-title">
                  {{ 'dj-业务主键' | translate }}
                  <span class="item-required">*</span>
                </div>
                <ad-select
                  style="width: 100%"
                  formControlName="uniKeys"
                  [nzPlaceHolder]="'dj-请选择' | translate"
                  (ngModelChange)="handleScheduleTypeChange($event)"
                  nzMode="multiple"
                >
                  <ad-option
                    *ngFor="let option of optionList"
                    [nzValue]="option.data_name"
                    [nzLabel]="option.fieldName"
                    [nzDisabled]="option.disabled"
                  ></ad-option>
                </ad-select>
              </div>
            </nz-form-control>
          </nz-form-item>
          <ng-container *ngIf="'script' === dataFormGroup.get('type').value">
            <nz-form-item class="nz-form-item-content">
              <nz-form-control [nzAutoTips]="errorTips">
                <span>
                  {{ 'dj-表达式设置' | translate }}
                  <i
                    adIcon
                    iconfont="iconshuomingwenzi"
                    class="question-icon"
                    nzTooltipTrigger="hover"
                    nz-tooltip
                    [nzTooltipTitle]="'dj-返回数组包裹主键形式' | translate"
                  >
                  </i>
                </span>
                <nz-input-group [nzSuffix]="suffixIcon">
                  <input
                    readonly
                    nz-input
                    (dblclick)="handleOpenScript()"
                    formControlName="script"
                  />
                </nz-input-group>
                <ng-template #suffixIcon>
                  <i adIcon iconfont="icongaodaima" (click)="handleOpenScript()"></i>
                </ng-template>
              </nz-form-control>
            </nz-form-item>
          </ng-container>
        </div>
      </nz-collapse-panel>
    </nz-collapse>
  </form>
</div>

<!--script脚本编辑器弹窗-->
<app-script-editor
  *ngIf="scriptModalVisible"
  [scriptModal]="scriptModalVisible"
  [script]="script"
  (confirm)="handleCloseScript('confirm', $event)"
  (close)="handleCloseScript('close', $event)"
></app-script-editor>
