<!-- 编辑索引开窗 -->
<ad-modal
  [nzTitle]="modalTitle"
  [nzWidth]="'460px'"
  [nzVisible]="visible"
  [nzClosable]="true"
  (nzAfterClose)="handleAfterClose()"
  (nzOnOk)="handleOk()"
  (nzOnCancel)="handleCancel()"
>
  <ng-container *adModalContent>
    <form class="variable-modal-form" nz-form [formGroup]="form" style="width: 400px">
      <!-- 数据类型 -->
      <nz-form-item>
        <nz-form-control>
          <ad-select
            nzDropdownClassName="variable-modal-custom-select"
            style="width: 100%"
            [isInsideLabel]="true"
            [label]="'dj-数据类型' | translate"
            [nzPlaceHolder]="'dj-请选择' | translate"
            nzOptionHeightPx="40"
            formControlName="dataType"
            [nzAllowClear]="false"
          >
            <ng-container *ngFor="let data of dataTypes">
              <ad-option nzCustomContent [nzValue]="data.value" [nzDisabled]="data.disabled" [nzLabel]="data.label">
                <div class="custom-option-content">
                  <i class="iconfont" adIcon [iconfont]="dataTypeIconMap[data.value]"></i>
                  <div class="option-label">
                    {{ data.label }}
                    <span class="description">{{ data.description }}</span>
                  </div>
                </div>
              </ad-option>
            </ng-container>
          </ad-select>
        </nz-form-control>
      </nz-form-item>

      <!-- 变量名称 -->
      <nz-form-item *ngIf="!['Mechanism'].includes(form.get('dataType').value)">
        <nz-form-control [nzAutoTips]="autoTips">
          <nz-input-group
            [required]="true"
            [adInnerLabel]="('DTDVar' === form.get('dataType').value ? 'dj-内部使用变量' : 'dj-变量名称') | translate"
            nz-tooltip
            nzTooltipTrigger="click"
            [nzTooltipTitle]="'dj-仅支持输入英文、数字、下划线' | translate"
            nzTooltipPlacement="bottomLeft"
          >
            <input nz-input formControlName="varName" />
          </nz-input-group>
        </nz-form-control>
      </nz-form-item>

      <!-- 模型 -->
      <nz-form-item *ngIf="form.get('dataType').value === 'BusinessObject'">
        <nz-form-control [nzAutoTips]="autoTips">
          <ad-select
            style="width: 100%"
            required
            [isInsideLabel]="true"
            [label]="'dj-模型' | translate"
            [nzPlaceHolder]="'dj-请选择' | translate"
            formControlName="modeCode"
          >
            <ng-container *ngFor="let modal of modelList">
              <ad-option [nzLabel]="modal.name" [nzValue]="modal.code"></ad-option>
            </ng-container>
          </ad-select>
        </nz-form-control>
      </nz-form-item>

      <!-- 机制参数 -->
      <nz-form-item *ngIf="form.get('dataType').value === 'Mechanism'">
        <nz-form-control [nzAutoTips]="autoTips2">
          <ad-select
            style="width: 100%"
            required
            [isInsideLabel]="true"
            [label]="'dj-机制参数' | translate"
            [nzPlaceHolder]="'dj-请选择' | translate"
            formControlName="varName"
            [nzCustomTemplate]="mechanismOptions"
          >
            <ng-container *ngFor="let mechanism of mechanismList">
              <ad-option
                [nzLabel]="mechanism.paramName + '-' + mechanism.paramCode"
                [nzValue]="mechanism.paramCode"
              ></ad-option>
            </ng-container>
          </ad-select>
          <ng-template #mechanismOptions let-selected>
            {{ getMechanismLabel(selected.nzValue) }}
          </ng-template>
        </nz-form-control>
      </nz-form-item>

      <ng-container *ngIf="'Mechanism' !== form.get('dataType').value">
        <!-- 是否数组 -->
        <nz-form-item>
          <nz-form-control>
            <label nz-checkbox [nzDisabled]="'DTDVar' === form.get('dataType').value" formControlName="isArray">{{
              'dj-是否数组' | translate
            }}</label>
            <i
              adIcon
              iconfont="iconshuomingwenzi"
              class="question-icon"
              nzTooltipTrigger="hover"
              nz-tooltip
              [nzTooltipTitle]="'dj-调用变量时将处理为数组结构' | translate"
            >
            </i>
          </nz-form-control>
        </nz-form-item>

        <!-- 必填 -->
        <nz-form-item *ngIf="'DTDVar' !== form.get('dataType').value">
          <nz-form-control>
            <label nz-checkbox formControlName="required">{{ 'dj-必填' | translate }}</label>
          </nz-form-control>
        </nz-form-item>

        <!-- 默认值 -->
        <nz-form-item *ngIf="'DTDVar' !== form.get('dataType').value">
          <nz-form-control [nzValidateStatus]="form.get('defaultValue')">
            <div class="default-value-container">
              <nz-input-group [adInnerLabel]="'dj-默认值' | translate">
                <input nz-input formControlName="defaultValue" />
              </nz-input-group>
              <i
                adIcon
                iconfont="iconshuomingwenzi"
                class="question-icon"
                nzTooltipTrigger="hover"
                nz-tooltip
                [nzTooltipTitle]="'dj-使用该变量时取此处配置的默认值' | translate"
              >
              </i>
            </div>
          </nz-form-control>
        </nz-form-item>

        <!-- 流程输入变量code -->
        <nz-form-item *ngIf="'DTDVar' === form.get('dataType').value">
          <nz-form-control [nzAutoTips]="autoTips">
            <nz-spin [nzSpinning]="variableLoading" nzSize="small" class="variable-loading">
              <ad-select
                style="width: 100%"
                [isInsideLabel]="true"
                isInsideLabel
                [required]="true"
                [label]="'dj-外部输入DTD变量' | translate"
                [nzPlaceHolder]="'dj-请选择' | translate"
                [nzOptions]="variableList"
                formControlName="mappingCode"
                [nzAllowClear]="false"
                (ngModelChange)="handleDtdChanged($event)"
              >
              </ad-select>
            </nz-spin>
          </nz-form-control>
        </nz-form-item>

        <!-- 描述 -->
        <nz-form-item>
          <nz-form-control>
            <nz-input-group [adInnerLabel]="'dj-描述' | translate">
              <textarea
                nz-input
                [maxlength]="200"
                [nzAutosize]="{ minRows: 3, maxRows: 6 }"
                formControlName="description"
              ></textarea>
            </nz-input-group>
          </nz-form-control>
        </nz-form-item>
      </ng-container>

      <ng-container *ngIf="'Mechanism' === form.get('dataType').value && mechanismItem">
        <div class="mechanism-wrap">
          <nz-input-group class="mechanism-item" [adInnerLabel]="'dj-参数编码' | translate">
            <input nz-input disabled [value]="mechanismItem.paramCode" />
          </nz-input-group>
          <nz-input-group class="mechanism-item" [adInnerLabel]="'dj-参数名称' | translate">
            <input nz-input disabled [value]="mechanismItem.paramName" />
          </nz-input-group>
          <nz-input-group class="mechanism-item" [adInnerLabel]="'dj-描述' | translate">
            <input nz-input disabled [value]="mechanismItem.description" />
          </nz-input-group>
        </div>
      </ng-container>
    </form>
  </ng-container>
</ad-modal>
