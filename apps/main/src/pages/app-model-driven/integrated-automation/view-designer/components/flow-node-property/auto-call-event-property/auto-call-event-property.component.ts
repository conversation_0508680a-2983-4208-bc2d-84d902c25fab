import { Component, EventEmitter, Input, OnInit, Output, SimpleChanges } from '@angular/core';
import { FormBuilder, Validators } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { NameValidators } from '../../../config/utils';
import { debounce, isEmpty } from 'lodash';
import { LocaleService } from 'common/service/locale.service';
import { ViewStoreService } from '../../../service/store.service';

@Component({
  selector: 'app-auto-call-event-property',
  templateUrl: './auto-call-event-property.component.html',
  styleUrls: ['./auto-call-event-property.component.less'],
})
export class AutoCallEventPropertyComponent implements OnInit {
  // 输入的渲染数据
  @Input() data;
  @Input() nodeId;
  // 通知父组件关闭属性面板
  @Output() close: EventEmitter<any> = new EventEmitter();
  // 表单数据改变通过校验后向父组件通知更新数据
  @Output() changeData: EventEmitter<any> = new EventEmitter();
  // 事件开窗是否显示
  eventListVisible: boolean;
  dataFormGroup;
  formGroupValidityFlag = false;
  // 多语言单独处理，name
  nameLang: any;
  currScriptControlName: string;
  scriptModal: boolean;
  scriptData: any;
  constructor(
    public translate: TranslateService,
    private fb: FormBuilder,
    private languageService: LocaleService,
    public viewStoreService: ViewStoreService,
  ) {}

  ngOnInit(): void {
    // 调试写死数据

    this.handleInit();
    this.getCurrentData();
    this.dataFormGroup.valueChanges.subscribe(() => {
      if (!this.formGroupValidityFlag) {
        this.getCurrentData();
      }
    });
  }
  handleClosePanel(): void {
    this.close.emit();
  }

  showEventList() {
    this.eventListVisible = true;
  }

  // 根据输入的组装fromgroup
  handleInit(): void {
    // 初始化fromgroup
    // 直接校验
    // 对父组件发出change事件主要里面混入isVerificationPassed
    this.dataFormGroup = this.fb.group({
      id: [this.data?.id || null, [Validators.required]],
      name: [this.data?.name || '', [NameValidators.trim, Validators.required, Validators.pattern('^.{0,255}$')]],
      _eventId: [this.data?._eventId || null, [Validators.required]],
      _eventName: [this.data?._eventName || null],
      _eventBody: [this.data?._eventBody || null, [Validators.required]],
    });
    this.nameLang = this.data.lang;
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (Object.keys(changes).includes('nodeId')) {
      if (!changes.nodeId.firstChange) {
        this.handleChangeValue();
      }
    }
  }

  handleChangeValue() {
    // 为了patchValue不触发valueChanges
    this.formGroupValidityFlag = true;
    this.dataFormGroup.patchValue({
      id: this.data?.id,
      name: this.data?.name,
      _eventId: this.data?._eventId,
      _eventBody: this.data?._eventBody,
    });
    this.formGroupValidityFlag = false;
    this.nameLang = this.data.lang;
    this.getCurrentData();
  }

  handlePatchApp(key: any, data: any): void {
    if (data.needLang) {
      this.nameLang = {
        ...(this.nameLang || {}),
        [key]: data.lang,
      };
    }
    this.dataFormGroup.patchValue({
      [key]: data?.value,
    });
  }

  showScriptModal(controlName) {
    this.currScriptControlName = controlName;
    this.scriptData = this.dataFormGroup.get(controlName)?.value;
    this.scriptModal = true;
  }
  handleCloseSript(flag, value) {
    if (flag === 'confirm') {
      this.dataFormGroup.patchValue({
        [this.currScriptControlName]: value,
      });
    }
    this.scriptModal = false;
  }

  // 获取当前最新的数据
  getCurrentData = debounce(
    () => {
      this.formGroupValidityFlag = true;
      for (const i of Object.keys(this.dataFormGroup?.controls)) {
        this.dataFormGroup.controls[i].markAsDirty();
        this.dataFormGroup.controls[i].updateValueAndValidity();
      }
      this.formGroupValidityFlag = false;
      const currentData = this.dataFormGroup.getRawValue();
      // 混入isVerificationPassed 是否校验通过
      const isVerificationPassed = this.viewStoreService.transformVerificationPassed(this.dataFormGroup.valid);
      const returnData = {
        data: Object.assign(this.data, currentData, { lang: this.nameLang }),
        isVerificationPassed,
      };
      this.changeData.emit(returnData);
      return returnData;
    },
    150,
    { leading: false, trailing: true },
  );

  handleChooseEvent(data) {
    const businessName =
      data?.lang?.businessName?.[this.languageService?.currentLanguage || 'zh_CN'] || data.businessName;
    const middleLine = businessName && data?.eventName ? '-' : '';
    if (!isEmpty(data)) {
      this.dataFormGroup.patchValue({
        _eventId: data?.eventId,
        _eventName: `${businessName || ''}${middleLine}${data?.eventName || ''}`,
        // _eventBody: data?._eventBody,
      });
    }

    this.eventListVisible = false;
  }
}
