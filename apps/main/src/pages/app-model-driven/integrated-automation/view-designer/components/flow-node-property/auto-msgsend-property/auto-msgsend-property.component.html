<div class="auto-notify-property-root submit-form">
  <div class="header">
    <span>{{ 'dj-消息通知' | translate }}</span>
    <i adIcon iconfont="icondanchuxiaoxiguanbi" class="iconfont" aria-hidden="true" (click)="handleClosePanel()"></i>
  </div>
  <form nz-form class="content" [formGroup]="dataFormGroup">
    <nz-collapse [nzBordered]="false">
      <!--基本信息的面板-->
      <nz-collapse-panel [nzHeader]="'dj-基本信息' | translate" [nzActive]="true" [nzExpandedIcon]="'caret-right'">
        <nz-form-item class="nz-form-item-content">
          <nz-form-control [nzAutoTips]="errorTips">
            <!--节点id-->
            <div class="form-item">
              <div class="item-title">
                {{ 'dj-节点id' | translate }}
                <span class="item-required">*</span>
              </div>
              <app-component-input
                [attr]="{
                  name: '节点id',
                  required: true,
                  needLang: false,
                  readOnly: true
                }"
                style="width: 100%"
                formControlName="id"
                [value]="dataFormGroup.get('id')?.value"
                ngDefaultControl
              >
              </app-component-input>
            </div>
          </nz-form-control>
        </nz-form-item>

        <nz-form-item class="nz-form-item-content">
          <nz-form-control [nzAutoTips]="errorTips">
            <!--节点名称-->
            <div class="form-item">
              <div class="item-title">
                {{ 'dj-节点名称' | translate }}
                <span class="item-required">*</span>
              </div>
              <app-modal-input
                class="language-input"
                [attr]="{
                  name: 'name',
                  required: true,
                  lang: this.nameLang?.name,
                  needLang: true
                }"
                [innerLabel]="false"
                [value]="nameLang?.name?.[('dj-LANG' | translate)] || dataFormGroup.get('name')?.value"
                (callBack)="handlePatchApp('name', $event)"
                [formControlName]="'name'"
                ngDefaultControl
              ></app-modal-input>
            </div>
          </nz-form-control>
        </nz-form-item>
      </nz-collapse-panel>
      <nz-collapse-panel [nzHeader]="'dj-通知设置' | translate" [nzActive]="true" [nzExpandedIcon]="'caret-right'">
        <div class="form-item">
          <div class="item-title">
            {{ 'dj-选择消息推送' | translate }}
            <span class="item-required">*</span>
          </div>
          <nz-form-control [nzAutoTips]="errorTips">
            <nz-spin [nzSpinning]="sceneLoading" nzSize="small" class="api-loading">
              <div class="scene-line">
                <ad-select
                  formControlName="sceneSid"
                  [nzPlaceHolder]="'dj-请选择' | translate"
                  style="display: flex; flex: 1"
                  [nzAllowClear]="true"
                  (ngModelChange)="handleSceneChange($event)"
                >
                  <ad-option
                    [nzLabel]="item.lang?.name?.[('dj-LANG' | translate)] || item.name"
                    [nzValue]="item.sid"
                    *ngFor="let item of sceneList"
                  ></ad-option>
                </ad-select>
                <span class="preview" *ngIf="dataFormGroup?.get('sceneSid').value" (click)="handlePreview()">{{
                  'dj-预览' | translate
                }}</span>
              </div>
            </nz-spin>
          </nz-form-control>
        </div>
        <nz-spin [nzSpinning]="eventLoading" nzSize="small" class="api-loading">
          <app-msg-field-set
            *ngIf="dataFormGroup.get('sceneSid').value"
            [nodeId]="nodeId"
            [fieldInfosData]="fieldInfosData"
            [required]="false"
            [explainBody]="explainBody"
            (fieldInfosDataBack)="fieldInfosDataBack($event)"
          ></app-msg-field-set>
        </nz-spin>
      </nz-collapse-panel>
    </nz-collapse>
  </form>
</div>

<nz-drawer
  [nzWrapClassName]="'prd-edit-drawer'"
  [nzBodyStyle]="{ overflow: 'auto' }"
  *ngIf="drawerVisible"
  nzPlacement="right"
  [nzVisible]="drawerVisible"
  [nzWidth]="'90%'"
  [nzTitle]="titleTemplate"
  (nzOnClose)="handleCloseDrawer()"
>
  <ng-template #titleTemplate>
    <div class="header-title">{{ 'dj-预览' | translate }}</div>
  </ng-template>
  <ng-container *nzDrawerContent>
    <app-message-notify-detail [headerVisible]="fasle" [param]="params"></app-message-notify-detail>
  </ng-container>
</nz-drawer>
