import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NameValidators } from '../../../config/utils';
import { debounce, omit } from 'lodash';
import { ViewStoreService } from '../../../service/store.service';
import { SubProjectPropertyService } from './sub-project-property.service';

@Component({
  selector: 'app-auto-sub-project-property',
  templateUrl: './auto-sub-project-property.component.html',
  styleUrls: ['./auto-sub-project-property.component.less'],
  providers: [SubProjectPropertyService],
})
export class AutoSubProjectPropertyComponent implements OnInit, OnChanges {
  // 输入的渲染数据
  @Input() data;
  @Input() nodeId;
  // 通知父组件关闭属性面板
  @Output() close: EventEmitter<any> = new EventEmitter();
  // 表单数据改变通过校验后向父组件通知更新数据
  @Output() changeData: EventEmitter<any> = new EventEmitter();

  requestScriptModal: boolean;
  scriptType: string;
  requestScriptContent: string = undefined;

  projects: any[] = [];
  dataFormGroup: FormGroup;
  errorTips: Record<string, Record<string, string>> = {
    default: {
      required: this.translate.instant('dj-必填'),
      pattern: this.translate.instant('dj-超长'),
    },
  };
  formGroupValidityFlag = false;
  // 多语言单独处理，name
  nameLang: any;
  loading: boolean = false;
  constructor(
    public translate: TranslateService,
    private fb: FormBuilder,
    public viewStoreService: ViewStoreService,
    private service: SubProjectPropertyService,
  ) {
    this.initForm();
  }

  async ngOnInit() {
    await this.handleInit();
    this.getCurrentData();
    this.dataFormGroup.valueChanges.subscribe(() => {
      if (!this.formGroupValidityFlag) {
        this.getCurrentData();
      }
    });
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (Object.keys(changes).includes('nodeId')) {
      if (!changes.nodeId.firstChange) {
        this.handleChangeValue();
      }
    }
  }

  async handleInit(): Promise<void> {
    this.dataFormGroup.patchValue({
      id: this.data?.id,
      name: this.data?.name,
      projectCode: this.data?.config?.invokeConfig?.projectCode,
      inputVariables: this.data?.config?.invokeConfig?.inputVariables,
      outputVariables: this.data?.config?.invokeConfig?.outputVariables,
    });
    this.nameLang = this.data.lang;
    await this.handleLoadProject();
  }

  initForm(): void {
    this.dataFormGroup = this.fb.group({
      id: [null, [Validators.required]],
      name: [null, [NameValidators.trim, Validators.required, Validators.pattern('^.{0,255}$')]],
      projectCode: [null, [Validators.required]],
      inputVariables: [[]],
      outputVariables: [[]],
    });
  }

  handleClosePanel(): void {
    this.close.emit();
  }

  handlePatchApp(key: any, data: any): void {
    if (data.needLang) {
      this.nameLang = {
        ...(this.nameLang || {}),
        [key]: data.lang,
      };
    }
    this.dataFormGroup.patchValue({
      [key]: data?.value,
    });
  }

  handleChangeValue(): void {
    this.formGroupValidityFlag = true;
    this.dataFormGroup.patchValue({
      id: this.data?.id,
      name: this.data?.name,
      projectCode: this.data?.config?.invokeConfig?.projectCode,
      inputVariables: this.data?.config?.invokeConfig?.inputVariables,
      outputVariables: this.data?.config?.invokeConfig?.outputVariables,
    });
    this.formGroupValidityFlag = false;
    this.nameLang = this.data.lang;
    this.getCurrentData();
  }

  getCurrentData = debounce(
    () => {
      this.formGroupValidityFlag = true;
      for (const i of Object.keys(this.dataFormGroup?.controls)) {
        this.dataFormGroup.controls[i].markAsDirty();
        this.dataFormGroup.controls[i].updateValueAndValidity();
      }
      this.formGroupValidityFlag = false;
      const currentData = this.dataFormGroup.getRawValue();
      const isVerificationPassed = this.viewStoreService.transformVerificationPassed(this.dataFormGroup.valid);
      const returnData = {
        data: Object.assign(this.data, {
          name: currentData.name,
          lang: this.nameLang,
          config: {
            invokeConfig: {
              projectCode: currentData.projectCode,
              projectFrom: this.getProjectFromByProjectId(currentData.projectCode),
              inputVariables: currentData.inputVariables,
              outputVariables: currentData.outputVariables,
            },
          },
        }),
        isVerificationPassed,
      };
      console.log('returnData', returnData);
      this.changeData.emit(returnData);
      return returnData;
    },
    50,
    { leading: false, trailing: true },
  );

  private getProjectFromByProjectId(code: string): string | undefined {
    return this.projects.find((item) => item.code === code)?.projectFrom;
  }

  private async handleLoadProject(): Promise<void> {
    try {
      this.loading = true;
      const {
        originalFlowData: { application, processId },
      } = this.viewStoreService.state;
      const result = (await this.service.getProjects({ application, processId }).toPromise()) as any;
      if (result.code === 0) {
        this.projects = result.data;
      }
    } finally {
      this.loading = false;
    }
  }

  handleOpenScriptEditor(type: 'inputVariables' | 'outputVariables'): void {
    this.scriptType = type;
    this.requestScriptContent =
      this.dataFormGroup.get(type).value ||
      `/* 发起子项目的输入输出脚本，modelCode为发起的实体，必须要确认正确
  return {
    "modelCode":{
      "name":$variables['ServiceTask_ec333f586c982d2b019cc8a42a061506']['name'],
      "age":$variables['age'],
      "status":'2',
      "school":$variables['school']
    }  
  }
 */`;
    this.requestScriptModal = true;
  }

  handleSaveRequestScript(script: string): void {
    this.dataFormGroup.patchValue({
      [this.scriptType]: script,
    });
    this.requestScriptContent = null;
    this.requestScriptModal = false;
  }
}
