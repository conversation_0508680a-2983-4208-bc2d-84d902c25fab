<form nz-form class="app-node-content" [formGroup]="dataFormGroup">
  <nz-collapse [nzBordered]="false">
    <!--签核设置的面板-->
    <nz-collapse-panel [nzHeader]="'dj-签核设置' | translate" [nzActive]="true" [nzExpandedIcon]="'caret-right'">
      <ad-tabs class="node-setting-tab" navStyle="button" [nzSelectedIndex]="0">
        <ad-tab [nzTitle]="'dj-人员设置' | translate">
          <ng-container *ngTemplateOutlet="personalSetting"></ng-container>
          <nz-form-item class="nz-form-item-content" formGroupName="strategyConfig">
            <nz-form-control [nzAutoTips]="errorTips">
              <!--审批策略-->
              <div class="form-item">
                <div class="item-title">
                  {{ 'dj-审批策略' | translate }}
                  <span class="item-required">*</span>
                  <i
                    adIcon
                    iconfont="iconshuomingwenzi"
                    aria-hidden="true"
                    class="question-icon"
                    nzTooltipTrigger="hover"
                    nz-tooltip
                    [nzTooltipTitle]="
                      'dj-当审批人为多人时，将结合审批策略进行核决。审批策略针对当前节点生效' | translate
                    "
                  >
                  </i>
                </div>
                <ad-select
                  ngDefaultControl
                  formControlName="multiplayerStrategy"
                  [nzPlaceHolder]="'dj-请选择' | translate"
                  style="width: 100%"
                >
                  <ad-option [nzLabel]="'dj-会签（需所有审批人同意）' | translate" nzValue="andSign"></ad-option>
                  <ad-option [nzLabel]="'dj-或签（一名审批人同意即可）' | translate" nzValue="orSign"></ad-option>
                </ad-select>
              </div>
            </nz-form-control>
          </nz-form-item>
          <ng-container *ngTemplateOutlet="strategySetting"></ng-container>
        </ad-tab>
        <ad-tab [nzTitle]="'dj-签核按钮' | translate">
          <div class="form-item">
            <div class="item-title">
              {{ 'dj-签核按钮设置' | translate }}
              <span class="item-required">*</span>
            </div>
            <div class="approve-btn-table">
              <div nz-row>
                <div nz-col nzSpan="12">{{ 'dj-操作按钮' | translate }}</div>
                <div nz-col nzSpan="12">{{ 'dj-启用' | translate }}</div>
              </div>
              <div nz-row>
                <div nz-col nzSpan="12">{{ 'dj-同意' | translate }}</div>
                <div nz-col nzSpan="12">
                  <nz-switch
                    [(ngModel)]="approveButton"
                    [ngModelOptions]="{ standalone: true }"
                    (ngModelChange)="handelSwitchChange()"
                  ></nz-switch>
                </div>
              </div>
              <div nz-row>
                <div nz-col nzSpan="12">{{ 'dj-拒绝' | translate }}</div>
                <div nz-col nzSpan="12">
                  <nz-switch
                    [(ngModel)]="refuseButton"
                    [ngModelOptions]="{ standalone: true }"
                    (ngModelChange)="handelSwitchChange()"
                  ></nz-switch>
                </div>
              </div>
              <div nz-row>
                <div nz-col nzSpan="12">{{ 'dj-转派' | translate }}</div>
                <div nz-col nzSpan="12">
                  <nz-switch
                    [(ngModel)]="reassignmentButton"
                    [ngModelOptions]="{ standalone: true }"
                    (ngModelChange)="handelSwitchChange()"
                  ></nz-switch>
                </div>
              </div>
              <div nz-row>
                <div nz-col nzSpan="12">{{ 'dj-加签' | translate }}</div>
                <div nz-col nzSpan="12">
                  <nz-switch
                    [nzDisabled]="dataFormGroup.get('strategyConfig').get('multiplayerStrategy')?.value === 'andSign'"
                    [(ngModel)]="addApproveButton"
                    [ngModelOptions]="{ standalone: true }"
                    (ngModelChange)="handelSwitchChange()"
                  ></nz-switch>
                </div>
              </div>
              <div nz-row>
                <div nz-col nzSpan="12">{{ 'dj-退回' | translate }}</div>
                <div nz-col nzSpan="12">
                  <nz-switch
                    [(ngModel)]="backButton"
                    [ngModelOptions]="{ standalone: true }"
                    (ngModelChange)="handelSwitchChange()"
                  ></nz-switch>
                </div>
              </div>
            </div>

            <div class="form-item-tip">
              <i style="margin: 4px 4px 0 0" adIcon type="bulb" theme="outline"></i>
              <span>{{ 'dj-审批策略开启会签时，不支持加签' | translate }}</span>
            </div>
            <!--退回按钮未打开，隐藏不给选-->
            <div class="approve-btn-backsetting" [ngClass]="{ 'approve-btn-backsetting-hide': !backButton }">
              <div class="form-item">
                <span class="item-title">
                  {{ 'dj-退回设置' | translate }}
                  <span class="item-required">*</span>
                </span>
                <div style="padding-left: 16px">
                  <span style="display: inline-block; margin-bottom: 8px">{{
                    'dj-退回至发起人重新提交后' | translate
                  }}</span>
                  <nz-radio-group
                    [(ngModel)]="returnExecutionStrategy"
                    [ngModelOptions]="{ standalone: true }"
                    (ngModelChange)="handelSwitchChange()"
                  >
                    <label style="margin-bottom: 8px" nz-radio nzValue="Sequential">
                      {{ 'dj-重新依次审批' | translate }}
                      <i
                        adIcon
                        iconfont="iconshuomingwenzi"
                        aria-hidden="true"
                        class="question-icon"
                        nzTooltipTrigger="hover"
                        nz-tooltip
                        [nzTooltipTitle]="'dj-退回后，需回滚A-C间两个节点数据，并重新执行相关节点' | translate"
                      >
                      </i>
                    </label>
                    <!-- <label nz-radio nzValue="NonSequential" [nzDisabled]="true">
                      {{ 'dj-从当前节点审批' | translate }}
                      <i
                        adIcon
                        iconfont="iconshuomingwenzi"
                        aria-hidden="true"
                        class="question-icon"
                        nzTooltipTrigger="hover"
                        nz-tooltip
                        [nzTooltipTitle]="'dj-退回后，需回滚' | translate"
                      >
                      </i>
                    </label> -->
                  </nz-radio-group>
                </div>
                <app-return-strategy
                  [nodeId]="data.id"
                  [returnStrategyData]="returnStrategy"
                  [preManualNodes]="preManualNodes"
                  (returnStrategyDataChange)="handleReturnStrategyDataChange($event)"
                ></app-return-strategy>
              </div>
            </div>
          </div>
        </ad-tab>
        <ad-tab [nzTitle]="'dj-字段权限' | translate">
          <app-field-setting-property
            [data]="fieldConfig"
            [bindForm]="bindForm"
            [nodeId]="data.id"
            [processId]="data.processId"
            [extendHeader]="service.combineHeaders"
            (changeData)="handleFieldSettingChange($event)"
          ></app-field-setting-property>
        </ad-tab>
      </ad-tabs>
      <div *ngIf="false">
        <ng-container *ngTemplateOutlet="personalSetting"></ng-container>
        <nz-form-item class="nz-form-item-content" formGroupName="strategyConfig">
          <nz-form-control [nzAutoTips]="errorTips">
            <!--审批策略-->
            <div class="form-item">
              <div class="item-title">
                {{ 'dj-审批策略' | translate }}
                <span class="item-required">*</span>
                <i
                  adIcon
                  iconfont="iconshuomingwenzi"
                  aria-hidden="true"
                  class="question-icon"
                  nzTooltipTrigger="hover"
                  nz-tooltip
                  [nzTooltipTitle]="'dj-当审批人为多人时，将结合审批策略进行核决。审批策略针对当前节点生效' | translate"
                >
                </i>
              </div>
              <ad-select
                ngDefaultControl
                formControlName="multiplayerStrategy"
                [nzPlaceHolder]="'dj-请选择' | translate"
                style="width: 100%"
              >
                <ad-option [nzLabel]="'dj-会签（需所有审批人同意）' | translate" nzValue="andSign"></ad-option>
                <ad-option [nzLabel]="'dj-或签（一名审批人同意即可）' | translate" nzValue="orSign"></ad-option>
              </ad-select>
            </div>
          </nz-form-control>
        </nz-form-item>
        <ng-container *ngTemplateOutlet="strategySetting"></ng-container>
      </div>
    </nz-collapse-panel>
  </nz-collapse>
</form>

<ng-template #personalSetting>
  <app-people-setting-property
    personType="approve"
    [bindForm]="bindForm"
    [nodeId]="nodeId"
    [executor]="executor"
    [decisionConfig]="decisionConfig"
    [preManualNodes]="preManualNodes"
    (changeData)="handlePeopleSettingChange($event)"
  ></app-people-setting-property>
</ng-template>

<ng-template #strategySetting>
  <div class="form-item">
    <div class="item-title">
      {{ 'dj-自动跳过策略' | translate }}
      <i
        adIcon
        iconfont="iconshuomingwenzi"
        aria-hidden="true"
        class="question-icon"
        nzTooltipTrigger="hover"
        nz-tooltip
        [nzTooltipTitle]="'dj-勾选相应策略后，如遇相应节点，将会自动审批通过' | translate"
      >
      </i>
    </div>

    <nz-checkbox-wrapper style="width: 100%">
      <div nz-row>
        <div nz-col nzSpan="24" style="margin-bottom: 8px">
          <label
            nz-checkbox
            (ngModelChange)="handleExecuteByPassChange()"
            [(ngModel)]="executeByPass[0].enable"
            [ngModelOptions]="{ standalone: true }"
          >
            {{ 'dj-签核人与发起人相同' | translate }}
          </label>
        </div>
        <div nz-col nzSpan="24" style="margin-bottom: 8px">
          <label
            nz-checkbox
            (ngModelChange)="handleExecuteByPassChange()"
            [(ngModel)]="executeByPass[1].enable"
            [ngModelOptions]="{ standalone: true }"
          >
            {{ 'dj-签核人与上一签核人相同' | translate }}
          </label>
        </div>
        <div nz-col nzSpan="24">
          <label
            nz-checkbox
            (ngModelChange)="handleExecuteByPassChange()"
            [(ngModel)]="executeByPass[2].enable"
            [ngModelOptions]="{ standalone: true }"
          >
            {{ 'dj-已签核过' | translate }}
          </label>
        </div>
      </div>
    </nz-checkbox-wrapper>
  </div>
</ng-template>
