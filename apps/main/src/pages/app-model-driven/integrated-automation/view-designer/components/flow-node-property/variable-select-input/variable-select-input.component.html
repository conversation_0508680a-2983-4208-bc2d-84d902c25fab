<nz-input-group [nzPrefix]="prefixTpl" [nzSuffix]="modalTpl" class="input-group-wrap">
  <input
    [(ngModel)]="variableSuffix"
    (ngModelChange)="handleSelChange()"
    [ngModelOptions]="{ standalone: true }"
    nz-input
    type="text"
    [placeholder]="!variableName ? ('dj-请选择' | translate) : ''"
    nz-tooltip
    nzTooltipTrigger="hover"
    nzTooltipPlacement="topLeft"
    [nzTooltipTitle]="getVariableTooltip()"
    [readonly]="!inputable || !variableName"
  />
</nz-input-group>
<ng-template #prefixTpl>
  <ng-container *ngIf="variableName">
    <span
      nz-tooltip
      nzTooltipTrigger="hover"
      nzTooltipPlacement="topLeft"
      [nzTooltipTitle]="getVariableTooltip()"
      [style]="{
        maxWidth: getPrefixWidth(),
        'white-space': 'nowrap',
        overflow: 'hidden',
        'text-overflow': 'ellipsis'
      }"
    >
      {{ variableName }}
    </span>
  </ng-container>
</ng-template>
<ng-template #modalTpl>
  <i class="icon" adIcon [iconfont]="'iconkaichuang1'" (click)="showVariableModal = true"></i>
</ng-template>

<variable-modal
  *ngIf="showVariableModal"
  [showModal]="showVariableModal"
  [nodeId]="nodeId"
  [modelRoot]="modelRoot"
  [includeGlobalModel]="includeGlobalModel"
  [variableRange]="variableRange"
  [initVariable]="variable"
  (close)="showVariableModal = false"
  (changeVariable)="handleChangeVariable($event)"
></variable-modal>
