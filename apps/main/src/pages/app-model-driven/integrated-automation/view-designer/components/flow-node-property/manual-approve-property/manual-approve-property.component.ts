import { Component, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';

import { ManualApprovePropertyService } from './manual-approve-property.service';

import { ApproveCardSettingComponent } from './components/card-setting/card-setting.component';
import { ApproveNodeSettingComponent } from './components/node-setting/node-setting.component';
import { TranslateService } from '@ngx-translate/core';
import { ViewStoreService } from '../../../service/store.service';

@Component({
  selector: 'app-manual-approve-property',
  templateUrl: './manual-approve-property.component.html',
  styleUrls: ['./manual-approve-property.component.less'],
  providers: [ManualApprovePropertyService],
})
export class ManualApprovePropertyComponent implements OnInit {
  // 输入的渲染数据
  @Input() data;
  // 唯一标识节点使用，OnChanges中监听便于重新加载数据
  @Input() nodeId;
  // 通知父组件关闭属性面板
  @Output() close: EventEmitter<any> = new EventEmitter();
  // 表单数据改变通过校验后向父组件通知更新数据
  @Output() changeData: EventEmitter<any> = new EventEmitter();

  @ViewChild('approveCardSetting') approveCardSetting: ApproveCardSettingComponent;
  @ViewChild('approveNodeSetting') approveNodeSetting: ApproveNodeSettingComponent;

  @Input() uiKey: string;
  @Output() clearUiKey: EventEmitter<any> = new EventEmitter();

  tabIndex: number = 0;

  constructor(public translate: TranslateService, private viewStoreService: ViewStoreService) {}

  ngOnInit(): void {}

  handleChangeTab(data) {
    this.tabIndex = data?.index;
  }

  handleClosePanel(): void {
    this.close.emit();
  }

  handleClearUiKey(): void {
    this.clearUiKey.emit();
  }

  handleChangeData(e) {
    const { data, valid, componentType } = e;
    let mergeDataValidate: any = {};
    if (componentType === 'approveCardSetting') {
      mergeDataValidate = this.approveNodeSetting?.getFormValidate();
    } else {
      mergeDataValidate = this.approveCardSetting?.getFormValidate();
    }
    const isVerificationPassed = this.viewStoreService.transformVerificationPassed(valid && mergeDataValidate);
    const returnData = {
      data,
      isVerificationPassed,
    };
    this.changeData.emit(returnData);
  }
}
