<div class="add-data-property-root submit-form">
  <div class="header">
    <span>{{ 'dj-新增数据' | translate }}</span>
    <i adIcon iconfont="icondanchuxiaoxiguanbi" class="iconfont" aria-hidden="true" (click)="handleClosePanel()"></i>
  </div>
  <form nz-form class="content" [formGroup]="dataFormGroup">
    <nz-collapse [nzBordered]="false">
      <!--基本信息的面板-->
      <nz-collapse-panel [nzHeader]="'dj-基本信息' | translate" [nzActive]="true" [nzExpandedIcon]="'caret-right'">
        <nz-form-item class="nz-form-item-content">
          <nz-form-control [nzAutoTips]="errorTips">
            <!--节点id-->
            <div class="form-item">
              <div class="item-title">
                {{ 'dj-节点id' | translate }}
                <span class="item-required">*</span>
              </div>
              <app-component-input
                [attr]="{
                  name: '节点id',
                  required: true,
                  needLang: false,
                  readOnly: true
                }"
                style="width: 100%"
                formControlName="id"
                [value]="dataFormGroup.get('id')?.value"
                ngDefaultControl
              >
              </app-component-input>
            </div>
          </nz-form-control>
        </nz-form-item>

        <nz-form-item class="nz-form-item-content">
          <nz-form-control [nzAutoTips]="errorTips">
            <!--节点名称-->
            <div class="form-item">
              <div class="item-title">
                {{ 'dj-节点名称' | translate }}
                <span class="item-required">*</span>
              </div>
              <app-modal-input
                class="language-input"
                [attr]="{
                  name: 'name',
                  required: true,
                  lang: nameLang?.name,
                  needLang: true
                }"
                [innerLabel]="false"
                [value]="nameLang?.name?.[('dj-LANG' | translate)] || dataFormGroup.get('name')?.value"
                (callBack)="handlePatchApp('name', $event)"
                [formControlName]="'name'"
                ngDefaultControl
              ></app-modal-input>
            </div>
          </nz-form-control>
        </nz-form-item>

        <nz-form-item class="nz-form-item-content" formGroupName="bindForm">
          <nz-form-control [nzAutoTips]="errorTips">
            <!--节点名称-->
            <div class="form-item">
              <div class="item-title">
                {{ 'dj-业务对象' | translate }}
                <span class="item-required">*</span>
                <i
                  adIcon
                  iconfont="iconshuomingwenzi"
                  aria-hidden="true"
                  class="question-icon"
                  nzTooltipTrigger="hover"
                  nz-tooltip
                  [nzTooltipTitle]="'dj-选择更新数据的目标业务对象' | translate"
                >
                </i>
              </div>
              <ad-select
                formControlName="formCode"
                [nzPlaceHolder]="'dj-请选择' | translate"
                style="width: 100%"
                [nzAllowClear]="true"
                [nzOptions]="businessConstructorList"
              >
              </ad-select>
            </div>
          </nz-form-control>
        </nz-form-item>
      </nz-collapse-panel>

      <nz-collapse-panel [nzHeader]="'dj-数据设置' | translate" [nzActive]="true" [nzExpandedIcon]="'caret-right'">
        <nz-form-item class="nz-form-item-content">
          <nz-form-control [nzAutoTips]="errorTips">
            <!--节点id-->
            <div class="form-item">
              <div class="item-title">
                {{ 'dj-新增数据' | translate }}
                <span class="item-required">*</span>
              </div>
              <nz-radio-group style="width: 101%" nzDisabled formControlName="addType">
                <label nz-radio nzValue="addSingle">{{ 'dj-新增单条数据' | translate }}</label>
                <label nz-radio nzValue="addMult"
                  >{{ 'dj-新增多条数据' | translate }}
                  <span class="help-text">{{ 'dj-敬请期待' | translate }}</span></label
                >
              </nz-radio-group>
            </div>
          </nz-form-control>
        </nz-form-item>

        <app-field-set
          [nodeId]="nodeId"
          [fieldInfosData]="fieldInfosData"
          [required]="true"
          [preModelNodes]="preModelNodes"
          [bindForm]="dataFormGroup.get('bindForm')?.value"
          (checkBindForm)="checkBindForm()"
          (fieldInfosDataBack)="fieldInfosDataBack($event)"
        ></app-field-set>
      </nz-collapse-panel>
    </nz-collapse>
  </form>
</div>
