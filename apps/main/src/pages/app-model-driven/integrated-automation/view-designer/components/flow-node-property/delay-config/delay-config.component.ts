import {
  AfterViewInit,
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  Output,
  SimpleChanges,
  ViewChild,
  ElementRef,
} from '@angular/core';
import { FormArray, FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { AdModalService } from 'components/ad-ui-components/ad-modal/ad-modal.service';
import { fromEvent, Subject, Subscription } from 'rxjs';
import { ViewApiService } from '../../../service/api.service';
import { isJSON, validatorForm } from 'common/utils/core.utils';
import { cloneDeep, debounce, throttle } from 'lodash';
import { debounceTime, takeUntil } from 'rxjs/operators';
import { ViewStoreService } from '../../../service/store.service';
import { PannelTabsType } from '../../../config/typings';
import { ViewGraphService } from '../../../service/graph.service';
import { ViewToolsService } from '../../../service/tools.service';
import { scripts } from 'pages/app/dtd/activity-flow/service/flow.util';
import { responseScript } from 'pages/app/dtd/drive-execution-new/components/api-design/service/script-template';

enum EActionType {
  ESP = 'ESP',
  HTTP = 'HTTP',
  Script = 'SCRIPT',
  EMAIL = 'EMAIL',
  AUTOCOMPLETE = 'AUTOCOMPLETE',
}

@Component({
  selector: 'app-delay-config',
  templateUrl: './delay-config.component.html',
  styleUrls: ['./delay-config.component.less'],
})
export class DelayConfigComponent implements OnInit, OnDestroy, AfterViewInit {
  @Input() nodeId: string;

  @Output() close = new EventEmitter();

  EActionType = EActionType;

  form: FormGroup;

  @ViewChild('scrollContent') scrollContent: ElementRef<HTMLDivElement>;

  private destory$ = new Subject();
  private formGroupValidityFlag: boolean = false;

  dropVisible: boolean = false;

  actionTypeSubject: Subscription;
  private scrollSubject: Subscription;
  delayConfig: any = {};

  private get state() {
    return this.viewStoreService.state;
  }

  expand: boolean = true;

  delayStrategyOptions: { label: string; value: string }[] = [
    {
      label: this.translate.instant('dj-活动创建前'),
      value: 'beforeActivity',
    },
    {
      label: this.translate.instant('dj-活动创建后'),
      value: 'afterActivity',
    },
  ];

  //#region 脚本编辑器
  // 脚本编辑器
  scriptModal: boolean = false;
  scriptData: string | null = null;

  editingForm:
    | {
        from?: string;
      }
    | undefined = undefined;
  //#endregion

  // 下拉选项
  actionOptions: { label: string; value: string }[] = [
    {
      label: 'ESP',
      value: EActionType.ESP,
    },
    {
      label: 'HTTP',
      value: EActionType.HTTP,
    },
    {
      label: this.translate.instant('dj-脚本'),
      value: EActionType.Script,
    },
    // {
    //   label: this.translate.instant('dj-邮件'),
    //   value: EActionType.EMAIL,
    // },
    {
      label: this.translate.instant('dj-自动完成'),
      value: EActionType.AUTOCOMPLETE,
    },
  ];

  //#region esp
  // action开窗是否显示
  espActionVisible: boolean = false;

  produces: {
    [k: string]: string[];
  } = {};
  //#endregion

  //#region http
  readonly domainList: string[] = [
    'default',
    'eoc',
    'iam',
    'smartdata',
    'thememap',
    'aim',
    'atmc',
    'flowengine',
    'taskengine',
    'emc',
    'lcdp',
    'abi',
    'aam',
    'mdc',
    'itsys',
    'cac',
    'airh',
  ];
  //#endregion

  constructor(
    private fb: FormBuilder,
    private translate: TranslateService,
    private modal: AdModalService,
    private apiService: ViewApiService,
    private viewStoreService: ViewStoreService,
    private viewGraphService: ViewGraphService,
    private viewToolsService: ViewToolsService,
  ) {}

  ngOnInit() {
    const { delayConfig } = this.state.propertiesObj[this.state.currentSelectedNodeId];
    this.delayConfig = delayConfig;
    this.handleInit();
  }

  ngAfterViewInit(): void {
    this.scrollSubject = fromEvent(this.scrollContent.nativeElement, 'scroll').subscribe(
      throttle(
        () => {
          this.dropVisible = false;
        },
        100,
        { leading: true, trailing: false },
      ),
    );
  }

  ngOnDestroy(): void {
    this.scrollSubject?.unsubscribe();
    this.actionTypeSubject?.unsubscribe();
    this.destory$.next();
    this.destory$.complete();
  }

  private handleInit(): void {
    this.form?.reset();
    this.form = this.fb.group({
      delayStrategy: [this.delayConfig?.delayStrategy || ''],
      dateType: [this.delayConfig?.dateType || '', this.delayConfig?.delayStrategy ? [Validators.required] : null],
      dateValue: [this.delayConfig?.dateValue || '', this.delayConfig?.delayStrategy ? [Validators.required] : null],
      settingType: [this.delayConfig?.settingType || 'calculation'],
      actionType: [this.delayConfig?.actionType || ''],
      expression: [this.delayConfig?.expression || ''],
      actionCfg: this.fb.group({
        domain: [this.delayConfig?.actionCfg?.domain || ''],
        url: [this.delayConfig?.actionCfg?.url || ''],
        prod: [this.delayConfig?.actionCfg?.prod || ''],
        method: [this.delayConfig?.actionCfg?.method || ''],
        isAsync: [this.delayConfig?.actionCfg?.isAsync || false],
        serviceName: [this.delayConfig?.actionCfg?.serviceName || ''],
        header: [this.objectToString(this.delayConfig?.actionCfg?.header || {})],
        requestScript: [this.delayConfig?.actionCfg?.requestScript || ''],
        responseScript: [this.delayConfig?.actionCfg?.responseScript || ''],
        fieldInfos: this.fb.array(
          this.delayConfig?.actionCfg?.fieldInfos?.map((info) =>
            this.fb.group({
              fieldId: [info?.fieldId || null],
              fieldValue: [info?.fieldValue || null],
              fieldType: [info?.fieldType || null],
              fullPath: [info?.fullPath || null],
              paramType: [info?.paramType || null],
            }),
          ) || [],
        ),
      }),
    });

    if (this.delayConfig?.actionType === EActionType.ESP && this.delayConfig?.actionCfg?.serviceName) {
      this.queryProducts(this.delayConfig?.actionCfg?.serviceName);
    }
    this.subscriptActionChange();
    this.getCurrentData();
    this.form.get('delayStrategy').valueChanges.subscribe((value) => {
      this.handleDynamicRule(value);
    });
    this.form.valueChanges.pipe(takeUntil(this.destory$)).subscribe(() => {
      if (!this.formGroupValidityFlag) {
        this.getCurrentData();
      }
    });
    this.form.get('actionType').valueChanges.subscribe((value) => {
      if (!this.formGroupValidityFlag) {
        this.expand = true;
      }
    });
  }

  handleDynamicRule(value): void {
    if (!!value) {
      this.form.get('dateType').setValidators([Validators.required]);
      this.form.get('dateValue').setValidators([Validators.required]);
    } else {
      this.form.get('dateType').setValidators(null);
      this.form.get('dateValue').setValidators(null);
    }
    this.form.get('dateType').setValue('');
    this.form.get('dateValue').setValue('');
  }

  /**
   * 编辑条件
   * @param type
   * @param index
   */
  handleEditExpression(): void {
    const defaultExpression = this.form.get('expression').value;
    this.scriptData =
      defaultExpression ||
      `/* 
  此处编写内容为条件表达式，如 $variables['balance'] > 10  该表达式含义为当自定义变量'balance‘大于10时，通过该条件判断
*/`;
    this.scriptModal = true;
    this.editingForm = {
      from: 'expression',
    };
  }

  handleClosePanel(): void {
    this.close.emit();
  }

  /**
   * 展开/收起面板
   * @param e
   * @param type
   * @param index
   */
  toggleExpand(e: MouseEvent): void {
    e.stopPropagation();
    this.expand = !this.expand;
  }

  /**
   * 显示ESP动作选择面板
   * @param type
   * @param index
   */
  showEspAction(): void {
    this.editingForm = {};
    this.espActionVisible = true;
  }

  /**
   * 选择ESP动作面的回掉
   * @param data
   * @param type
   */
  async handleSelectAction(data: any, close: boolean): Promise<void> {
    if (!close && data.actionId) {
      const actionId = data.actionId.startsWith('esp_') ? data.actionId.replace('esp_', '') : data.actionId;
      const {} = this.editingForm;
      this.form.get('actionCfg').get('serviceName').setValue(actionId);
      this.espActionVisible = false;
      await this.queryProducts(actionId);
      this.handleSelect(this.produces[actionId][0]);
    } else {
      this.espActionVisible = false;
    }
    this.editingForm = undefined;
  }

  handleSelect(item: string): void {
    this.form.get('actionCfg').get('prod').setValue(item);
  }

  showScriptModal(from: 'requestScript' | 'responseScript'): void {
    const defaultScript = this.form.get('actionCfg').get(from).value;
    this.editingForm = { from: 'actionCfg.' + from };
    if (defaultScript) {
      this.scriptData = defaultScript;
    } else {
      const actionType = this.form.get('actionType').value;
      this.scriptData = this.getDefaultScript(actionType, from === 'requestScript');
    }
    this.scriptModal = true;
  }
  //#endregion

  //#region 脚本编辑回显
  handleCloseSript(action: 'confirm' | 'close', data: string): void {
    if (action === 'confirm') {
      const { from } = this.editingForm;
      if (from) {
        const path = from.split('.');
        let control: any = this.form;
        while (path.length) {
          const first = path.shift();
          control = control.get(first);
        }
        control.setValue(data);
      }
    }
    this.scriptModal = false;
    this.scriptData = undefined;
    this.editingForm = undefined;
  }

  handleDropMenuVisible(visible: boolean): void {
    this.dropVisible = visible;
  }

  /**
   * 获取产品名
   * @param actionId
   */
  private async queryProducts(actionId: string): Promise<void> {
    try {
      if (!this.produces[actionId]) {
        const res = (await this.apiService.getApiProvider(actionId).toPromise()) as any;
        this.produces[actionId] = res.data || [];
      }
    } catch {}
  }

  /**
   * 设置监听
   * @param type
   */
  private subscriptActionChange(): void {
    const actionCfg = this.form.get('actionCfg');
    this.actionTypeSubject?.unsubscribe();
    const setValidators = () => {
      const actionCfg = this.form.get('actionCfg');
      const fieldInfoForm = actionCfg.get('fieldInfos') as FormArray;
      fieldInfoForm.controls.forEach((control) => {
        control.get('fieldValue').setValidators(null);
      });
    };
    setValidators();
    this.actionTypeSubject = this.form.get('actionType').valueChanges.subscribe((value) => {
      setValidators();
      actionCfg.patchValue({
        type: null,
        domain: null,
        url: '',
        prod: '',
        method: '',
        isAsync: false,
        serviceName: '',
        header: this.objectToString({}),
        requestScript: '',
        responseScript: '',
        fieldInfos: [
          {
            fieldId: 'manage_status',
            fieldValue: null,
            fieldType: 'VARCHAR',
            fullPath: '',
            paramType: 'constant',
          },
        ],
      });
    });
  }

  /**
   * 获取当前数据
   */
  private getCurrentData = debounce(
    () => {
      this.formGroupValidityFlag = true;
      validatorForm(this.form);
      this.formGroupValidityFlag = false;
      this.setNodeValid();
      this.nodeErrorHighlight();
      const values = this.form.getRawValue();
      this.handleFormData(cloneDeep(values));
    },
    100,
    { leading: false, trailing: true },
  );

  private handleFormData(values: any): void {
    values.actionCfg.type = values.actionType;
    if (values.actionCfg.header) {
      try {
        values.actionCfg.header = JSON.parse(values.actionCfg.header);
      } catch {}
    }
    this.viewStoreService.setState((state) => {
      state.propertiesObj[this.state.currentSelectedNodeId].delayConfig = values;
    });
  }

  private getNodeData(): any {
    const currentSelectedNodeId = this.state.currentSelectedNodeId;
    return this.state.propertiesObj[currentSelectedNodeId];
  }

  private setNodeValid(): void {
    const { _isValidPassed } = this.getNodeData();
    let newValidPassed;
    if (isJSON(_isValidPassed)) {
      newValidPassed = { ..._isValidPassed, [PannelTabsType.DELAYCONFIG]: this.form.valid };
    } else {
      newValidPassed = { [PannelTabsType.BASE]: _isValidPassed, [PannelTabsType.DELAYCONFIG]: this.form.valid };
    }
    this.viewStoreService.setState((state) => {
      state.propertiesObj[this.state.currentSelectedNodeId]._isValidPassed = newValidPassed;
    });
  }

  private nodeErrorHighlight() {
    const { currentSelectedNodeId } = this.state;
    const node = this.viewGraphService.graph.getCellById(currentSelectedNodeId);
    const { _isValidPassed } = this.getNodeData();

    node.setData({
      isVerificationPassed: _isValidPassed,
    });
    this.viewToolsService.toolsNodeStyle(node, 'highlight');
  }

  /**
   * 获取默认的脚本
   * @param type
   * @param isReq
   * @returns
   */
  private getDefaultScript(type: EActionType, isReq: boolean = false): string {
    if (type === EActionType.ESP) {
      if (isReq) return scripts.script_5;
      return responseScript(JSON.stringify({}, null, '\t'));
    }
    if (type === EActionType.HTTP) {
      if (isReq) {
        return `/*
脚本处理请求参数后return内容为接口入参
  var request = {
      'std_data': {
          'parameter': {
          }
      }
  };
  return request;
 */`;
      }
      return `/*
处理返回结果并存储
var response = $(response);
  return {
    "success" : true,
    "processVariable" : {
      "key1" : "value1"
    },
    "errorMessage" : ""
  };
 */`;
    }
    if (type === EActionType.Script) {
      return `/*
进行数据处理，转换，更新
例 拿到当前节点输出中的name更新流程变量name
return{
  "name":$variables['ServiceTask_ec333f586c982d2b019cc8a42a061506']['name'] //当前节点id
}
 */`;
    }
    return undefined;
  }

  private objectToString(obj: any = {}): string {
    if (typeof obj === 'string') return obj;
    return JSON.stringify(obj);
  }
  //#endregion
}
