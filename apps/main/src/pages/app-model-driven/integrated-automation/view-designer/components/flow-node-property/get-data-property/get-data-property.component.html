<div class="get-data-property-root submit-form">
  <div class="header">
    <span>{{ 'dj-获取数据' | translate }}</span>
    <i adIcon iconfont="icondanchuxiaoxiguanbi" class="iconfont" aria-hidden="true" (click)="handleClosePanel()"></i>
  </div>
  <form nz-form class="content" [formGroup]="dataFormGroup">
    <nz-collapse [nzBordered]="false">
      <!--基本信息的面板-->
      <nz-collapse-panel [nzHeader]="'dj-基本信息' | translate" [nzActive]="true" [nzExpandedIcon]="'caret-right'">
        <nz-form-item class="nz-form-item-content">
          <nz-form-control [nzAutoTips]="errorTips">
            <!--节点id-->
            <div class="form-item">
              <div class="item-title">
                {{ 'dj-节点id' | translate }}
                <span class="item-required">*</span>
              </div>
              <app-component-input
                [attr]="{
                  name: '节点id',
                  required: true,
                  needLang: false,
                  readOnly: true
                }"
                style="width: 100%"
                formControlName="id"
                [value]="dataFormGroup.get('id')?.value"
                ngDefaultControl
              >
              </app-component-input>
            </div>
          </nz-form-control>
        </nz-form-item>

        <nz-form-item class="nz-form-item-content">
          <nz-form-control [nzAutoTips]="errorTips">
            <!--节点名称-->
            <div class="form-item">
              <div class="item-title">
                {{ 'dj-节点名称' | translate }}
                <span class="item-required">*</span>
              </div>
              <app-modal-input
                class="language-input"
                [attr]="{
                  name: 'name',
                  required: true,
                  lang: nameLang?.name,
                  needLang: true
                }"
                [innerLabel]="false"
                [value]="nameLang?.name?.[('dj-LANG' | translate)] || dataFormGroup.get('name')?.value"
                (callBack)="handlePatchApp('name', $event)"
                [formControlName]="'name'"
                ngDefaultControl
              ></app-modal-input>
            </div>
          </nz-form-control>
        </nz-form-item>
      </nz-collapse-panel>

      <nz-collapse-panel [nzHeader]="'dj-数据设置' | translate" [nzActive]="true" [nzExpandedIcon]="'caret-right'">
        <nz-form-item class="nz-form-item-content">
          <nz-form-control [nzAutoTips]="errorTips">
            <!--节点id-->
            <div class="form-item">
              <div class="item-title">
                {{ 'dj-获取来源' | translate }}
                <span class="item-required">*</span>
              </div>
              <nz-radio-group formControlName="dataSource">
                <label nz-radio nzValue="business">{{ 'dj-业务对象' | translate }}</label>
                <label nz-radio nzValue="node">{{ 'dj-数据节点' | translate }}</label>
              </nz-radio-group>
            </div>
          </nz-form-control>
        </nz-form-item>

        <nz-form-item
          *ngIf="dataFormGroup.get('dataSource')?.value === 'business'"
          class="nz-form-item-content"
          formGroupName="bindForm"
        >
          <nz-form-control [nzAutoTips]="errorTips">
            <!--节点名称-->
            <div class="form-item">
              <div class="item-title">
                {{ 'dj-业务对象' | translate }}
                <span class="item-required">*</span>
              </div>
              <ad-select
                formControlName="formCode"
                [nzPlaceHolder]="'dj-请选择' | translate"
                style="width: 100%"
                [nzAllowClear]="true"
                [nzOptions]="businessConstructorList"
              >
              </ad-select>
            </div>
          </nz-form-control>
        </nz-form-item>

        <nz-form-item *ngIf="dataFormGroup.get('dataSource')?.value === 'node'" class="nz-form-item-content">
          <nz-form-control [nzAutoTips]="errorTips">
            <!--节点名称-->
            <div class="form-item">
              <div class="item-title">
                {{ 'dj-数据节点' | translate }}
                <span class="item-required">*</span>
              </div>
              <ad-select
                formControlName="nodeList"
                [nzPlaceHolder]="'dj-请选择' | translate"
                style="width: 100%"
                nzAllowClear="true"
              >
                <ad-option
                  [nzLabel]="modal.nodeName"
                  [nzValue]="modal.id"
                  *ngFor="let modal of preGetNodesMode"
                ></ad-option>
              </ad-select>
            </div>
          </nz-form-control>
        </nz-form-item>

        <nz-form-item
          [ngClass]="{
            'nz-form-item-content ': true
          }"
          *ngIf="dataFormGroup.get('dataSource')?.value === 'business'"
        >
          <nz-form-control [nzAutoTips]="errorTips">
            <!--节点id-->
            <div class="form-item">
              <div class="item-title">
                {{ 'dj-数据过滤' | translate }}
                <span class="item-required">*</span>
              </div>
              <nz-radio-group formControlName="dataFilter">
                <label nz-radio nzValue="noFilter">{{ 'dj-全部' | translate }}</label>
                <label nz-radio nzValue="condition">{{ 'dj-按条件过滤' | translate }}</label>
              </nz-radio-group>
            </div>
          </nz-form-control>
        </nz-form-item>

        <app-data-view-filter
          *ngIf="
            dataFormGroup.get('dataSource')?.value === 'business' &&
            dataFormGroup.get('dataFilter')?.value === 'condition'
          "
          [dataViewFilterError]="dataViewFilterError"
          [allFields]="allFields"
          [currentLang]="currentLang"
          [conditionList]="conditionList"
          [isRequired]="true"
          [showTitle]="false"
          (dataViewFilterChange)="handleDataViewFilterChange($event)"
        ></app-data-view-filter>

        <nz-form-item
          *ngIf="dataFormGroup.get('dataSource')?.value === 'business'"
          [ngClass]="{
            'nz-form-item-content ': true,
            mt12: dataFormGroup.get('dataFilter')?.value === 'condition'
          }"
        >
          <nz-form-control [nzAutoTips]="errorTips">
            <!--节点id-->
            <div class="form-item">
              <div class="item-title">
                {{ 'dj-数据排序规则' | translate }}
                <span class="item-required">*</span>
              </div>
              <nz-radio-group formControlName="orderRule">
                <label nz-radio nzValue="no">{{ 'dj-不排序' | translate }}</label>
                <label nz-radio nzValue="asc">{{ 'dj-升序' | translate }}</label>
                <label nz-radio nzValue="desc">{{ 'dj-降序' | translate }}</label>
              </nz-radio-group>
            </div>
          </nz-form-control>
        </nz-form-item>
      </nz-collapse-panel>
    </nz-collapse>
  </form>
</div>
