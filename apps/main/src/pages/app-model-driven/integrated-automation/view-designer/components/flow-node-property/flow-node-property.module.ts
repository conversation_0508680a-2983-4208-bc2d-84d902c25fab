import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AutoCallEventPropertyComponent } from './auto-call-event-property/auto-call-event-property.component';
import { AutoEspPropertyComponent } from './auto-esp-property/auto-esp-property.component';
import { AutoHttpPropertyComponent } from './auto-http-property/auto-http-property.component';
import { WaitPropertyComponent } from './wait-property/wait-property.component';
import { AutoSubProjectPropertyComponent } from './auto-sub-project-property/auto-sub-project-property.component';
import { AutoNotifyPropertyComponent } from './auto-notify-property/auto-notify-property.component';
import { AutoScriptPropertyComponent } from './auto-script-property/auto-script-property.component';
import { ConditionBranchPropertyComponent } from './condition-branch-property/condition-branch-property.component';
import { ParallelBranchPropertyComponent } from './parallel-branch-property/parallel-branch-property.component';
import { ConditionOtherPropertyComponent } from './condition-other-property/condition-other-property.component';
import { EndFlowPropertyComponent } from './end-flow-property/end-flow-property.component';
import { FieldSettingPropertyComponent } from './field-setting-property/field-setting-property.component';
import { GlobalSettingComponent } from './global-setting/global-setting.component';
import { ManualApprovePropertyComponent } from './manual-approve-property/manual-approve-property.component';
import { ManualExecutionPropertyComponent } from './manual-execution-property/manual-execution-property.component';
import { ManualNotificationPropertyComponent } from './manual-notification-property/manual-notification-property.component';
import { PeopleSettingPropertyComponent } from './people-setting-property/people-setting-property.component';
import { StartEventPropertyComponent } from './start-event-property/start-event-property.component';
import { StartPagePropertyComponent } from './start-page-property/start-page-property.component';
import { StartTimerPropertyComponent } from './start-timer-property/start-timer-property.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NzCollapseModule } from 'ng-zorro-antd/collapse';
import { InputModule } from '../../../../../../components/form-components/input/input.module';

import { AdTabsModule } from 'components/ad-ui-components/ad-tabs/ad-tabs.module';
import { AdSelectModule } from 'components/ad-ui-components/ad-select/ad-select.module';
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox';
import { AdIconModule } from 'components/ad-ui-components/ad-icon/ad-icon.module';
import { NzSwitchModule } from 'ng-zorro-antd/switch';
import { AdDatePickerModule } from 'components/ad-ui-components/ad-date-picker/ad-date-picker.module';
import { AdButtonModule } from 'components/ad-ui-components/ad-button/ad-button.module';
import { NzTreeModule } from 'ng-zorro-antd/tree';
import { AdModalModule } from 'components/ad-ui-components/ad-modal/ad-modal.module';

import { AdInputNumberModule } from 'components/ad-ui-components/ad-input-number/ad-input-number.module';
import { TranslateModule } from '@ngx-translate/core';
import { NzFormModule } from 'ng-zorro-antd/form';
import { PeriodicModule } from '../../../../../../components/bussiness-components/periodic/periodic.module';
import { PreviewModalModule } from 'components/bussiness-components/preview-modal';
import { SelectEventModalModule } from 'components/bussiness-components/select-event-modal/select-event-modal.module';
import { ScriptEditorModule } from '../../../../../../components/bussiness-components/script-editor/script-editor.module';
import { ActionModalModule } from '../../../../../../components/bussiness-components/action-modal/action-modal.module';
import { DataViewModule } from '../../../../../../components/page-design/entries/data-view/data-view.module';
import { ViewApiService } from '../../service/api.service';
import { ViewStoreService } from '../../service/store.service';
import { PropertyMenuWrapperComponent } from './property-menu-wrapper/property-menu-wrapper.component';
import { TaskWorkDesignNewModule } from 'components/page-design/entries/task-work-design-new/task-work-design-new.module';
import { DataMappingComponent } from './data-mapping/data-mapping.component';
import { GlobalVariableComponent } from './global-variable/global-variable.component';
import { AdTreeSelectModule } from 'components/ad-ui-components/ad-tree-select/ad-tree-select.module';
import { VariableItemComponent } from './global-variable/variable-item/variable-item.component';
import { NzPopoverModule } from 'ng-zorro-antd/popover';
import { MappingTargetSelectModule } from './data-mapping/mapping-target-select/mapping-target-select.module';
import { AddDataPropertyComponent } from './add-data-property/add-data-property.component';
import { NzDropDownModule } from 'ng-zorro-antd/dropdown';
import { AddFieldValueComponent } from './add-data-property/components/add-field-value/add-field-value.component';
import { FieldSetComponent } from './add-data-property/components/field-set/field-set.component';
import { UpdateDataPropertyComponent } from './update-data-property/update-data-property.component';
import { GetDataPropertyComponent } from './get-data-property/get-data-property.component';
import { GetMultDataPropertyComponent } from './get-multData-property/get-multData-property.component';
import { DeleteDataPropertyComponent } from './delete-data-property/delete-data-property.component';
import { AdEmptyModule } from 'components/ad-ui-components/ad-empty/ad-empty.module';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { MutilTreeSelectModule } from './data-mapping/mutil-tree-select/mutil-tree-select.module';
import { VariableModalModule } from '../variable-modal/variable-modal.module';
import { SelectVariableModalComponent } from './people-setting-property/components/select-variable-modal/select-variable-modal.component';
import { GroupingDataPropertyComponent } from './grouping-data-property/grouping-data-property.component';
import { SelectDataSourceModalComponent } from './grouping-data-property/select-data-source-modal/select-data-source-modal.component';
import { StartManualPropertyComponent } from './start-manual-property/start-manual-property.component';
import { ProjectWorkDesignNewModule } from 'components/page-design/entries/project-work-design-new/project-work-design-new.module';
import { ExtendedInfoModule } from 'components/bussiness-components/extended-info/extended-info.module';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzCascaderModule } from 'ng-zorro-antd/cascader';
import { VariableSelectInputComponent } from './variable-select-input/variable-select-input.component';
import { VariableModalComponent } from './variable-select-input/components/variable-modal/variable-modal.component';
import { NzTabsModule } from 'ng-zorro-antd/tabs';
import { ProcessSettingComponent } from './global-setting/components/process-setting/process-setting.component';
import { ProjectCardComponent } from './global-setting/components/project-card/project-card.component';
import { ApproveCardSettingComponent } from './manual-approve-property/components/card-setting/card-setting.component';
import { ApproveNodeSettingComponent } from './manual-approve-property/components/node-setting/node-setting.component';
import { ExecutionCardSettingComponent } from './manual-execution-property/components/card-setting/card-setting.component';
import { CheckpointSettingComponent } from './manual-execution-property/components/checkpoint-setting/checkpoint-setting.component';
import { NzTableModule } from 'ng-zorro-antd/table';
import { TaskProjectCardDesignModule } from 'components/page-design/entries/task-project-card-design/task-project-card-design.module';
import { ActionSettingComponent } from './action-setting/action-setting.component';
import { NzSpinModule } from 'ng-zorro-antd/spin';
import { NzRadioModule } from 'ng-zorro-antd/radio';
import { DragDropModule } from '@angular/cdk/drag-drop';
import { AutoMsgsendPropertyComponent } from './auto-msgsend-property/auto-msgsend-property.component';
import { MsgFieldSetComponent } from './auto-msgsend-property/components/msg-field-set/msg-field-set.component';
import { MessageNotifyModule } from 'pages/app-model-driven/integrated-automation/components/message-notify/message-notify.module';
import { NzDrawerModule } from 'ng-zorro-antd/drawer';
import { DelayConfigComponent } from './delay-config/delay-config.component';
import { AutoEvokeWaitComponent } from './auto-evoke-wait/auto-evoke-wait.component';
import { EvokeWaitModalModule } from './auto-evoke-wait/components/action-modal/action-modal.module';
import { ReturnStrategyComponent } from './return-strategy/return-strategy.component';
import { ParallelStartPropertyComponent } from './parallel-start-property/parallel-start-property.component';
import { ConditionStartPropertyComponent } from './condition-start-property/condition-start-property.component';
import { ConditionEndPropertyComponent } from './condition-end-property/condition-end-property.component';
import { ParallelEndPropertyComponent } from './parallel-end-property/parallel-end-property.component';

@NgModule({
  declarations: [
    AutoCallEventPropertyComponent,
    AutoEspPropertyComponent,
    AutoHttpPropertyComponent,
    WaitPropertyComponent,
    AutoSubProjectPropertyComponent,
    AutoNotifyPropertyComponent,
    AutoScriptPropertyComponent,
    ConditionBranchPropertyComponent,
    ConditionOtherPropertyComponent,
    ParallelBranchPropertyComponent,
    EndFlowPropertyComponent,
    FieldSettingPropertyComponent,
    GlobalSettingComponent,
    ManualApprovePropertyComponent,
    ManualExecutionPropertyComponent,
    ManualNotificationPropertyComponent,
    PeopleSettingPropertyComponent,
    StartEventPropertyComponent,
    StartPagePropertyComponent,
    StartTimerPropertyComponent,
    PropertyMenuWrapperComponent,
    DataMappingComponent,
    GlobalVariableComponent,
    VariableItemComponent,
    AddDataPropertyComponent,
    AddFieldValueComponent,
    FieldSetComponent,
    UpdateDataPropertyComponent,
    GetDataPropertyComponent,
    GetMultDataPropertyComponent,
    DeleteDataPropertyComponent,
    SelectVariableModalComponent,
    GroupingDataPropertyComponent,
    SelectDataSourceModalComponent,
    StartManualPropertyComponent,
    VariableSelectInputComponent,
    VariableModalComponent,
    ProcessSettingComponent,
    ProjectCardComponent,
    ApproveCardSettingComponent,
    ApproveNodeSettingComponent,
    ExecutionCardSettingComponent,
    CheckpointSettingComponent,
    ActionSettingComponent,
    AutoMsgsendPropertyComponent,
    MsgFieldSetComponent,
    DelayConfigComponent,
    AutoEvokeWaitComponent,
    ReturnStrategyComponent,
    ParallelStartPropertyComponent,
    ConditionStartPropertyComponent,
    ConditionEndPropertyComponent,
    ParallelEndPropertyComponent,
  ],
  imports: [
    CommonModule,
    NzFormModule,
    FormsModule,
    ReactiveFormsModule,
    TranslateModule,
    NzCollapseModule,
    InputModule,
    AdIconModule,
    AdTabsModule,
    NzRadioModule,
    AdSelectModule,
    NzCheckboxModule,
    AdButtonModule,
    AdIconModule,
    NzCheckboxModule,
    NzSwitchModule,
    AdDatePickerModule,
    AdButtonModule,
    NzTreeModule,
    AdModalModule,
    AdInputNumberModule,
    PeriodicModule,
    PreviewModalModule,
    SelectEventModalModule,
    ScriptEditorModule,
    ActionModalModule,
    DataViewModule,
    TaskWorkDesignNewModule, //
    AdTreeSelectModule,
    NzPopoverModule,
    MappingTargetSelectModule,
    NzDropDownModule,
    AdEmptyModule,
    NzSpinModule,
    NzInputModule,
    NzToolTipModule,
    MutilTreeSelectModule,
    VariableModalModule,
    ProjectWorkDesignNewModule,
    ExtendedInfoModule,
    NzSelectModule,
    NzCascaderModule,
    NzTabsModule,
    NzSwitchModule,
    NzTableModule,
    NzCascaderModule,
    TaskProjectCardDesignModule,
    NzSpinModule,
    NzRadioModule,
    DragDropModule,
    MessageNotifyModule,
    NzDrawerModule,
    EvokeWaitModalModule,
  ],
  exports: [
    AutoCallEventPropertyComponent,
    AutoEspPropertyComponent,
    AutoHttpPropertyComponent,
    WaitPropertyComponent,
    AutoSubProjectPropertyComponent,
    AutoNotifyPropertyComponent,
    AutoScriptPropertyComponent,
    ConditionBranchPropertyComponent,
    ParallelBranchPropertyComponent,
    ConditionOtherPropertyComponent,
    EndFlowPropertyComponent,
    FieldSettingPropertyComponent,
    GlobalSettingComponent,
    ManualApprovePropertyComponent,
    ManualExecutionPropertyComponent,
    ManualNotificationPropertyComponent,
    PeopleSettingPropertyComponent,
    StartEventPropertyComponent,
    StartPagePropertyComponent,
    StartTimerPropertyComponent,
    PropertyMenuWrapperComponent,
    AddDataPropertyComponent,
    AddFieldValueComponent,
    FieldSetComponent,
    UpdateDataPropertyComponent,
    GetDataPropertyComponent,
    GetMultDataPropertyComponent,
    DeleteDataPropertyComponent,
    MsgFieldSetComponent,
    AutoEvokeWaitComponent,
    ParallelStartPropertyComponent,
    ConditionStartPropertyComponent,
    ConditionEndPropertyComponent,
    ParallelEndPropertyComponent,
  ],
  providers: [ViewStoreService, ViewApiService],
})
export class FlowNodePropertyModule {}
