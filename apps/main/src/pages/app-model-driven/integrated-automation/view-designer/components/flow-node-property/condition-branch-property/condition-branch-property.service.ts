import { Injectable } from '@angular/core';
import { SystemConfigService } from 'common/service/system-config.service';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { AppService } from '../../../../../../apps/app.service';

@Injectable()
export class ConditionBranchPropertyService {
  private adesignerUrl: string;
  constructor(
    private systemConfigService: SystemConfigService,
    public appService: AppService,
    private http: HttpClient,
  ) {
    this.systemConfigService.get('adesignerUrl').subscribe((url) => {
      this.adesignerUrl = url;
    });
  }
  /**
   * 查询当前解决方案下的基础资料
   * @returns
   */
  loadBasicByAppCode(): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/activityConfigs/getActivityListByPatternAndApplication`;
    return this.http.get(url, { params: { pattern: 'DATA_ENTRY', application: this.appService?.selectedApp?.code } });
  }

  // 泽宇那边数据视图查字段的
  // {
  //   "modelId": "canshu_canshudang",
  //   "productCode": "athena-lcdp-paas"
  // }
  queryDataViewFields(params: any): Observable<any> {
    // params = { modelId: 'canshu_canshudang', productCode: 'athena-lcdp-paas' };
    const url = `${this.adesignerUrl}/athena-designer/dataView/queryDataViewFields`;
    const req = params.pageViewCode
      ? {
          pageViewCode: params.pageViewCode,
        }
      : {
          modelId: params.code,
          productCode: params.serviceCode,
        };
    return this.http.post(url, req);
  }
  // 根据模型code批量查询出模型名称等信息
  findModelByCode(param): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/process/findModelByCode`;
    return this.http.post(url, param);
  }
}
