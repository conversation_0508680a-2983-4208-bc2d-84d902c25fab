import { Component, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>roy, OnInit, Output } from '@angular/core';
import { Form<PERSON>rray, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { CommonValidators } from 'common/utils/common.validators';
import { ViewStoreService } from '../../../service/store.service';
import { ViewApiService } from '../../../service/api.service';
import { DataMappingService } from './service/data-mapping.service';
import { debounceTime, distinctUntilChanged, takeUntil } from 'rxjs/operators';
import { isJSON, validatorForm } from 'common/utils/core.utils';
import { ViewToolsService } from '../../../service/tools.service';
import { ViewGraphService } from '../../../service/graph.service';
import { NodeType, PannelTabsType, VariableType } from '../../../config/typings';
import { Subject, Subscription } from 'rxjs';
import { VariableService } from '../../../service/variable.service';
import { EVariableRange } from '../variable-select-input/components/variable-modal/variable-modal.component';

// 数据映射数据结构：https://z0lxpczot6u.feishu.cn/wiki/QfbSwVG5QixQzykHJMkc491NnQg#Z3HqdWxogosUYuxZ57lcfgnonpz
@Component({
  selector: 'app-data-mapping',
  templateUrl: './data-mapping.component.html',
  styleUrls: ['./data-mapping.component.less'],
  providers: [DataMappingService],
})
export class DataMappingComponent implements OnInit, OnDestroy {
  @Output() close = new EventEmitter();

  form: FormGroup;
  scriptModalVisible: boolean = false;
  // 当前节点下拉数据
  currentNodeSelectData: any;
  // 映射目标下拉数据
  targetSelectData: any[];
  // 当前script类型：input/output
  currentScriptType: string;
  // script脚本
  script: any;
  loading: boolean;

  isAutoNode: boolean = false; // 是否是自动节点
  isWaitNode: boolean = false; // 是否是等待节点

  defaultInputScript = `/*
  输入映射定义任务卡推送数据，间接影响打开任务卡数据源入参（页面数据源为esp数据源情况下）
  以下为取流程变量组成入参demo，return内容为推送数据
  var name = $variables['name'];
  var id = $variables['id'];
  return {
      "name": name,
      "id": id
  }
*/`;

  defaultAutoOutputScript = `/*
节点完成后置操作，一般进行一些数据转换及处理
例 拿到当前节点输出中的name更新流程变量name
return{
  "name":$variables['ServiceTask_ec333f586c982d2b019cc8a42a061506']['name'] //当前节点id
}
 */`;

  inputMapping = this.fb.array([]); // 入参设置mapping
  outputMapping = this.fb.array([]); // 出参设置mapping
  outputTypeSubscribe: Subscription;
  inputTypeSubscribe: Subscription;
  formSubscribe: Subscription;
  destroy$ = new Subject();

  get state() {
    return this.viewStoreService.state;
  }

  // 输入的变量映射不能包含节点变量
  get outputTargetSelectData() {
    if (!this.targetSelectData) return [];
    return this.targetSelectData.filter(
      (item) => ![VariableType.NODE, VariableType.PROCESS, VariableType.Mechanism].includes(item.dataType),
    );
  }

  get outputRange() {
    const mode = EVariableRange.CUSTOM | EVariableRange.MODEL | EVariableRange.BUSINESS;
    if (this.viewStoreService.isFusionMode()) return mode | EVariableRange.DTD;
    return mode;
  }

  get inputRange() {
    const mode =
      EVariableRange.CUSTOM |
      EVariableRange.NODE |
      EVariableRange.MODEL |
      EVariableRange.SYSTEM |
      EVariableRange.MECHANISM |
      EVariableRange.BUSINESS;
    if (this.viewStoreService.isFusionMode()) return mode | EVariableRange.DTD;
    return mode;
  }

  constructor(
    private fb: FormBuilder,
    private viewStoreService: ViewStoreService,
    private viewService: ViewApiService,
    private mappingService: DataMappingService,
    private viewToolsService: ViewToolsService,
    private viewGraphService: ViewGraphService,
    private variableService: VariableService,
  ) {}

  ngOnDestroy(): void {
    this.outputTypeSubscribe.unsubscribe();
    this.inputTypeSubscribe.unsubscribe();
    this.formSubscribe.unsubscribe();
    this.destroy$.next(null);
    this.destroy$.complete();
  }

  ngOnInit() {
    // 创建表单
    this.form = this.generateForm();
    this.setAutoNode();
    this.setWaitNode();
    // 获取当前节点下拉数据
    this.fetchCurrentNodeSelectData();
    // 获取映射目标下拉数据
    this.getTargetSelectData();
    // 初始化数据
    this.initData();

    this.formSubscribe = this.form.valueChanges.pipe(debounceTime(300), distinctUntilChanged()).subscribe((value) => {
      const variablesMapping = this.form.getRawValue();
      this.viewStoreService.setState((state) => {
        // 设置数据映射数据
        state.propertiesObj[this.state.currentSelectedNodeId].variablesMapping = variablesMapping;
      });
      // 设置节点验证结果
      this.setPannelValid();
      // 节点错误高亮
      this.nodeErrorHighlight();
    });

    // 监听新增流程变量后重新获取
    this.variableService.variableRefresh$.pipe(takeUntil(this.destroy$)).subscribe((data) => {
      if (data) {
        // 获取映射目标下拉数据
        this.getTargetSelectData();
      }
    });

    // UI模式、脚本模式切换映射数组
    this.inputTypeSubscribe = this.setMappingArray('input');
    this.outputTypeSubscribe = this.setMappingArray('output');

    validatorForm(this.form);
    // 设置节点验证结果
    this.setPannelValid();
    // 节点错误高亮
    this.nodeErrorHighlight();
  }

  /**
   * 获取人工节点的默认输出脚本
   * @returns
   */
  getDefaultManualOutputScript(): string {
    return `/*
  输出映射定义当前节点的后置动作，进行数据转换处理变量
  例如拿到当前节点提交的name更新流程变量中的name(其中变量名称根据实际使用替换)
  return{
      "name":$variables['${this.state.originalFlowData.code}']['${
      this.state?.modelData?.modelCode ?? 'ljccqq'
    }'][0]['part_name']
  }
 */`;
  }

  /**
   * 生成表单
   * @returns
   */
  generateForm(): FormGroup {
    return this.fb.group({
      // 输入
      input: this.fb.group({
        // mapping:UI模式、script:脚本模式
        type: ['mapping'],
        mapping: this.inputMapping,
        script: [null, CommonValidators.emptyStringToNull],
      }),
      // 输出
      output: this.fb.group({
        type: ['mapping'],
        mapping: this.outputMapping,
        script: [null, CommonValidators.emptyStringToNull],
      }),
    });
  }

  /**
   * 初始化数据
   */
  initData(): void {
    const { variablesMapping, _nodeType } = this.state.propertiesObj[this.state.currentSelectedNodeId];

    this.handleAutoNode();
    this.handleWaitNode();

    if (!variablesMapping) return;
    const { input, output } = variablesMapping;
    // 输入
    input?.mapping?.forEach(() => this.addMapping(this.inputMapping));
    // 输出
    output?.mapping?.forEach(() => this.addMapping(this.outputMapping));
    // 赋值
    this.form.patchValue(variablesMapping, { emitEvent: false });
    (window as any).form = this.form;
  }

  setAutoNode() {
    const { _nodeType } = this.state.propertiesObj[this.state.currentSelectedNodeId];
    this.isAutoNode = !![
      NodeType.AUTO_HTTP,
      NodeType.AUTO_ESP,
      NodeType.AUTO_NOTIFY,
      NodeType.MSG_SEND,
      NodeType.AUTO_SCRIPT,
      NodeType.AUTO_CALL_EVENT,
      NodeType.DATA_ADD,
      NodeType.DATA_UPDATE,
      NodeType.DATA_GET,
      NodeType.DATA_GET_MULTI,
      NodeType.DATA_DELETE,
    ].includes(_nodeType);
  }

  /**
   * @description: 设置等待/唤起等待节点
   */
  setWaitNode() {
    const { _nodeType } = this.state.propertiesObj[this.state.currentSelectedNodeId];
    this.isWaitNode = !![
      NodeType.AUTO_WAIT,
      NodeType.AUTO_EVOKE_WAIT,
    ].includes(_nodeType);
  }

  handleAutoNode() {
    if (this.isAutoNode) {
      this.form.get(['output', 'type']).setValue('script');
      // this.form.get(['output', 'type']).disable();
    }
    return;
  }
  /**
   * @description: 设置等待/唤起等待节点默认值
   */
  handleWaitNode() {
    if (this.isWaitNode) {
      this.form.get(['output', 'type']).setValue('script');
      this.form.get(['input', 'type']).setValue('script');
    }
    return;
  }

  /**
   * 添加映射
   * @param formArray 表单数组
   */
  addMapping(formArray: FormArray): void {
    const group = this.fb.group({
      keyCode: [null, Validators.required],
      valueCode: [null, Validators.required],
      valueType: [null],
      valuePath: [null],
      valueSuffix: [null],
    });
    formArray.push(group);
  }

  /**
   * 删除映射
   * @param formArray
   * @param index
   */
  removeMapping(type: string, index: number): void {
    const formArray = type === 'input' ? this.inputMapping : this.outputMapping;
    formArray.removeAt(index);
  }

  /**
   * 添加映射
   */
  handleAddMapping(type: string) {
    const formArray = type === 'input' ? this.inputMapping : this.outputMapping;
    this.addMapping(formArray);
  }

  /**
   * 点击打开脚本编辑器
   */
  handleOpenSript(type: 'input' | 'output'): void {
    this.scriptModalVisible = true;
    this.currentScriptType = type;

    const defaultScript =
      type === 'input'
        ? this.defaultInputScript
        : this.isAutoNode
        ? this.defaultAutoOutputScript
        : this.getDefaultManualOutputScript();
    this.script = this.form.get([type, 'script']).value || defaultScript;
  }

  handleCloseSript(type, data): void {
    this.scriptModalVisible = false;

    if (type === 'confirm') {
      this.form.get([this.currentScriptType, 'script']).setValue(data);
    }
  }

  /**
   * 获取流程数据
   */
  getProcessData() {
    const processCode = this.state.originalFlowData.code;
    return this.state.propertiesObj[processCode];
  }

  /**
   * 根据节点id获取对应数据
   * @param nodeId 节点id
   * @returns
   */
  getNodeData() {
    const currentSelectedNodeId = this.state.currentSelectedNodeId;
    return this.state.propertiesObj[currentSelectedNodeId];
  }

  /**
   * 获取当前节点下拉数据
   */
  async fetchCurrentNodeSelectData(): Promise<any> {
    const { pageView, bindForm } = this.getNodeData();
    let params = {};

    try {
      if (bindForm?.type === 'pageView') {
        //  如果是自定义任务的话传，此时不传模型信息
        params['pageViewCode'] = pageView.code;
      } else {
        const { modelCode, serviceCode } = bindForm;
        // 非自定义任务
        params['modelCode'] = modelCode;
        params['serviceCode'] = serviceCode;
      }
      this.loading = true;
      const res = await this.viewService.queryFieldsByCode(params).toPromise();
      // 转成当前节点下拉可用的数据
      this.currentNodeSelectData = this.mappingService.transformTreeData(res.data);
    } catch (error) {
    } finally {
      this.loading = false;
    }
  }

  /**
   * 批量获取业务对象树
   * @returns
   */
  async fetchBusinessObjectTrees(): Promise<any> {
    try {
      // 批量获取业务对象树
      this.loading = true;
      const params = this.getBusinessObjectParams();
      const { data } = await this.viewService.queryFieldsGroupList(params).toPromise();
      const modelFieldInfoVos = data.map((item) => ({
        ...item.modelFieldInfoVo,
      }));
      return modelFieldInfoVos;
    } catch (error) {
    } finally {
      this.loading = false;
    }
  }

  /**
   * 获取模型树
   * @param modelVariables
   */
  async handleModelTreeList(modelVariables) {
    try {
      // 批量获取业务对象树
      this.loading = true;
      const params = modelVariables.map((item) => ({
        serviceCode: item.serviceCode,
        modelCode: item.modelCode,
        variableCode: item.uniqueKey,
      }));
      const { data } = await this.viewService.queryFieldsGroupList(params).toPromise();
      const modelFieldInfoVos = data.map((item) => ({
        ...item.modelFieldInfoVo,
      }));
      return this.mappingService.transformTreeData(modelFieldInfoVos, VariableType?.MODEL);
    } catch (error) {
    } finally {
      this.loading = false;
    }
  }

  /**
   * 组装映射目标下拉数据
   * @returns
   */
  async getTargetSelectData() {
    if (this.isAutoNode) {
      return;
    }
    const data = await this.fetchBusinessObjectTrees();
    const treeList = this.mappingService.transformTreeData(data);
    const {
      customVariables = [],
      systemVariable = [],
      modelVariables = [],
      mechanismVariables = [],
    } = this.getProcessData();
    const modelTreeList = await this.handleModelTreeList(modelVariables);
    const { currentSelectedNodeId } = this.state;
    const currentNode = this.viewGraphService.graph.getCellById(currentSelectedNodeId);
    const nodeVariables = currentNode
      ? this.viewGraphService.getNodesVariable({ type: 'pre', node: currentNode as any })
      : [];
    this.targetSelectData = [
      // 自定义变量
      ...customVariables.map((item) => ({
        label: (item.description ?? '') + '(' + item.varName + ')',
        value: item.varName,
        dataType: item.dataType,
      })),
      // 系统变量
      ...systemVariable.map((item) => ({
        label: (item.description ?? '') + '(' + item.varName + ')',
        value: item.varName,
        dataType: VariableType.PROCESS,
      })),
      // 节点变量
      ...nodeVariables.map((item) => ({
        label: (item.varName ?? '') + '(' + item.nodeId + ')',
        value: item.nodeId,
        dataType: VariableType.NODE,
      })),
      // 业务对象
      ...treeList.map((item) => ({
        ...item,
        dataType: VariableType.BUSINESS_OBJECT,
      })),
      ...modelTreeList.map((item) => ({
        ...item,
        dataType: VariableType.MODEL,
      })),
      ...mechanismVariables.map((item) => ({
        label: (item.description ?? '') + '(' + item.varName + ')',
        value: item.actionId,
        dataType: VariableType.Mechanism,
      })),
    ];
  }

  /**
   * 获取业务对象参数
   * @returns {serviceCode:string,modelCode:string}[]
   */
  getBusinessObjectParams() {
    const processData = this.getProcessData();
    const { businessObjectVariables = [] } = processData;

    return businessObjectVariables.map((item) => ({
      serviceCode: item.serviceCode,
      modelCode: item.modeCode,
      variableCode: item.varName,
    }));
  }

  /**
   * 映射目标Change回调
   * @param value
   */
  handleMappingTargetChange(
    data: { valueCode: string; valueType: string; variableCode?: string; variableSuffix?: string },
    type: 'input' | 'output',
    index: number,
  ) {
    this.form.get([type, 'mapping', index, 'valuePath']).setValue(null);
    this.form.get([type, 'mapping', index, 'valueType']).setValue(data.valueType);
    if (data.valueType === VariableType.BUSINESS_OBJECT || data.valueType === VariableType.MODEL) {
      // 如果是对象变量，将valuePath的值设为变量名
      this.form.get([type, 'mapping', index, 'valueCode']).setValue(data.variableCode);
      this.form.get([type, 'mapping', index, 'valuePath']).setValue(data.valueCode);
    } else {
      this.form.get([type, 'mapping', index, 'valueCode']).setValue(data.valueCode);
      if (data.valueType === VariableType.NODE) {
        this.form.get([type, 'mapping', index, 'valuePath']).setValue('');
      } else if (data.valueType === VariableType.Mechanism) {
        this.form.get([type, 'mapping', index, 'valuePath']).setValue(data.variableCode);
      }
    }
    this.form.get([type, 'mapping', index, 'valueSuffix']).setValue(data.variableSuffix);
    this.form.get([type, 'mapping', index, 'valueCode']).markAsDirty();
    this.form.get([type, 'mapping', index, 'valueCode']).updateValueAndValidity();
  }

  /**
   * 节点错误高亮
   */
  nodeErrorHighlight() {
    const { currentSelectedNodeId } = this.state;
    const node = this.viewGraphService.graph.getCellById(currentSelectedNodeId);
    const { _isValidPassed } = this.getNodeData();

    node.setData({
      isVerificationPassed: _isValidPassed,
    });
    this.viewToolsService.toolsNodeStyle(node, 'highlight');
  }

  /**
   * 设置节点验证结果
   */
  setPannelValid() {
    const { _isValidPassed } = this.getNodeData();
    let newValidPassed;

    if (isJSON(_isValidPassed)) {
      newValidPassed = { ..._isValidPassed, [PannelTabsType.DATA_MAPPING]: this.form.valid };
    } else {
      newValidPassed = { [PannelTabsType.BASE]: _isValidPassed, [PannelTabsType.DATA_MAPPING]: this.form.valid };
    }

    this.viewStoreService.setState((state) => {
      // 更新节点是否通过验证
      state.propertiesObj[this.state.currentSelectedNodeId]._isValidPassed = newValidPassed;
    });
  }

  /**
   * 根据UI模式、脚本模式切换映射数组
   * 用于表单验证，仅验证当前模式
   * @param value
   * @param type 'input' | 'output'
   */
  setMappingArray(type: 'input' | 'output'): Subscription {
    return this.form
      .get([type, 'type'])
      .valueChanges.pipe(distinctUntilChanged())
      .subscribe((value: 'mapping' | 'script') => {
        const group = this.form.get(type) as FormGroup;
        const formArray = type === 'input' ? this.inputMapping : this.outputMapping;
        const array = value === 'script' ? this.fb.array([]) : formArray;
        group.setControl('mapping', array);
      });
  }

  handleClosePanel(): void {
    this.close.emit();
  }
}
