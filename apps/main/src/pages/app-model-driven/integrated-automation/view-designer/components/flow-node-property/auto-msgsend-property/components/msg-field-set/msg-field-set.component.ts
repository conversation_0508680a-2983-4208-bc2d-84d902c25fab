import { Component, EventEmitter, Input, OnChanges, OnDestroy, OnInit, Output, SimpleChanges } from '@angular/core';
import { FormArray, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NzMessageService } from 'ng-zorro-antd/message';
import { TranslateService } from '@ngx-translate/core';
import { validatorForm } from 'common/utils/core.utils';
import { Subject, Subscription } from 'rxjs';
import { EVariableRange } from '../../../variable-select-input/components/variable-modal/variable-modal.component';
import {
  dataTypeToValueType,
  dataTypeToVariableType,
  variableTypeToDataType,
} from 'pages/app-model-driven/utils/utils';
import { VariableType } from 'pages/app-model-driven/integrated-automation/view-designer/config/typings';
import { MsgFieldSetService } from './msg-field-set.service';
import { takeUntil } from 'rxjs/operators';

@Component({
  selector: 'app-msg-field-set',
  templateUrl: './msg-field-set.component.html',
  styleUrls: ['./msg-field-set.component.less'],
})
export class MsgFieldSetComponent implements OnInit, OnChanges, OnDestroy {
  @Input() fieldInfosData;
  @Input() nodeId;
  @Input() style = '';
  @Input() required: boolean = true;
  @Input() explainBody: any = [];
  @Input() autoFillField: boolean = true;

  @Output() fieldInfosDataBack: EventEmitter<any> = new EventEmitter();

  errorTips: Record<string, Record<string, string>> = {
    default: {
      required: this.translate.instant('dj-必填'),
      pattern: this.translate.instant('dj-超长'),
    },
  };

  fieldSetError;

  dataFormGroup: FormGroup;
  get fieldInfos() {
    return this.dataFormGroup?.get('fieldInfos') as FormArray;
  }

  formGroupValidityFlag = false;

  fieldTypeEum = ['constant', 'variable'];
  fieldType = {
    constant: {
      icon: 'iconchangliang1',
      name: this.translate.instant('dj-常量'),
    },
    variable: {
      icon: 'iconbianliang',
      name: this.translate.instant('dj-变量'),
    },
  };
  fieldValueItem: any;
  fieldValueItemIndex: any;
  showFieldValueModal: boolean = false;

  private destory$ = new Subject();

  get variableRange() {
    return (
      EVariableRange.MODEL |
      EVariableRange.SYSTEM |
      EVariableRange.BUSINESS |
      EVariableRange.CUSTOM |
      EVariableRange.NODE |
      EVariableRange.MECHANISM
    );
  }

  constructor(
    private translate: TranslateService,
    private message: NzMessageService,
    private fb: FormBuilder,
    private msgService: MsgFieldSetService,
  ) {
    this.dataFormGroup = this.fb.group({
      fieldInfos: this.fb.array([]),
    });
    this.msgService.sceneChanged$.pipe(takeUntil(this.destory$)).subscribe(() => {
      this.formGroupValidityFlag = true;
      this.fieldInfos.clear();
      this.formGroupValidityFlag = false;
      this.getCurrentData();
    });
  }
  ngOnDestroy(): void {
    this.destory$.next();
    this.destory$.complete();
  }

  ngOnChanges(changes: SimpleChanges): void {
    const keys = Object.keys(changes);
    if (keys.includes('nodeId')) {
      if (!changes.nodeId.firstChange) {
        this.formGroupValidityFlag = true;
        this.fieldInfos.clear();
        this.handleInit();
        this.formGroupValidityFlag = false;
        this.getCurrentData();
      }
    }
  }

  ngOnInit() {
    this.handleInit();
    this.getCurrentData();
    this.dataFormGroup.valueChanges.subscribe(() => {
      if (!this.formGroupValidityFlag) {
        this.getCurrentData();
      }
    });
  }

  async handleInit() {
    this.fieldInfos.clear();
    let fieldInfosData = this.fieldInfosData;
    fieldInfosData.forEach((item) => {
      this.fieldInfos.push(
        this.fb.group({
          fieldId: [item.fieldId, [Validators.required]],
          fieldValue: [item.fieldValue, [Validators.required]],
          paramType: [item.paramType || 'constant', [Validators.required]],
          fieldType: [item.fieldType, [Validators.required]],
          isRequired: [item.isRequired],
          fullPath: [item.fullPath, [Validators.required]],
          valueType: [item.valueType],
          fieldName: [item.fieldName],
          modelCode: [item.modelCode],
          serviceCode: [item.serviceCode],
          nodeId: [item.nodeId],
          noteType: [item.noteType],
          noteSubType: [item.noteSubType],
          variableCode: [item.variableCode],
          valueSuffix: [item.valueSuffix],
        }),
      );
    });
  }

  /**
   * 字段 select disabled
   * @param value
   * @returns
   */
  fieldIdDisabled(value) {
    return this.explainBody?.findIndex((node) => node.fieldId === value && node.notNull) > -1;
  }

  /**
   * 字段 select operation disabled
   * @param item
   * @returns
   */
  selectDisabled(item) {
    return this.fieldInfos.getRawValue()?.findIndex((node) => node.fieldId === item.fieldId) > -1;
  }

  /**
   * 删除字段
   * @param index
   * @param value
   * @returns
   */
  removeItem(index, isRequired) {
    if (isRequired) {
      return;
    }
    this.fieldInfos.removeAt(index);
  }

  /**
   * 修改字段类型
   * @param type
   * @param i
   */
  changeParamType(type, i) {
    if (this.fieldInfos.at(i).get('paramType').value === type) return;
    if (type === 'constant') {
      this.fieldInfos.at(i).patchValue({
        serviceCode: '',
        modelCode: '',
        valueType: '',
        fieldName: '',
        nodeId: '',
        noteType: '',
        noteSubType: '',
        variableCode: '',
        valueSuffix: '',
      });
    }
    this.fieldInfos.at(i).patchValue({
      paramType: type,
      fieldValue: '',
    });
  }

  /**
   * 新增字段
   */
  handleAddFields() {
    this.confirmAddFields();
  }

  /**
   * 确定新增字段
   */
  confirmAddFields() {
    this.fieldInfos.push(
      this.fb.group({
        fieldId: ['', [Validators.required]],
        fieldValue: ['', [Validators.required]],
        paramType: ['constant', [Validators.required]],
        fieldType: ['', [Validators.required]],
        isRequired: [false],
        fullPath: ['', [Validators.required]],
        valueType: [''],
        fieldName: [''],
        modelCode: [''],
        serviceCode: [''],
        nodeId: [''],
        noteType: [''],
        noteSubType: [''],
        variableCode: [''],
        valueSuffix: [''],
      }),
    );
  }

  /**
   * 选取字段值变化
   */
  onChangeFieldId(e, i) {
    const currentFieldItem = this.explainBody.find((node) => node.fieldId === e);
    if (!currentFieldItem) return;
    this.fieldInfos.at(i).patchValue({
      fieldType: currentFieldItem.fieldType,
      isRequired: currentFieldItem.notNull,
      fullPath: currentFieldItem.fullPath,
    });
  }

  /**
   * 选取字段值
   * @param item
   * @param i
   */
  selectFieldValueModal(item, i) {
    this.showFieldValueModal = true;
    this.fieldValueItem = item;
    this.fieldValueItemIndex = i;
  }

  confirmFieldValue() {
    this.showFieldValueModal = false;
  }

  changeFieldValueItem(data) {
    this.fieldInfos.at(this.fieldValueItemIndex).patchValue({
      ...data,
    });
    this.confirmFieldValue();
  }

  getCurrentData() {
    this.formGroupValidityFlag = true;
    validatorForm(this.dataFormGroup);
    this.formGroupValidityFlag = false;
    const currentData = this.dataFormGroup.getRawValue()?.fieldInfos;
    // 校验为空
    let isEmptyPassed = true;
    if (this.required && this.autoFillField && (!currentData || currentData?.length === 0)) {
      isEmptyPassed = false;
      this.fieldSetError = true;
    } else {
      isEmptyPassed = true;
      this.fieldSetError = false;
    }
    // 表单校验
    let isVerificationPassed = this.dataFormGroup.valid && isEmptyPassed;
    this.fieldInfosDataBack.emit({ currentData, isVerificationPassed });
  }

  handleValueChanged(event: any, index: number): void {
    const oldValue = this.fieldInfos.getRawValue()[index];
    const { modelCode, serviceCode, valueCode, variableCode, dataType } = event;
    const type = dataTypeToValueType(dataType);
    if (type === 'modelVariable' || type === 'businessObjectVariable') {
      const e = {
        ...oldValue,
        valueType: type,
        modelCode,
        serviceCode,
        fieldValue: variableCode,
        variableCode: valueCode,
      };
      this.fieldValueItemIndex = index;
      this.changeFieldValueItem(e);
    } else {
      const e = {
        ...oldValue,
        valueType: type,
        modelCode,
        serviceCode,
        fieldValue: valueCode,
        variableCode: valueCode,
      };
      if (type === 'mechanismVariable') {
        e.fieldValue = variableCode;
      }
      this.fieldValueItemIndex = index;
      this.changeFieldValueItem(e);
    }
  }

  getItem(index: number): any {
    if (this.fieldInfosData[index] === undefined) return undefined;
    const item = this.fieldInfosData[index];
    const type = dataTypeToVariableType(item.valueType || 'processVariable');
    if (type === VariableType.Mechanism) {
      return {
        valueSuffix: item.valueSuffix,
        valueType: type,
        valuePath: item.fieldValue,
        valueCode: item.variableCode,
      };
    }
    if (type === VariableType.BUSINESS_OBJECT) {
      return {
        valueSuffix: item.valueSuffix,
        valueType: type,
        valuePath: item.variableCode,
        valueCode: item.fieldValue,
      };
    }
    if (type === VariableType.MODEL) {
      // 为了兼容以前的老数据
      if (item.variableCode) {
        return {
          valueSuffix: item.valueSuffix,
          valueType: type,
          valuePath: item.variableCode,
          valueCode: item.fieldValue,
        };
      }
      return {
        valueSuffix: item.valueSuffix,
        valueType: type,
        valuePath: `${item.modelCode}&${item.serviceCode}`,
        valueCode: item.fieldValue,
      };
    }
    return {
      valueSuffix: item.valueSuffix,
      valueType: type,
      valuePath: item.fieldValue, // 模型/业务对象
      valueCode: item.fieldValue, // 变量
    };
  }
}
