import { Component, <PERSON><PERSON><PERSON>ter, <PERSON><PERSON><PERSON>roy, OnInit, Output } from '@angular/core';
import { NzTreeNodeOptions } from 'ng-zorro-antd/tree';
import { ViewStoreService } from '../../../service/store.service';
import { cloneDeep } from 'common/utils/core.utils';
import { VariableType } from '../../../config/typings';
import { GlobalVariableService } from './global-variable.service';
import { ViewGraphService } from '../../../service/graph.service';
import { LocaleService } from 'common/service/locale.service';
import { VariableService } from '../../../service/variable.service';
import { TranslateService } from '@ngx-translate/core';
import { NzMessageService } from 'ng-zorro-antd/message';
import { takeUntil } from 'rxjs/operators';
import { Subject } from 'rxjs';

@Component({
  selector: 'app-global-variable',
  templateUrl: './global-variable.component.html',
  styleUrls: ['./global-variable.component.less'],
  providers: [GlobalVariableService],
})
export class GlobalVariableComponent implements OnInit, OnDestroy {
  customVariables: any[] = [];
  systemVariable: any[] = [];
  businessObjectVariables: any[] = [];
  mechanismVariables: any[] = [];
  nodeVariables: any[] = [];
  dtdVariable: any[] = [];
  modelVariables: any[] = [];
  originCustomVariables: any[] = [];
  originSystemVariable: any[] = [];
  originBusinessObjectVariables: any[] = [];
  originMechanismVariables: any[] = [];
  originNodeVariables: any[] = [];
  originDtdVariable: any[] = [];
  originModelVariables: any[] = [];
  variableExtendData: any[] = [];
  searchValue: string = '';
  dataArray: any[] = [
    {
      key: 'customVariables', // 自定义变量
      originKey: 'originCustomVariables',
    },
    {
      key: 'systemVariable', // 系统变量
      originKey: 'originSystemVariable',
    },
    {
      key: 'businessObjectVariables', // 业务对象变量
      originKey: 'originBusinessObjectVariables',
    },
    {
      key: 'nodeVariables', // 节点变量
      originKey: 'originNodeVariables',
    },
    {
      key: 'modelVariables', // 模型变量
      originKey: 'originModelVariables',
    },
    {
      key: 'mechanismVariables', // 机制变量
      originKey: 'originMechanismVariables',
    },
    {
      key: 'dtdVariable', // DTD变量
      originKey: 'originDtdVariable',
    },
  ];
  destroy$ = new Subject();
  @Output() close: any = new EventEmitter();

  /**
   * 是不是融合模式
   */
  get isFusionMode(): boolean {
    return this.viewStoreService.isFusionMode();
  }

  constructor(
    private viewStoreService: ViewStoreService,
    private variableService: VariableService,
    private languageService: LocaleService,
    private globalVariableService: GlobalVariableService,
    private translateService: TranslateService,
    private athMessageService: NzMessageService,
  ) {
    this.variableService.variableRefresh$.pipe(takeUntil(this.destroy$)).subscribe((data) => {
      if (data) {
        this.getVariableData();
      }
    });
  }
  ngOnDestroy(): void {
    this.destroy$.next(null);
    this.destroy$.complete();
  }

  ngOnInit() {
    this.getVariableData();
  }

  handleOnSearch() {
    if (!this.searchValue) {
      for (const item of this.dataArray) {
        this[item.key] = cloneDeep(this[item.originKey]);
      }
      return;
    } else {
      for (const item of this.dataArray) {
        this[item.key] = this.filterData(cloneDeep(this[item.originKey]), this.searchValue.trim());
      }
    }
  }

  handleClearSearch() {
    this.searchValue = '';
    this.handleOnSearch();
  }

  filterData(data: any[], searchValue: string) {
    return data.filter((item) => item.varName.includes(searchValue));
  }

  /**
   * customVariables = 自定义变量
   * businessObjectVariables = 业务对象变量
   * systemVariable = 系统变量
   */
  getVariableData() {
    const {
      originalFlowData: { code },
      propertiesObj,
    } = this.viewStoreService.state;
    for (const item of this.dataArray) {
      this[item.key] = cloneDeep(propertiesObj?.[code]?.[item.key] || []);
      this[item.originKey] = cloneDeep(this[item.key]);
    }
  }

  /**
   * 处理编辑变量
   */
  handleEdit(data: { type: 'add' | 'delete' | 'update'; item: any; callBack: () => void }) {
    const { type, item, callBack } = data;
    let validatorError = true;
    if (type === 'add') {
      validatorError = this.variableService.handleCheckAddVariable(item);
    }
    if (!validatorError) {
      this.athMessageService.error(this.translateService.instant(`dj-变量名称重复，请重新输入`));
      return;
    }
    callBack && callBack();
    this.variableService.handleChangeVariableData(data);
  }

  /**
   * 处理变量的扩展信息
   * @param variable
   * @returns
   */
  handleExtendDataChange(variable) {
    // variableExtendData存在当前变量
    if (this.variableExtendData.hasOwnProperty(variable.varName)) {
      this.variableExtendData[variable.varName].showMore = !this.variableExtendData?.[variable.varName]?.showMore;
      return;
    }

    //展开的变量里不存在当前节点 || 存在当前节点 但是nodes没数据
    if (
      !this.variableExtendData.hasOwnProperty(variable.varName) ||
      !this.variableExtendData[variable.varName].nodes ||
      this.variableExtendData[variable.varName].nodes.length === 0
    ) {
      this.globalVariableService
        .queryFieldsGroup({
          serviceCode: variable.serviceCode,
          modelCode: variable.modeCode || variable.modelCode,
          isHasChildren: true,
          isModel: true,
        })
        .subscribe((res: any) => {
          if (res.code === 0) {
            const nodes = [];
            const language = this.languageService?.currentLanguage || 'zh_CN';
            res.data?.modelVariable?.forEach((item) => {
              const { lang, modelFileInfos } = item;
              nodes.push({
                title: `模型字段(${lang?.comment?.[language]})`,
                expanded: true,
                children: [
                  {
                    title: `${lang?.comment?.[language]}`,
                    expanded: true,
                    children: modelFileInfos,
                  },
                ],
              });
            });
            this.variableExtendData[variable.varName] = {
              nodes,
              showMore: true,
            };
          }
        });
    }
  }

  handleClosePanel(): void {
    this.close.emit();
  }
}
