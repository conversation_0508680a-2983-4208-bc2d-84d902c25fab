<form nz-form class="global-content" [formGroup]="dataFormGroup" [nzNoColon]="true">
  <nz-collapse [nzBordered]="false">
    <!--基本信息的面板-->
    <nz-collapse-panel
      style="padding-bottom: 6px"
      [nzHeader]="'dj-基本信息' | translate"
      [nzActive]="true"
      [nzExpandedIcon]="'caret-right'"
    >
      <nz-form-item class="nz-form-item-content">
        <nz-form-control [nzAutoTips]="errorTips">
          <!--节点id-->
          <div class="form-item">
            <div class="item-title">
              {{ 'dj-业务流ID' | translate }}
              <span class="item-required">*</span>
            </div>
            <app-component-input
              [attr]="{
                name: '业务流ID',
                required: true,
                needLang: false,
                readOnly: true
              }"
              style="width: 100%"
              formControlName="processId"
              [value]="dataFormGroup.get('processId')?.value"
              ngDefaultControl
            >
            </app-component-input>
          </div>
        </nz-form-control>
      </nz-form-item>

      <nz-form-item class="nz-form-item-content">
        <nz-form-control [nzAutoTips]="errorTips">
          <!--节点名称-->
          <div class="form-item">
            <div class="item-title">
              {{ 'dj-业务流名称' | translate }}
              <span class="item-required">*</span>
            </div>
            <app-modal-input
              formControlName="processName"
              ngDefaultControl
              [innerLabel]="false"
              [attr]="{
                name: '业务流名称',
                required: true,
                needLang: true,
                lang: lang?.processName
              }"
              style="width: 100%"
              [value]="dataFormGroup.get('processName')?.value"
              (callBack)="handlePatchLang('processName', $event)"
            >
            </app-modal-input>
          </div>
        </nz-form-control>
      </nz-form-item>
    </nz-collapse-panel>
    <!--项目设置-->
    <nz-collapse-panel
      style="padding-bottom: 6px"
      [nzHeader]="'dj-项目设置' | translate"
      [nzActive]="true"
      [nzExpandedIcon]="'caret-right'"
    >
      <nz-form-item class="nz-form-item-content" formGroupName="processConfig">
        <nz-form-control [nzAutoTips]="errorTips">
          <!--项目卡标题-->
          <div class="form-item">
            <div class="item-title">
              {{ 'dj-项目卡标题' | translate }}
              <span class="item-required">*</span>
              <i
                adIcon
                iconfont="iconshuomingwenzi"
                aria-hidden="true"
                class="question-icon"
                NzTooltipTrigger="hover"
                nz-tooltip
                [nzTooltipTitle]="
                  'dj-工作流实例（项目）的展示标题，支持变量传参数。配置后将展示为项目卡标题' | translate
                "
              >
              </i>
            </div>
            <app-modal-input
              formControlName="subjectRule"
              ngDefaultControl
              [innerLabel]="false"
              [attr]="{
                name: '项目卡标题',
                required: true,
                needLang: true,
                lang: lang?.subjectRule
              }"
              style="width: 100%"
              [value]="dataFormGroup.get('processConfig').get('subjectRule').value"
              (callBack)="handlePatchLang('subjectRule', $event)"
            >
            </app-modal-input>
          </div>
        </nz-form-control>
      </nz-form-item>
      <nz-form-item class="nz-form-item-content" formGroupName="processConfig">
        <nz-form-control [nzAutoTips]="errorTips">
          <!--来源名称-->
          <div class="form-item">
            <div class="item-title">
              {{ 'dj-来源名称' | translate }}
              <span class="item-required">*</span>
              <i
                adIcon
                iconfont="iconshuomingwenzi"
                aria-hidden="true"
                class="question-icon"
                NzTooltipTrigger="hover"
                nz-tooltip
                [nzTooltipTitle]="'dj-该配置将展示为项目卡（数据来源名称）字段' | translate"
              >
              </i>
            </div>
            <app-modal-input
              formControlName="sourceName"
              ngDefaultControl
              [innerLabel]="false"
              [attr]="{
                name: '来源名称',
                required: true,
                needLang: true,
                lang: lang?.sourceName
              }"
              style="width: 100%"
              [value]="dataFormGroup.get('processConfig').get('sourceName').value"
              (callBack)="handlePatchLang('sourceName', $event)"
            >
            </app-modal-input>
          </div>
        </nz-form-control>
      </nz-form-item>
      <nz-form-item class="nz-form-item-content" formGroupName="processConfig">
        <nz-form-control [nzAutoTips]="errorTips">
          <!--项目描述-->
          <div class="form-item">
            <div class="item-title">
              {{ 'dj-项目描述' | translate }}
            </div>
            <app-modal-input
              formControlName="projectDescription"
              ngDefaultControl
              [innerLabel]="false"
              [attr]="{
                name: '项目描述',
                required: true,
                needLang: true,
                lang: lang?.projectDescription
              }"
              style="width: 100%"
              [value]="dataFormGroup.get('processConfig').get('projectDescription').value"
              (callBack)="handlePatchLang('projectDescription', $event)"
            >
            </app-modal-input>
          </div>
        </nz-form-control>
      </nz-form-item>

      <nz-form-item class="nz-form-item-content">
        <nz-form-control [nzAutoTips]="errorTips">
          <!--项目当责者-->
          <div class="form-item">
            <div class="item-title">
              {{ 'dj-项目当责者' | translate }}
              <i
                adIcon
                iconfont="iconshuomingwenzi"
                aria-hidden="true"
                class="question-icon"
                NzTooltipTrigger="hover"
                nz-tooltip
                [nzTooltipTitle]="'dj-项目当责者为项目卡的收卡人，可通过项目卡进行执行情况追踪' | translate"
              >
              </i>
            </div>
          </div>
          <ng-container *ngTemplateOutlet="personalSetting"></ng-container>
        </nz-form-control>
      </nz-form-item>
    </nz-collapse-panel>

    <!--高级设置-->
    <nz-collapse-panel
      style="padding-bottom: 6px"
      [nzHeader]="'dj-高级设置' | translate"
      [nzActive]="true"
      [nzExpandedIcon]="'caret-right'"
    >
      <!-- 卡面设置-->
      <nz-form-item class="nz-form-item-content" formGroupName="processConfig">
        <nz-form-control [nzAutoTips]="errorTips">
          <div class="form-item switch-form-item">
            <div class="item-title switch-item-title">
              {{ 'dj-开启自定义卡面' | translate }}
              <i
                adIcon
                iconfont="iconshuomingwenzi"
                aria-hidden="true"
                class="question-icon"
                NzTooltipTrigger="hover"
                nz-tooltip
                [nzTooltipTitle]="'dj-开启后，将优先展示此处配置的项目卡卡面' | translate"
              >
              </i>
            </div>
            <nz-switch
              class="custom-project-card-switch"
              formControlName="enableCustomProjectCard"
              nzSize="small"
            ></nz-switch>
            <span
              (click)="handleEdit()"
              class="edit-icon"
              *ngIf="
                dataFormGroup.get('processConfig').get('enableCustomProjectCard')?.value === true ||
                dataFormGroup.get('processConfig').get('enableCustomProjectCard')?.value === 'true'
              "
            >
              {{ 'dj-编辑' | translate }}
            </span>
          </div>
        </nz-form-control>
      </nz-form-item>
      <nz-form-item class="nz-form-item-content">
        <nz-form-control [nzAutoTips]="errorTips">
          <div class="form-item switch-form-item">
            <div class="item-title switch-item-title">
              {{ 'dj-开启合并配置' | translate }}
              <i
                adIcon
                iconfont="iconshuomingwenzi"
                aria-hidden="true"
                class="question-icon"
                NzTooltipTrigger="hover"
                nz-tooltip
                [nzTooltipTitle]="'dj-开启项目卡合并，支持根据时距进行设置' | translate"
              >
              </i>
            </div>
            <nz-switch
              class="custom-project-card-switch"
              formControlName="merge"
              (ngModelChange)="handleChangeMerge($event)"
              nzSize="small"
            ></nz-switch>
          </div>
        </nz-form-control>
      </nz-form-item>

      <!-- 隐藏项目卡 -->
      <nz-form-item class="nz-form-item-content">
        <nz-form-control [nzAutoTips]="errorTips">
          <div class="form-item switch-form-item">
            <div class="item-title switch-item-title">
              {{ 'dj-隐藏项目卡' | translate }}
              <i
                adIcon
                iconfont="iconshuomingwenzi"
                aria-hidden="true"
                class="question-icon"
                NzTooltipTrigger="hover"
                nz-tooltip
                [nzTooltipTitle]="'dj-开启选项后，运行时将不展示项目卡' | translate"
              >
              </i>
            </div>
            <nz-switch class="custom-project-card-switch" formControlName="daemon" nzSize="small"></nz-switch>
          </div>
        </nz-form-control>
      </nz-form-item>

      <ng-container *ngIf="dataFormGroup.get('merge')?.value">
        <!-- 项目时距 -->
        <nz-form-item class="nz-form-item-content" formGroupName="dueDateTimeDistance">
          <nz-form-control>
            <div class="item-title">
              {{ 'dj-项目时距' | translate }}
            </div>
            <app-modal-select
              [attr]="{ options: dateType, name: '项目时距' }"
              [showLabel]="false"
              [value]="dataFormGroup.get('dueDateTimeDistance')?.value?.type"
              (callBack)="handlePatch('type', $event)"
              formControlName="type"
              ngDefaultControl
            >
            </app-modal-select>
          </nz-form-control>
        </nz-form-item>

        <nz-form-item
          style="margin-bottom: 2px"
          formGroupName="dueDateTimeDistance"
          *ngIf="dataFormGroup?.get('dueDateTimeDistance')?.get('type')?.value === 'MULTI_DAY'"
        >
          <nz-form-control>
            <div class="item-title">
              {{ 'dj-起始日期' | translate }}
            </div>
            <ad-date-picker
              ngDefaultControl
              formControlName="startDate"
              nzFormat="yyyy-MM-dd"
              style="width: 100%"
              [nzPlaceHolder]="'dj-起始日期' | translate"
            >
            </ad-date-picker>
          </nz-form-control>
        </nz-form-item>

        <nz-form-item
          formGroupName="dueDateTimeDistance"
          *ngIf="dataFormGroup?.get('dueDateTimeDistance')?.get('type')?.value === 'MULTI_DAY'"
        >
          <nz-form-control>
            <div class="item-title">
              {{ 'dj-天数间隔' | translate }}
            </div>
            <input nz-input formControlName="count" [placeholder]="'dj-请输入' | translate" />
          </nz-form-control>
        </nz-form-item>
      </ng-container>

      <!-- 拓展信息配置 -->
      <div class="form-item">
        <div class="item-title">
          {{ 'dj-扩展信息配置' | translate }}
          <i
            adIcon
            iconfont="iconshuomingwenzi"
            aria-hidden="true"
            class="question-icon"
            NzTooltipTrigger="hover"
            nz-tooltip
            [nzTooltipTitle]="'dj-针对项目卡扩展信息进行配置， 如项目卡合并规则等' | translate"
          >
          </i>
        </div>
        <span class="extend-setting-icon" (click)="handleExtendedInfo($event)">
          <i adIcon iconfont="iconsheding" aria-hidden="true"></i>
          {{ 'dj-配置' | translate }}
        </span>
      </div>
    </nz-collapse-panel>

    <!--到期策略-->
    <nz-collapse-panel
      style="padding-bottom: 6px"
      [nzHeader]="'dj-到期策略' | translate"
      [nzActive]="true"
      [nzExpandedIcon]="'caret-right'"
    >
      <nz-form-item class="nz-form-item-content" formGroupName="planEndTime">
        <nz-form-control [nzErrorTip]="translate.instant('dj-请输入完成流程的最晚期限设定')">
          <div class="form-item">
            <div class="item-title">
              {{ 'dj-完成项目的最晚期限设定' | translate }}
              <span class="item-required">*</span>
              <i
                adIcon
                iconfont="iconshuomingwenzi"
                aria-hidden="true"
                class="question-icon"
                NzTooltipTrigger="hover"
                nz-tooltip
                [nzTooltipTitle]="'dj-不启用到期则默认为发卡当日到期' | translate"
              >
              </i>
            </div>

            <nz-radio-group formControlName="settingType" [required]="true">
              <label nz-radio nzValue="no">{{ 'dj-不启用' | translate }}</label>
              <label nz-radio nzValue="calculation">{{ 'dj-计算到期日' | translate }}</label>
              <label nz-radio nzValue="specify">{{ 'dj-指定到期日' | translate }}</label>
            </nz-radio-group>
          </div>
        </nz-form-control>
      </nz-form-item>

      <div
        class="form-item-time form-item"
        *ngIf="dataFormGroup.get('planEndTime').get('settingType')?.value === 'calculation'"
      >
        <div class="item-title">
          {{ 'dj-到期日设置' | translate }}
          <span class="item-required">*</span>
        </div>
        <div class="flex-form-item">
          <span style="margin-right: 3px"> {{ 'dj-启动流程后' | translate }}</span>
          <nz-form-item class="nz-form-item-content" formGroupName="planEndTime">
            <nz-form-control [nzErrorTip]="translate.instant('dj-请输入数值')">
              <div class="flex-form-item multi-form-item">
                <nz-input-group nzCompact style="display: flex">
                  <ad-input-number
                    formControlName="dateValue"
                    [ngModelOptions]="{ standalone: true }"
                    [min]="1"
                    [step]="1"
                    [max]="9999"
                    [precision]="0"
                    precisionMode="cut"
                    width="90px"
                  >
                  </ad-input-number>
                  <ad-select
                    class="time-type-select"
                    formControlName="dateType"
                    [ngModelOptions]="{ standalone: true }"
                    style="width: 70px"
                    [nzPlaceHolder]="'dj-请选择' | translate"
                    nzAllowClear="false"
                  >
                    <ad-option [nzLabel]="'dj-分钟' | translate" [nzValue]="'minute'"></ad-option>
                    <ad-option [nzLabel]="'dj-小时' | translate" [nzValue]="'hour'"></ad-option>
                    <ad-option [nzLabel]="'dj-天' | translate" [nzValue]="'day'"></ad-option>
                    <ad-option [nzLabel]="'dj-周' | translate" [nzValue]="'week'"></ad-option>
                  </ad-select>
                </nz-input-group>
              </div>
            </nz-form-control>
          </nz-form-item>

          <span style="margin-left: 3px">{{ 'dj-到期' | translate }}</span>
        </div>
      </div>

      <div
        class="form-item-time form-item"
        *ngIf="dataFormGroup.get('planEndTime').get('settingType')?.value === 'specify'"
      >
        <div class="flex-form-item">
          <nz-form-item class="nz-form-item-content form-item-planEndTime" formGroupName="planEndTime">
            <nz-form-control
              [nzErrorTip]="'dj-请输入' | translate"
              *ngIf="dataFormGroup.get('planEndTime')?.get('sourceType')?.value == 'constant'"
            >
              <nz-input-group [nzAddOnBefore]="addOnBeforeTemplate">
                <ad-date-picker
                  nzFormat="yyyy/MM/dd HH:mm:ss"
                  [nzPlaceHolder]="'dj-请选择' | translate"
                  [nzShowToday]="false"
                  [nzShowTime]="true"
                  [nzAllowClear]="true"
                  class="end-type-picker"
                  formControlName="sourceValue"
                  style="width: 220px; min-width: 220px"
                ></ad-date-picker>
              </nz-input-group>
            </nz-form-control>
            <nz-form-control
              [nzErrorTip]="'dj-请输入' | translate"
              *ngIf="dataFormGroup.get('planEndTime')?.get('sourceType')?.value == 'variable'"
            >
              <nz-input-group [nzAddOnBefore]="addOnBeforeTemplate">
                <app-variable-select-input
                  *ngIf="dataFormGroup.get('planEndTime')?.get('sourceType')?.value == 'variable'"
                  style="display: block; width: 220px; min-width: 220px"
                  [inputable]="true"
                  [variableRange]="variableRange"
                  [nodeId]="data.id"
                  formControlName="variableCode"
                  [item]="item"
                  [sufixKey]="'variableSuffix'"
                  [maxWidth]="220"
                  (onChanged)="handleValueChanged($event)"
                >
                </app-variable-select-input>
              </nz-input-group>
            </nz-form-control>
          </nz-form-item>
        </div>
      </div>
    </nz-collapse-panel>
  </nz-collapse>
</form>

<!-- 项目界面设计器 -->
<app-task-project-card-design
  *ngIf="workVisible"
  type="project"
  [workVisible]="workVisible"
  [workData]="workData"
  [uiKey]="uiKey"
  [favouriteCode]="dataSourceService.favouriteCode"
  [applicationCodeProxy]="dataSourceService.applicationCodeProxy"
  [extendHeader]="service.combineHeaders"
  [isTenantProcessId]="isTenantProcessId"
  [hiddenPublishAndPreviewButton]="true"
  (close)="handleWorkPageClose($event)"
></app-task-project-card-design>

<!-- 扩展信息配置 -->
<app-extended-info
  #extendedInfoRef
  class="icon"
  style="position: absolute; left: -99999px"
  [code]="data.processId"
  [showName]="true"
  [sceneCode]="'datamapProject'"
  [appCode]="appService.selectedApp.code"
  [isTenantProcessId]="isTenantProcessId"
  [extendHeader]="service.combineHeaders"
  [showName]="false"
  [notShowIcon]="false"
></app-extended-info>

<ng-template #addOnBeforeTemplate>
  <span class="field-paramType" nz-button nz-dropdown [nzDropdownMenu]="paramTypeDropDown">
    <i
      adIcon
      [iconfont]="fieldType[dataFormGroup.get('planEndTime')?.get('sourceType')?.value || 'constant']?.icon"
    ></i>
    <span style="margin-left: 4px" adIcon type="down" theme="outline"></span>
  </span>
  <nz-dropdown-menu #paramTypeDropDown="nzDropdownMenu">
    <ul nz-menu>
      <li
        nz-menu-item
        [ngClass]="{
          'field-menu-item': true,
          'field-menu-item-active': field === dataFormGroup.get('planEndTime')?.get('sourceType')?.value
        }"
        *ngFor="let field of fieldTypeEum"
        (click)="changeParamType(field)"
      >
        <i adIcon [iconfont]="fieldType[field].icon"></i
        ><span class="field-name" style="margin-left: 4px">{{ fieldType[field].name }}</span>
      </li>
    </ul>
  </nz-dropdown-menu>
</ng-template>

<ng-template #personalSetting>
  <app-people-setting-property
    [showDefault]="true"
    [personSingle]="true"
    personType="''"
    [bindForm]="bindForm"
    [nodeId]="undefined"
    [executor]="personInCharge"
    [decisionConfig]="{}"
    [preManualNodes]="[]"
    (changeData)="handlePeopleSettingChange($event)"
  ></app-people-setting-property>
</ng-template>
