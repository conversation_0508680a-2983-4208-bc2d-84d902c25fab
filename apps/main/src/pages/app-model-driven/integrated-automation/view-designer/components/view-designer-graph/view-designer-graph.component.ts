import { AfterViewInit, Component, ElementRef, Input, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { takeUntil } from 'rxjs/operators';
import { Subject } from 'rxjs';
import { ViewGraphService } from '../../service/graph.service';
import { ViewStoreService } from '../../service/store.service';
import { ViewToolsService } from '../../service/tools.service';
import { ViewApiService } from '../../service/api.service';
import { registerTools } from '../../config/graph';
import { NodeType, IProperties, TriggerType, ModelNodes, EType } from '../../config/typings';
import { initIsNodePaas } from '../../config/init-graph';

import { isEmpty } from 'lodash';
import { processData } from 'pages/app-model-driven/utils/utils';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-view-designer-graph',
  templateUrl: './view-designer-graph.component.html',
  styleUrls: ['./view-designer-graph.component.less'],
})
export class ViewDesignerGraphComponent implements OnInit, AfterViewInit, OnDestroy {
  @Input() graphWrapper;
  @ViewChild('graphContainer', { static: true }) container: ElementRef;

  destroy$ = new Subject();

  currentSelectedNodeProperties: any = null;
  queryNodeId: string = null; // 从路由参数中获取的 nodeId

  get state() {
    return this.viewStoreService.state;
  }

  constructor(
    private activatedRoute: ActivatedRoute,
    private viewGraphService: ViewGraphService,
    private viewStoreService: ViewStoreService,
    private viewToolsService: ViewToolsService,
    private viewApiService: ViewApiService,
    private t: TranslateService,
  ) {
    this.viewGraphService.isShowPropertiesPanel$.pipe(takeUntil(this.destroy$)).subscribe((res: any) => {
      const { isShow, propertiesPanel } = res || {};
      this.currentSelectedNodeProperties = isShow ? propertiesPanel : null;
    });
    registerTools();
  }

  ngOnInit() {
    // 初始化画布
    this.init();
  }

  ngAfterViewInit(): void {
    this.activatedRoute.queryParams.pipe(takeUntil(this.destroy$)).subscribe((params: any) => {
      const { nodeId } = params;
      this.queryNodeId = nodeId;
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();

    this.viewGraphService.dispose();
  }

  init() {
    this.viewGraphService.graphWrapper = this.graphWrapper;
    const t = setTimeout(() => {
      clearTimeout(t);
      // 初始化画布
      this.viewGraphService.initGraph(this.container.nativeElement);
      // 注册其他事件
      this.registerEvents();
      // 初始化获取流程信息
      this.getFlowData();
    });
  }

  // 注册节点事件
  registerEvents() {
    // 空白区域单击
    this.viewGraphService.graph.on('blank:click', () => {
      // 清空
      this.viewGraphService.currentTransmitData = null;
      // 关闭浮层节点菜单
      this.viewGraphService.handleCloseNodeMenu();
      // 通知关闭属性面板
      this.viewGraphService.isShowPropertiesPanel$.next({
        isShow: false,
        propertiesPanel: null,
      });
      // 清除所有节点的高亮和工具
      this.viewToolsService.toolsClearAllNodesTools(this.viewGraphService.graph);
      // 清除所有线的高亮和工具
      this.viewToolsService.toolsClearAllEdgesTools(this.viewGraphService.graph);
    });

    // 空白区域双击
    this.viewGraphService.graph.on('blank:dblclick', ({ e }) => {
      // 已执行【空白区域单击】事件中逻辑
      // 打开浮层节点菜单
      this.viewGraphService.handleOpenNodeMenu(e);
    });

    // 进入节点
    this.viewGraphService.graph.on('node:mouseenter', ({ node }) => {
      const { isSelected } = node?.data || {};

      // 当前流程为引用流程，不处理后续逻辑
      if (this.state.isFromDtdReference) return;
      // 当前节点已选中，不处理后续逻辑
      if (isSelected) return;

      // 设置节点样式
      this.viewToolsService.toolsNodeStyle(node, 'highlight');
      // 链接桩可见
      this.viewGraphService.setNodePortsVisible(node, true);
      // 设置复制、删除按钮可见
      this.viewGraphService.setNodeTool(node, true);
    });

    // 离开节点
    this.viewGraphService.graph.on('node:mouseleave', ({ node }) => {
      const { isSelected } = node?.data || {};

      // 当前节点已选中，不处理后续逻辑
      if (isSelected) return;

      // 设置节点样式
      this.viewToolsService.toolsNodeStyle(node, 'none');
      // 链接桩不可见
      this.viewGraphService.setNodePortsVisible(node, false);
      // 设置复制、删除按钮不可见
      this.viewGraphService.setNodeTool(node, false);
    });

    // 选中节点
    this.viewGraphService.graph.on('node:selected', ({ node }) => {
      this.handleNodeSelected(node);
    });

    // 失去选中节点
    this.viewGraphService.graph.on('node:unselected', ({ node }) => {
      this.handleNodeUnSelected(node);
    });

    // 单击节点
    this.viewGraphService.graph.on('node:click', ({ node }) => {
      const { nodeId, nodeType, conditionEndId, conditionType, parallelEndId, parallelType } = node?.data || {};

      this.viewStoreService.setState({
        // 当前选中节点的id
        currentSelectedNodeId: nodeId,
      });
      switch (nodeType) {
        // case NodeType.CONDITION_BRANCH_START:
        //   const { isFromBranchButtonTool } = node?.data || {};
        //   // 如果不是从分支按钮工具点击的，则不处理
        //   if (isFromBranchButtonTool) {
        //     // 新增条件分支节点
        //     const cell = this.viewGraphService.graph.getCellById(conditionEndId);
        //     this.viewGraphService.addConditionBranchNode(node, cell, 'common');
        //     // 处理条件分支节点位置
        //     this.viewGraphService.positionConditionBranch(node, 'fromStart');
        //     break;
        //   }

        // case NodeType.PARALLEL_BRANCH_START:
        //   const cellIns = this.viewGraphService.graph.getCellById(parallelEndId);
        //   this.viewGraphService.addParallelBranchNode(node, cellIns, 'common');
        //   this.viewGraphService.positionParallelBranch(node, 'fromStart');
        // case NodeType.PARALLEL_BRANCH_END:
        // case NodeType.CONDITION_BRANCH_END:
        //   // 不处理
        //   break;
        case NodeType.CONDITION_BRANCH:
          // 获取所有前序人工节点绑定的模型
          const preManualNodesMode = this.viewGraphService.getAllPredecessorsNodesMode(node);
          // 获取当前条件节点的优先级
          const conditionNodesLevel = this.viewGraphService.getConditionNodesLevel(node);
          // 通知打开属性面板
          const currentPropertiesObj: IProperties = {
            nodeType: nodeType,
            nodeId: nodeId,
            conditionType: conditionType,
            conditionNodesLevel: conditionNodesLevel,
            preManualNodesMode: preManualNodesMode,
          };
          this.viewGraphService.isShowPropertiesPanel$.next({
            isShow: true,
            propertiesPanel: currentPropertiesObj,
          });
          break;
        case NodeType.PARALLEL_BRANCH:
          const paraPreManualNodesMode = this.viewGraphService.getAllPredecessorsNodesMode(node);
          const parallelNodesLevel = this.viewGraphService.getParallelBranchNodeExcludeDefault(node);
          const currentParallelPropertiesObj: IProperties = {
            nodeType: nodeType,
            nodeId: nodeId,
            parallelType,
            conditionNodesLevel: parallelNodesLevel,
            preManualNodesMode: paraPreManualNodesMode,
          };
          this.viewGraphService.isShowPropertiesPanel$.next({
            isShow: true,
            propertiesPanel: currentParallelPropertiesObj,
          });
          break;
        case NodeType.MANUAL_APPROVE:
        case NodeType.MANUAL_EXECUTION:
        case NodeType.MANUAL_NOTIFICATION:
          // 获取所有前序人工节点
          const preManualNodes = this.viewGraphService.getAllPredecessorsNodes(node);
          // 通知打开属性面板
          const currentManualPropertiesObj: IProperties = {
            nodeType: nodeType,
            nodeId: nodeId,
            preManualNodes: preManualNodes,
          };
          this.viewGraphService.isShowPropertiesPanel$.next({
            isShow: true,
            propertiesPanel: currentManualPropertiesObj,
          });
          break;
        case NodeType.DATA_ADD:
        case NodeType.DATA_UPDATE:
          const preModelNodes = this.viewGraphService.getFlowPartHasModelNodes({
            type: 'pre',
            node,
          });
          // 通知打开属性面板
          const currentDataAddPropertiesObj: IProperties = {
            nodeType: nodeType,
            nodeId: nodeId,
            preModelNodes,
          };
          this.viewGraphService.isShowPropertiesPanel$.next({
            isShow: true,
            propertiesPanel: currentDataAddPropertiesObj,
          });
          break;
        case NodeType.DATA_GET:
        case NodeType.DATA_GET_MULTI:
        case NodeType.DATA_DELETE:
        case NodeType.DATA_GROUPING:
          const preGetNodesMode = this.viewGraphService.getPreGetDataNodesMode(node);
          // 通知打开属性面板
          const currentDataGetPropertiesObj: IProperties = {
            nodeType: nodeType,
            nodeId: nodeId,
            preGetNodesMode,
          };
          this.viewGraphService.isShowPropertiesPanel$.next({
            isShow: true,
            propertiesPanel: currentDataGetPropertiesObj,
          });
          break;
        default:
          // 通知打开属性面板
          const currentDefaultPropertiesObj: IProperties = {
            nodeType: nodeType,
            nodeId: nodeId,
          };
          this.viewGraphService.isShowPropertiesPanel$.next({
            isShow: true,
            propertiesPanel: currentDefaultPropertiesObj,
          });
      }
    });

    // 双击节点
    this.viewGraphService.graph.on('node:dblclick', ({ node }) => {});

    // 节点删除回调
    this.viewGraphService.graph.on('node:removed', ({ node }) => {
      const { nodeId } = node?.data || {};

      // 通知关闭当前节点的属性面板
      if (this.currentSelectedNodeProperties?.nodeId === nodeId) {
        this.viewGraphService.isShowPropertiesPanel$.next({
          isShow: false,
          propertiesPanel: null,
        });
      }
    });

    // 连接线
    this.viewGraphService.graph.on('edge:connected', ({ edge }) => {
      const source = edge.getSourceCell();
      const target = edge.getTargetCell();
      if (source?.data?.isSelected || target?.data?.isSelected) {
        // 连线来源、目标节点选中状态，边显示添加节点工具并高亮加粗样式
        this.viewGraphService.edgeAddButtonTool(edge);
        this.viewToolsService.toolsEdgeStyle(edge, true);
      } else {
        // 链接桩不可见
        this.viewGraphService.setNodePortsVisible(source, false);
        this.viewGraphService.setNodePortsVisible(target, false);
        // 边隐藏添加节点工具并恢复默认样式
        edge.removeTool('edge-add-node-button');
        this.viewToolsService.toolsEdgeStyle(edge, false);
      }
    });

    // 进入线
    this.viewGraphService.graph.on('edge:mouseenter', ({ edge }) => {
      // 如果是引用的dtd，则不显示工具
      if (this.state.isFromDtdReference) return;

      const source = edge.getSourceCell();
      const target = edge.getTargetCell();
      if (
        (source?.data?.conditionStartId &&
          source?.data?.conditionEndId &&
          target?.data?.conditionStartId &&
          target?.data?.conditionEndId) ||
        this.checkIsBlongParallelNode(source, target)
      ) {
        // 连线添加工具禁止删除按钮
        this.viewToolsService.toolsEdgeDisabledRemoveButton(edge);
      } else {
        // 连线添加工具删除按钮
        this.viewGraphService.edgeRemoveButtonTool(edge);
      }
      // 边显示添加节点工具并高亮加粗样式
      this.viewGraphService.edgeAddButtonTool(edge);
      this.viewToolsService.toolsEdgeStyle(edge, true);
    });

    // 离开线
    this.viewGraphService.graph.on('edge:mouseleave', ({ edge }) => {
      // 移除删除线的按钮
      edge.removeTool('button-remove');
      edge.removeTool('edge-disabled-button-remove');

      const source = edge.getSourceCell();
      const target = edge.getTargetCell();
      // 连线来源、目标节点选中状态，边高亮加粗样式
      if (!source?.data?.isSelected && !target?.data?.isSelected) {
        // 边隐藏添加节点工具并恢复默认样式
        edge.removeTool('edge-add-node-button');
        this.viewToolsService.toolsEdgeStyle(edge, false);
      }
    });

    // 删除线
    this.viewGraphService.graph.on('edge:removed', ({ edge }: any) => {});

    // // 撤销画布
    // this.viewGraphService.graph.on('history:undo', ({ cmds }: any) => {
    //   this.viewToolsService.toolsHistoryUndoUpdateProperties(cmds);
    // });

    // // 重做画布
    // this.viewGraphService.graph.on('history:redo', ({ cmds }: any) => {
    //   this.viewToolsService.toolsHistoryRedoUpdateProperties(cmds);
    // });
  }

  handleNodeSelected(node): void {
    if (this.queryNodeId) {
      const id = this.queryNodeId.split('_')[1];
      if (node.id !== id) {
        const unSelectedNode: any = this.viewGraphService.graph.getCellById(id);
        this.handleNodeUnSelected(unSelectedNode);
        this.queryNodeId = null;
      }
    }

    // 设置节点选中状态
    node.setData({ ...(node?.data || {}), isSelected: true });
    // 设置节点样式
    // 非引用的流程可执行的操作
    if (!this.state.isFromDtdReference) {
      this.viewToolsService.toolsNodeStyle(node, 'highlight');
      // 获取节点相连的边，设置边高亮加粗样式
      const edges = this.viewGraphService.graph.getConnectedEdges(node);
      edges?.forEach((edge) => {
        // 边显示添加节点工具并高亮加粗样式
        this.viewGraphService.edgeAddButtonTool(edge);
        this.viewToolsService.toolsEdgeStyle(edge, true);
      });
      // 添加节点周边 + 图标
      this.viewGraphService.addNodeAddTool(node);
      // 链接桩不可见
      this.viewGraphService.setNodePortsVisible(node, false);
      // 设置复制、删除按钮不可见
      this.viewGraphService.setNodeTool(node, false);
    }
  }

  handleNodeUnSelected(node): void {
    // 设置节点未选中状态
    node.setData({ isSelected: false });
    // 设置节点样式
    this.viewToolsService.toolsNodeStyle(node, 'none');
    // 获取节点相连的边，设置边恢复默认样式
    const edges = this.viewGraphService.graph.getConnectedEdges(node);
    edges.forEach((edge) => {
      // 边隐藏添加节点工具并恢复默认样式
      edge.removeTool('edge-add-node-button');
      this.viewToolsService.toolsEdgeStyle(edge, false);
    });
    // 移除节点周边 + 图标
    this.viewGraphService.removeNodeAddTool(node);
    // 通知关闭属性面板
    this.viewGraphService.isShowPropertiesPanel$.next({
      isShow: false,
      propertiesPanel: null,
    });
  }

  // 校验是否属于并行分支节点
  checkIsBlongParallelNode(source, target) {
    return (
      source?.data?.parallelStartId &&
      source?.data?.parallelEndId &&
      target?.data?.parallelStartId &&
      target?.data?.parallelEndId
    );
  }

  /**
   * 获取整个流程的模型变量
   * @param data
   */
  async handleModelData(data: any) {
    const { bindForm = {}, processConfig } = data || {};
    const modelNodes = (processConfig?.nodes || [])
      .filter((e) => ModelNodes.includes(e._nodeType))
      .reduce((pre, cur) => {
        const { bindForm = {} } = cur;
        if (bindForm.modelCode && bindForm.serviceCode) {
          pre.push({
            unique: bindForm.modelCode + bindForm.serviceCode,
            ...bindForm,
          });
        }
        return pre;
      }, []);
    const globalSettingNode =
      !isEmpty(bindForm) && bindForm.modelCode
        ? [{ ...bindForm, unique: bindForm.modelCode + bindForm.serviceCode }]
        : [];
    const modalList = [...globalSettingNode, ...modelNodes];
    const { data: allModelList = [] } = (await this.viewApiService.getModelDesignList().toPromise()) as any;
    data.allModelList = allModelList;
    // 当前流程中没涉及到模型变量节点
    if (modalList?.length === 0) {
      data.modelVariables = [];
      return;
    }
    const map = new Map();
    for (const item of modalList) {
      if (!map.has(item.unique)) {
        map.set(item.unique, {
          varName: allModelList?.find((e) => e.code === item.modelCode)?.name || '',
          serviceCode: item.serviceCode,
          modelCode: item.modelCode,
        });
      }
    }
    data.modelVariables = Array.from(map.values());
  }

  // 获取流程信息
  getFlowData() {
    this.viewGraphService.doNotCheckStateChange$.next(true);
    const { id, objectId } = this.activatedRoute.snapshot.params;

    const params = {
      processId: id,
    };
    let requestApi = 'getFindProcessById';

    // 有objectId 就是 租户级 流程
    if (objectId) {
      params['objectId'] = objectId;
      requestApi = 'getFindProcessByIdTenant';
    }

    this.viewGraphService.isInitLoading = true;
    this.viewApiService[requestApi](params).subscribe(
      async (res) => {
        const { code, data } = res || {};
        if (code === 0) {
          processData(data, this.t);
          await this.handleModelData(data);
          // 存储原流程
          this.viewStoreService.setState((state) => {
            let _propertiesObj = {};
            // 全局配置
            _propertiesObj[id] = {
              businessCode: data?.businessCode || '',
              processId: data?.processId || '',
              processName: data?.processName || '',
              lang: data?.lang || {},
              bindForm: data?.bindForm || {},
              daemon: data?.daemon || false,
              personInCharge: data?.personInCharge,
              processConfig: {
                sourceName: data?.processConfig?.sourceName,
                subjectRule: data?.processConfig?.subjectRule,
                enableCustomProjectCard: data?.processConfig?.enableCustomProjectCard,
                projectDescription: data?.processConfig?.projectDescription,
              },
              _isValidPassed: initIsNodePaas(NodeType.GLOBAL_SETTING),
              _nodeId: data?.processId || '',
              _nodeType: NodeType.GLOBAL_SETTING,
              dtdVariable: data?.dtdVariable || [],
              customVariables: data?.customVariables || [],
              businessObjectVariables: data?.businessObjectVariables || [],
              mechanismVariables: data?.mechanismVariables || [],
              systemVariable: data?.systemVariable || [],
              nodeVariables: data.nodeVariables,
              modelVariables: data.modelVariables,
              allModelList: data.allModelList,
              merge: data?.merge,
              // mergeFields: data?.mergeFields,
              dueDateTimeDistance: data?.dueDateTimeDistance,
              adpStatus: data?.adpStatus,
              adpVersion: data?.adpVersion,
              planEndTime: data?.planEndTime,
            };
            // 节点配置
            const { processConfig } = data || {};
            const { nodes } = processConfig || {};
            if (nodes?.length) {
              nodes.forEach((item) => {
                _propertiesObj[item._nodeId] = item;
              });
            }
            state.propertiesObj = _propertiesObj;
            state.originalFlowData = {
              ...data,
              _isValidPassed: initIsNodePaas(NodeType.GLOBAL_SETTING),
              _nodeId: data?.processId || '',
              _nodeType: NodeType.GLOBAL_SETTING,
            };
            state.modelData = {
              formCode: data.businessCode,
              modelCode: data.businessModelCode,
              serviceCode: data.businessServiceCode,
            };
          });
          if (data?.adpVersion) {
            this.viewApiService.headers = {
              adpVersion: data?.adpVersion,
              adpStatus: data?.adpStatus,
            };
          }

          // 判断是否是人工节点，展示提示
          this.viewGraphService.isShowAlert$.next(data?.triggerType === TriggerType.PAGE);
          // 清空画布
          this.viewGraphService.graph.clearCells();
          // 初始化渲染画布
          try {
            const { flowGraph } = data || {};
            const { nodes, links } = flowGraph || {};
            const initCells = [...nodes, ...links];
            this.viewToolsService.toolsGraphFromJSON(this.viewGraphService.graph, initCells);
          } catch (error) {}

          // 初始化打开属性面板
          if (this.queryNodeId) {
            const id = this.queryNodeId.split('_')[1];
            const node: any = this.viewGraphService.graph.getCellById(id);
            this.handleNodeSelected(node);
            const { propertiesObj } = this.viewStoreService.state;
            const { _nodeType, preManualNodes = [] } = propertiesObj[id];
            this.viewGraphService.isShowPropertiesPanel$.next({
              isShow: true,
              propertiesPanel: {
                nodeId: id,
                nodeType: _nodeType,
                preManualNodes,
              },
            });
          } else if (data?.processId) {
            this.viewGraphService.isShowPropertiesPanel$.next({
              isShow: true,
              propertiesPanel: {
                nodeId: data?.processId || '',
                nodeType: NodeType.GLOBAL_SETTING,
              },
            });
          }
        }
        this.viewGraphService.isInitLoading = false;
        this.viewGraphService.doNotCheckStateChange$.next(false);
      },
      () => {
        this.viewGraphService.isInitLoading = false;
        this.viewGraphService.doNotCheckStateChange$.next(false);
      },
    );
  }
}
