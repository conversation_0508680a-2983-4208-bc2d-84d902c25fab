.people-setting-property-root {
  .form-item {
    margin-bottom: 16px;

    .item-title {
      color: #333;
      font-size: 13px;
      margin-bottom: 8px;
      display: inline-block;

      .item-required {
        margin-right: 4px;
        color: #ff4d4f;
        font-size: 14px;
        font-family: SimSun, sans-serif;
        line-height: 1;
      }
    }

    .hasError {
      ::ng-deep .ant-input-affix-wrapper {
        border: 1px solid #ea3d46;
      }
    }

    .hasErrorBorder {
      border: 1px solid #ea3d46;
    }

    .error {
      color: #ff4d4f;
      font-size: 12px;
    }
  }

  .person-type-sel {
    flex: none;
    position: relative;
    background-color: #fafafa;
    padding: 5px;
    border-top: 1px solid #d9d9d9;
    border-bottom: 1px solid #d9d9d9;
    border-left: 1px solid #d9d9d9;
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
    height: 32px;
  }

  .person-sel {
    display: flex;
    flex: auto;
    ::ng-deep ad-select > ad-select-top-control {
      border-top-left-radius: 0px;
      border-bottom-left-radius: 0px;
    }
    ::ng-deep.ath-input-group-wrapper .ath-input-group-cocoon {
      height: 32px;
    }
    ::ng-deep.ath-input-group-wrapper .ant-input-affix-wrapper {
      height: 32px;
    }
    ::ng-deep.ath-input-group-wrapper .ant-input-suffix {
      padding-top: 2.5px;
    }
    ::ng-deep .input-group-wrap {
      height: 32px;
    }
    ::ng-deep .adp-select-selector {
      height: 32px !important;
    }
  }
  .people-input-require {
    ::ng-deep.ath-input-group-wrapper .ant-input-affix-wrapper {
      height: 32px;
      border-color: red !important;
    }
  }
  .person-type-options {
    width: 96px;
    cursor: pointer;
    position: absolute;
    left: 8px;
    z-index: 2;
    background: #fff;
    border: 1px solid #eeeeee;

    & > div {
      display: flex;
      align-items: center;
      margin: 8px;
    }
  }

  .question-icon {
    padding-right: 8px;
    font-size: 14px;
    cursor: pointer;
    display: inline-block;
    line-height: 17px;
    vertical-align: middle;
  }
  .people-sel-require {
    border: 1px solid red;
    ::ng-deep .adp-select-selector {
      height: 30px !important;
    }
  }
  .custom-content {
    color: #999;
  }
}
