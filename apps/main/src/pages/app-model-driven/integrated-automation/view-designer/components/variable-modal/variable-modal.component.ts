import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { DataTypeIconMap } from './utils';
import { trim } from 'lodash';
import { validatorForm } from 'common/utils/core.utils';
import { CommonValidators } from 'common/utils/common.validators';
import { VariableModalService } from './variable-modal.service';
import { ViewGraphService } from '../../service/graph.service';
import { VariableType } from '../flow-node-property/global-variable/variable-item/variable-item.component';
import { AppService } from 'pages/apps/app.service';

@Component({
  selector: 'app-variable-modal',
  templateUrl: './variable-modal.component.html',
  styleUrls: ['./variable-modal.component.less'],
})
export class VariableModalComponent implements OnInit {
  // 是不是融合模式
  @Input() isFusionMode: boolean = false;
  // 融合模式的task code
  @Input() taskCode: string = undefined;
  @Input() visible: boolean = false;
  @Input() formData: any = {};
  @Input() variableType: VariableType = '';
  @Output() visibleChange = new EventEmitter<boolean>();
  @Output() updateFormData = new EventEmitter<any>();
  dataTypeIconMap = DataTypeIconMap;
  modelList: any = []; // 业务对象变量列表

  variableLoading: boolean = false;
  variableList: any[] = []; // 融合模式下，新T可访问的变量

  // 机制参数列表
  mechanismList: any[] = [];

  private readonly defaultDataTypes = [
    {
      label: `Boolean ${this.t('dj-布尔值')}`,
      value: 'Boolean',
      description: this.t('dj-布尔值描述'),
      disabled: false,
    },
    { label: `Integer ${this.t('dj-整数')}`, value: 'Integer', description: this.t('dj-整数描述'), disabled: false },
    { label: `Decimal ${this.t('dj-小数')}`, value: 'Decimal', description: this.t('dj-小数描述'), disabled: false },
    { label: `String ${this.t('dj-文本')}`, value: 'String', description: this.t('dj-文本描述'), disabled: false },
    {
      label: `DateTime ${this.t('dj-日期时间')}`,
      value: 'DateTime',
      description: this.t('dj-日期时间描述'),
      disabled: false,
    },
    {
      label: `BusinessObject ${this.t('dj-业务对象')}`,
      value: 'BusinessObject',
      description: this.t('dj-业务对象描述2'),
      disabled: false,
    },
    {
      label: `Mechanism ${this.t('dj-机制参数')}`,
      value: 'Mechanism',
      description: this.t('dj-机制参数描述'),
      disabled: false,
    },
    {
      label: `DTDVar ${this.t('dj-DTD变量')}`,
      value: 'DTDVar',
      description: this.t('dj-DTD变量描述'),
      disabled: false,
    },
  ];

  // 数据类型：String,Integer,Decimal,Boolean,DateTime,BusinessObject
  get dataTypes(): any[] {
    if (this.isFusionMode) {
      return this.defaultDataTypes;
    }
    return this.defaultDataTypes.slice(0, this.defaultDataTypes.length - 1);
  }

  form: FormGroup;
  modalTitle: any = '';

  lastDataType: string = undefined;

  autoTips: Record<string, Record<string, string>> = {
    default: {
      required: this.translate.instant('dj-请输入'),
      pattern: this.translate.instant('dj-格式不正确'),
    },
  };

  autoTips2: Record<string, Record<string, string>> = {
    default: {
      required: this.translate.instant('dj-请选择'),
    },
  };

  get mechanismItem() {
    if (this.form?.get('dataType')?.value === 'Mechanism') {
      return this.mechanismList.find((e) => e.paramCode === this.form.get('varName').value);
    }
    return undefined;
  }

  initVariableName: string = `variable${Math.floor(Math.random() * 900) + 100}`;

  constructor(
    private fb: FormBuilder,
    private translate: TranslateService,
    private variableModalService: VariableModalService,
    private appService: AppService,
    private viewGraphService: ViewGraphService,
  ) {
    this.form = this.createForm();
    this.resetToNull();
    this.defaultValueChange();
  }

  async ngOnInit() {
    this.handleChangeValue();
    this.getModelList();
    this.handleQueryMechanism();
    if (this.isFusionMode && this.taskCode) {
      await this.handleVariablesQuery();
      const mappingCode = this.form.get('mappingCode').value;
      if (mappingCode) {
        if (!this.variableList.some((e) => e.value === mappingCode)) {
          this.form.patchValue({
            mappingCode: undefined,
          });
        }
      }
    }
  }

  // 查询融合模式下，新T可访问的变量
  async handleVariablesQuery(): Promise<void> {
    this.variableLoading = true;
    try {
      const { code, data } = await this.variableModalService.loadCurrentTaskData(this.taskCode).toPromise();
      if (code !== 0) return;
      const codeList = data.stateMaps?.map((e) => e.input) || [];
      const varibalesResult = await this.variableModalService
        .variablesQuery(this.appService?.selectedApp?.code || '', codeList)
        .toPromise();
      const variableList = [];
      const list: any = [
        {
          code: 'init_b1f78dc74c0c3c019c902e3d3375e8ca',
          name: '发起流程数据',
          lang: {
            name: {
              zh_CN: '发起流程数据',
              zh_TW: '發起流程數據',
              en_US: '发起流程数据',
            },
          },
          variables: [
            {
              dataType: 'OBJECT',
              key: 'data',
              hasArrayStruct: false,
              name: '发起流程data',
            },
          ],
        },
      ].concat([...(varibalesResult?.data?.variableList || [])]);
      list.forEach((item) => {
        item.variables?.forEach((e) => {
          variableList.push({
            label: `${e.key} (${e.dataType})`,
            value: `${item.code}@@${e.key}`,
            dataType: e.dataType,
            description: e.name,
            isArray: !!e.hasArrayStruct,
            groupLabel: item.name,
          });
        });
      });
      this.variableList = variableList;
    } finally {
      this.variableLoading = false;
    }
  }

  handleChangeValue() {
    const isDtdVariable = (this.formData?.dataType || this.variableType || 'String') === 'DTDVar';
    this.form?.patchValue({
      dataType: this.formData?.dataType || this.variableType || 'String',
      varName: this.formData?.varName || this.initVariableName,
      isArray: this.formData?.isArray,
      required: this.formData?.required,
      defaultValue: this.formData?.defaultValue,
      description: this.formData?.description,
      mappingCode: isDtdVariable
        ? this.formData?.mappingCode && this.formData?.code
          ? `${this.formData.code}@@${this.formData.mappingCode}`
          : undefined
        : undefined,
    });
    this.handleInitDisabledData();
  }

  handleDtdChanged(value: string): void {
    const variable = this.variableList.find((e) => e.value === value);
    this.form.patchValue({
      isArray: variable.isArray,
      description: variable.description,
    });
  }

  /**
   * 处理disabled数据
   */
  handleInitDisabledData() {
    this.modalTitle = this.t('dj-新增流程变量');
    // 编辑状态下，禁用变量名
    if (this.formData?.varName) {
      this.modalTitle = this.t('dj-编辑流程变量');
      this.form.get('varName').disable();
    }
    // dataType不同， dataTypes下拉不同
    this.handleDateType();
  }

  handleDateType() {
    if (!this.variableType) {
      return;
    }
    const type = ['businessObject', 'mechanism', 'DTDVar'];
    const map = {
      businessObject: 'BusinessObject',
      mechanism: 'Mechanism',
      DTDVar: 'DTDVar',
    };

    if (type.includes(this.variableType)) {
      this.dataTypes.forEach((item) => {
        if (item.value !== map[this.variableType]) {
          item.disabled = true;
        }
      });
    } else {
      this.dataTypes.forEach((item) => {
        if (Object.values(map).includes(item.value)) item.disabled = true;
      });
    }
  }

  /**
   * 获取模型list
   */
  getModelList() {
    this.variableModalService.getModelDesignList().subscribe((data: any) => {
      this.modelList = data?.data || [];
      // 新增的时候， 过滤掉已有节点的模型
      // if (!this.formData?.varName) {
      //   const nodes = this.viewGraphService.getAllNodesHasModel({
      //     type: 'all',
      //   });
      //   this.modelList = this.modelList.filter((item) => !nodes.some((node) => node.modelCode === item.code));
      // }
    });
  }

  /**
   * 创建表单
   * @returns
   */
  createForm(): FormGroup {
    return this.fb.group({
      dataType: ['String', Validators.required],
      varName: [null, [Validators.required, Validators.pattern('^(?!\\d)[a-zA-Z0-9_]+$')]],
      isArray: [false],
      required: [false],
      defaultValue: [null, CommonValidators.emptyStringToNull],
      description: [null, CommonValidators.emptyStringToNull],
      mappingCode: [null],
      code: [null],
    });
  }

  /**
   * defaultValue 为空字符串时置为null
   */
  resetToNull() {
    this.form.get('defaultValue').valueChanges.subscribe((value) => {
      if (trim(value) === '') {
        this.form.get('defaultValue').setValue(null, { emitEvent: false });
      }
    });
  }

  /**
   * 根据required状态禁用defaultValue
   * 根据dataType展示model
   */
  defaultValueChange(): void {
    this.form.get('required').valueChanges.subscribe((value) => {
      const defaultValue = this.form.get('defaultValue');
      if (!value) {
        defaultValue.enable();
        return;
      }
      defaultValue.disable();
      defaultValue.setValue(null);
    });
    this.form.get('dataType').valueChanges.subscribe((value) => {
      if (!value) {
        return;
      }
      this.handleDataSourceChange(value);
    });
    this.handleModalChange();
  }

  /**
   * modal改变单独处理
   */
  handleModalChange() {
    this.form.get('modeCode')?.valueChanges.subscribe((value) => {
      if (!value) {
        return;
      }
      const modelItem = this.modelList.find((item) => item.code === value);
      this.form.get('serviceCode').setValue(modelItem?.serviceCode);
    });
  }

  /**
   * dataSource 改变
   * 数据来源 business 业务对象 node数据节点
   */
  handleDataSourceChange(code) {
    if (code === 'DTDVar') {
      this.form.get('mappingCode').setValidators([Validators.required]);
      this.form.get('description').disable();
    } else {
      this.form.get('mappingCode').setValidators(null);
      this.form.get('description').enable();
    }
    if (code === 'BusinessObject') {
      this.form.addControl('modeCode', this.fb.control(this.formData?.modeCode, Validators.required));
      this.form.addControl('serviceCode', this.fb.control(this.formData?.serviceCode));
      this.handleModalChange();
    } else {
      this.form?.removeControl('modeCode');
      this.form?.removeControl('serviceCode');
    }
    if (this.lastDataType === 'Mechanism') {
      this.form?.get('varName').setValue(this.initVariableName);
    } else if (code === 'Mechanism') {
      this.form?.get('varName').setValue(undefined);
    }
    this.lastDataType = code;
  }

  t(desc: string, params: any = {}): void {
    return this.translate.instant(desc, params);
  }

  handleOk(): void {
    validatorForm(this.form);
    if (this.form.valid) {
      const data = this.form.getRawValue();
      if (data.dataType === 'Mechanism') {
        this.updateFormData.emit(Object.assign({}, data, { ...this.mechanismItem }));
      } else if (data.dataType === 'DTDVar') {
        const { mappingCode, ...rest } = data;
        const [code, next] = mappingCode.split('@@');
        this.updateFormData.emit({
          ...rest,
          code,
          mappingCode: next,
        });
      } else {
        this.updateFormData.emit(data);
      }
    }
  }

  handleCancel(): void {
    this.visibleChange.emit(false);
  }

  handleAfterClose(): void {
    this.form.reset();
  }

  /**
   * 查询解决方案的参数
   */
  async handleQueryMechanism(): Promise<void> {
    const param = {
      appCode: this.appService?.selectedApp?.code,
    };
    const { code, data } = await this.variableModalService.loadAppParam(param).toPromise();
    if (code === 0) {
      this.mechanismList = data || [];
    } else {
      this.mechanismList = [];
    }
  }

  getMechanismLabel(value: string): string | undefined {
    return this.mechanismList.find((e) => e.paramCode === value)?.paramName;
  }
}
