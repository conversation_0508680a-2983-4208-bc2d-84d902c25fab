<form nz-form class="form-item field-form-content" [formGroup]="dataFormGroup">
  <nz-form-item [style]="style" class="nz-form-item-content field-setting">
    <nz-form-control [nzAutoTips]="errorTips">
      <!--节点id-->
      <div class="form-item">
        <div class="item-title">
          {{ 'dj-字段设置' | translate }}
          <span class="item-required" *ngIf="required">*</span>
        </div>
      </div>
    </nz-form-control>
  </nz-form-item>

  <div class="field-content" formArrayName="fieldInfos">
    <div class="field-row" *ngFor="let properties of fieldInfos.controls; let i = index" [formGroupName]="i">
      <div class="field-line">
        <div class="field-form field-form-value">
          <nz-form-item>
            <nz-form-control [nzErrorTip]="'dj-请选择' | translate">
              <ad-select
                style="width: 100%"
                [placeholder]="'dj-属性-值' | translate"
                [nzAllowClear]="false"
                [nzShowSearch]="false"
                formControlName="fieldId"
                [nzDropdownMatchSelectWidth]="true"
                nzDropdownClassName="field-select-dropdown"
                (ngModelChange)="onChangeFieldId($event, i)"
                [nzDisabled]="fieldIdDisabled(properties.get('fieldId').value)"
              >
                <ad-option
                  *ngFor="let item of explainBody"
                  [nzDisabled]="selectDisabled(item)"
                  [nzValue]="item.fieldId"
                  [nzLabel]="item.fieldName"
                ></ad-option>
              </ad-select>
            </nz-form-control>
          </nz-form-item>

          <nz-form-item>
            <nz-form-control>
              <!-- <div class="line">{{ 'dj-值设为' | translate }}</div> -->
              <div class="line">值设为</div>
            </nz-form-control>
          </nz-form-item>

          <nz-form-item class="field-form-item">
            <nz-form-control [nzErrorTip]="'dj-请输入' | translate">
              <nz-input-group [nzAddOnBefore]="addOnBeforeTemplate">
                <ng-template #addOnBeforeTemplate>
                  <span class="field-paramType" nz-button nz-dropdown [nzDropdownMenu]="paramTypeDropDown">
                    <i adIcon [iconfont]="fieldType[properties.get('paramType').value].icon"></i>
                  </span>
                  <nz-dropdown-menu #paramTypeDropDown="nzDropdownMenu">
                    <ul nz-menu>
                      <li
                        nz-menu-item
                        [ngClass]="{
                          'field-menu-item': true,
                          'field-menu-item-active': field === properties.get('paramType').value
                        }"
                        *ngFor="let field of fieldTypeEum"
                        (click)="changeParamType(field, i)"
                      >
                        <i adIcon [iconfont]="fieldType[field].icon"></i
                        ><span class="field-name">{{ fieldType[field].name }}</span>
                      </li>
                    </ul>
                  </nz-dropdown-menu>
                </ng-template>

                <input
                  *ngIf="properties.get('paramType').value == 'constant'"
                  nz-input
                  formControlName="fieldValue"
                  [placeholder]="'dj-属性-值' | translate"
                />
                <app-variable-select-input
                  *ngIf="properties.get('paramType').value == 'variable'"
                  formControlName="valueSuffix"
                  [variableRange]="variableRange"
                  [nodeId]="nodeId"
                  [sufixKey]="'valueSuffix'"
                  [inputable]="true"
                  [maxWidth]="118"
                  [item]="getItem(i)"
                  (onChanged)="handleValueChanged($event, i)"
                >
                </app-variable-select-input>
                <ng-template #tt>
                  <span
                    >{{ properties.get('fieldName').value }} <span *ngIf="properties.get('fieldValue').value">(</span
                    >{{ properties.get('fieldValue').value }}
                    <span *ngIf="properties.get('fieldValue').value">)</span></span
                  >
                </ng-template>
                <ng-template #modalTpl>
                  <i
                    class="icon"
                    adIcon
                    [iconfont]="'icon-kaichuang'"
                    (click)="selectFieldValueModal(properties.value, i)"
                  ></i>
                </ng-template>
              </nz-input-group>
            </nz-form-control>
          </nz-form-item>
        </div>
        <div
          class="field-close"
          [ngClass]="{
            'iconfont-disabled': fieldIdDisabled(properties.get('fieldId').value)
          }"
          (click)="removeItem(i, fieldIdDisabled(properties.get('fieldId').value))"
        ></div>
      </div>
    </div>
  </div>

  <nz-form-item>
    <nz-form-control>
      <button
        [ngClass]="{
          'field-add': true,
          'field-add-error': fieldSetError
        }"
        ad-button
        nzSize="large"
        (click)="handleAddFields()"
      >
        {{ 'dj-添加字段' | translate }}
      </button>
      <div *ngIf="fieldSetError" class="field-add-error-tip">{{ 'dj-请输入' | translate }}</div>
    </nz-form-control>
  </nz-form-item>

  <!-- <app-msg-add-field-value
    *ngIf="showFieldValueModal"
    [bindForm]="bindForm"
    [explainBody]="explainBody"
    [showModal]="showFieldValueModal"
    [fieldValueItem]="fieldValueItem"
    (close)="confirmFieldValue()"
    (changeFieldValueItem)="changeFieldValueItem($event)"
  >
  </app-msg-add-field-value> -->
</form>
