<div class="view-designer-header">
  <div class="header-bar">
    <div class="title">
      <div *ngIf="headerCustomTemplate">
        <ng-template [ngTemplateOutlet]="headerCustomTemplate"></ng-template>
      </div>
      <div
        nz-tooltip
        [nzTooltipTitle]="processName"
        [nzTooltipPlacement]="'top'"
        class="title-text"
        *ngIf="!headerCustomTemplate"
      >
        <span class="font-class" nz-icon nzType="arrow-left" nzTheme="outline" (click)="goback()"></span>
        <span>{{ processName }}</span>
      </div>
      <i
        [class]="processSettingValid ? 'setting-default' : 'setting-warning'"
        adIcon
        iconfont="iconsheding"
        aria-hidden="true"
        nz-tooltip
        [nzTooltipTitle]="'dj-业务流设置' | translate"
        (click)="handleSetViewDesign()"
      ></i>
    </div>
    <div class="button-group">
      <button
        class="ant-btn-primary-version ant-btn-primary-border version-dropdown-button"
        nz-button
        nzType="default"
        nz-dropdown
        nzTrigger="click"
        [nzLoading]="loadingVersion"
        [nzDropdownMenu]="menu"
        nzPlacement="bottomLeft"
        *ngIf="projectCatetory !== 'combined'"
      >
        <div class="version-link">
          <span class="version-link-text">{{ viewGraphService.currentSelectedVersion?.adpRemark }}</span>
          <span
            [ngClass]="{
              'version-link-tag': viewGraphService.currentSelectedVersion?.adpStatus === 'effect',
              'version-link-tag-draw': viewGraphService.currentSelectedVersion?.adpStatus === 'draft'
            }"
          >
            {{
              viewGraphService.currentSelectedVersion?.adpStatus === 'effect'
                ? ('dj-生效' | translate)
                : ('dj-草稿' | translate)
            }}
          </span>
        </div>
      </button>
      <ng-container *operateAuth="{ prefix: 'update', tenantPaas: isTenant, guards: [!isFromDtdReference] }">
        <button
          class="ant-btn-primary-version ant-btn-primary-border"
          (click)="handleAddVersion()"
          *ngIf="viewGraphService.currentSelectedVersion?.adpStatus === 'effect' && projectCatetory !== 'combined'"
          nz-button
          [nzLoading]="addVersionLoading"
          nzType="default"
        >
          <span class="create-new-version-item">{{ 'dj-创建新版本' | translate }}</span>
        </button>

        <app-module-publish-button
          #publishButton
          [classes]="'ant-btn-primary-border'"
          [size]="'default'"
          [module]="isTenant ? 'tenant_process' : 'process'"
          [pkValue]="processId"
          [needTenant]="true"
          [needSave]="true"
          [nzMode]="isTenant ? 'default' : 'multiple'"
          [isTenant]="isTenant"
          [tenantUserId]="tenantUserId"
          [tenantHeaders]="viewApiService.tenantHeaders"
          [needEffect]="true"
          [effectData]="effectData"
          [projectCategory]="projectCatetory"
          (clickPublicAction)="handleOperateBeforePublish()"
          (publicAction)="handleOperatePublish($event)"
          (effectiveFlow)="handleEffectiveFlow()"
          (upDateState)="handleUpDateState($event)"
        ></app-module-publish-button>
        <button
          ad-button
          adType="primary"
          [nzLoading]="viewGraphService.isSaveLoading"
          nzSize="large"
          (click)="handleOperateSave(false, false)"
        >
          {{ 'dj-保存' | translate }}
        </button>
      </ng-container>
    </div>
  </div>
  <div class="tool-bar">
    <div class="intro-view-designer-1"></div>

    <ng-container *operateAuth="{ prefix: 'create', tenantPaas: isTenant, guards: [!isFromDtdReference] }">
      <div class="tool-bar-item" (click)="handleOpenNodeMenu($event)">
        <i adIcon [iconfont]="'iconshulidejiahao'" class="iconfont" aria-hidden="true"> </i>
        <span>{{ 'dj-新增节点' | translate }}</span>
      </div>
    </ng-container>

    <ng-container *operateAuth="{ prefix: 'create', tenantPaas: isTenant, guards: [!isFromDtdReference] }">
      <div class="tool-bar-item border-right" (click)="handleAddVariable()">
        <i adIcon [iconfont]="'iconshulidejiahao'" class="iconfont" aria-hidden="true"> </i>
        <span>{{ 'dj-新增全局变量' | translate }}</span>
      </div>
    </ng-container>

    <div class="tool-bar-item">
      <span nz-tooltip [nzTooltipTitle]="'dj-还原' | translate" (click)="handleOperateOriginalSize()">{{
        'dj-还原' | translate
      }}</span>
    </div>
    <div class="tool-bar-item">
      <i
        adIcon
        [iconfont]="'iconfangda2'"
        nz-tooltip
        [nzTooltipTitle]="'so-放大' | translate"
        class="iconfont"
        aria-hidden="true"
        (click)="handleOperateEnlarge()"
      >
      </i>
    </div>
    <div class="tool-bar-item">
      <i
        adIcon
        [iconfont]="'iconsuoxiao2'"
        nz-tooltip
        [nzTooltipTitle]="'so-缩小' | translate"
        class="iconfont"
        aria-hidden="true"
        (click)="handleOperateReduce()"
      >
      </i>
    </div>
    <!-- 业务逻辑复杂，问题会比较多，隐藏 -->
    <!-- <div class="tool-bar-item">
      <i
        adIcon
        [iconfont]="'iconchexiao-graph'"
        nz-tooltip
        [nzTooltipTitle]="'so-撤销' | translate"
        class="iconfont"
        aria-hidden="true"
        (click)="handleOperateUndo()"
      >
      </i>
    </div>
    <div class="tool-bar-item">
      <i
        adIcon
        [iconfont]="'iconzhongzuo-graph'"
        nz-tooltip
        [nzTooltipTitle]="'so-恢复' | translate"
        class="iconfont"
        aria-hidden="true"
        (click)="handleOperateRedo()"
      >
      </i>
    </div> -->
  </div>
</div>

<app-variable-modal
  *ngIf="variableModalVisible"
  [taskCode]="taskCode"
  [isFusionMode]="viewStoreService.isFusionMode()"
  [visible]="variableModalVisible"
  formData="{}"
  (visibleChange)="handleVisibleChange($event)"
  (updateFormData)="handleUpdateFormData($event)"
></app-variable-modal>

<app-manage-version-modal
  *ngIf="manageVersionVisible"
  [visible]="manageVersionVisible"
  (visibleChange)="handleManageVisible($event)"
  (refresh)="handleRefreshVersionList()"
></app-manage-version-modal>

<app-version-remark-modal
  *ngIf="versionRemarkVisible"
  [visible]="versionRemarkVisible"
  [processId]="processId"
  [data]="viewGraphService.currentSelectedVersion"
  (visibleChange)="versionRemarkVisible = false"
  (updateRemark)="handleUpdateRemarkOK($event)"
></app-version-remark-modal>

<nz-dropdown-menu class="link-menu-container" #menu="nzDropdownMenu">
  <ul nz-menu class="ul-link-menu-container">
    <div *ngFor="let version of versionList; let i = index" (click)="handleSelectVersion(version)">
      <li nz-menu-item>
        <div
          class="version-link"
          [ngClass]="{
            'version-link': true,
            'version-link-active': viewGraphService.currentSelectedVersion?.adpVersion === version.adpVersion
          }"
        >
          <span nz-tooltip [nzTooltipTitle]="version.adpRemark" class="version-link-text">{{ version.adpRemark }}</span>
          <span
            [ngClass]="{
              'version-link-tag': version.adpStatus === 'effect',
              'version-link-tag-draw': version.adpStatus === 'draft'
            }"
          >
            {{ version.adpStatus === 'effect' ? ('dj-生效' | translate) : ('dj-草稿' | translate) }}
          </span>
        </div>
      </li>
    </div>

    <ng-container *operateAuth="{ prefix: 'update', tenantPaas: isTenant }">
      <li nz-menu-divider style="margin: 4px 12px"></li>
      <li nz-menu-item>
        <div class="version-link version-link-manage" (click)="handleUpdateRemark()">
          <i adIcon iconfont="iconcharu" aria-hidden="true"></i
          ><span class="update-version-remark-item">{{ 'dj-修改版本备注' | translate }}</span>
        </div>
      </li>
      <li nz-menu-item>
        <div class="version-link version-link-manage" (click)="handleVersionManagement()">
          <i adIcon iconfont="iconsheding" aria-hidden="true" class="submitIcon"></i
          ><span class="update-version-remark-item">{{ 'dj-业务流版本管理' | translate }}</span>
        </div>
      </li>
    </ng-container>
  </ul>
</nz-dropdown-menu>
