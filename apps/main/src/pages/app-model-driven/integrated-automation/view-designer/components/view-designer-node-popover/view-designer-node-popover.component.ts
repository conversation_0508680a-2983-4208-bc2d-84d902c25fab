import { AfterViewInit, Component, ElementRef, OnInit, ViewChild, OnDestroy } from '@angular/core';
import { Observable, Subject, Subscription } from 'rxjs';
import { TranslateService } from '@ngx-translate/core';
import { stencil, nodesAgs, commonNode } from '../../config/stencil';
import { ViewGraphService } from '../../service/graph.service';
import { ViewStoreService } from '../../service/store.service';

@Component({
  selector: 'app-view-designer-node-popover',
  templateUrl: './view-designer-node-popover.component.html',
  styleUrls: ['./view-designer-node-popover.component.less'],
})
export class ViewDesignerNodePopoverComponent implements OnInit, AfterViewInit, OnDestroy {
  @ViewChild('flowStencil', { static: true }) stencilRef: ElementRef;

  menuList: any[] = [];
  nodeMouseUpSubject: Observable<any> = new Subject();
  _nodeMouseUpSubject$: Subscription;

  constructor(
    private translateService: TranslateService,
    public viewGraphService: ViewGraphService,
    public viewStoreService: ViewStoreService,
  ) {}

  ngOnInit() {
    // 订阅浮层节点拖放至画布
    // this._nodeMouseUpSubject$ = this.nodeMouseUpSubject.subscribe((node: Node) => {
    //   this.viewGraphService.handleDropedNode(node);
    // });

    // 初始化 stencil 菜单
    // this.handleInit();

    // 初始化自定义菜单
    this.menuList = nodesAgs;
  }

  ngAfterViewInit() {}

  ngOnDestroy() {
    // this._nodeMouseUpSubject$.unsubscribe();
  }

  stopEvent(event: Event) {
    event.preventDefault();
    event.stopPropagation();
  }

  handleInit() {
    // 初始化左侧栏图组
    this.viewGraphService.stencil = stencil(
      {
        graph: this.viewGraphService.graph,
        basicTitle: this.translateService.instant('dj-基础节点'),
        manualTitle: this.translateService.instant('dj-人工节点'),
        dataTitle: this.translateService.instant('dj-数据节点'),
        automaticTitle: this.translateService.instant('dj-自动节点'),
        branchTitle: this.translateService.instant('dj-分支节点'),
      },
      this.nodeMouseUpSubject,
      this.stencilRef.nativeElement,
    );
    this.stencilRef.nativeElement.appendChild(this.viewGraphService.stencil.container);
    // 创建左侧栏图组内容组件
    this.createNodes();
  }

  // 创建节点并添加到左侧栏中
  private createNodes(): void {
    nodesAgs.forEach((item) => {
      const { group, nodes } = item;
      const loadNodes = nodes.map((data) => {
        const { nodeName, nodeType, isOpen } = data;
        const _nodeName = this.translateService.instant(nodeName);
        const commonNodeParams = {
          toolType: 'stencil',
          nodeName: _nodeName,
          nodeType,
        };
        const node = this.viewGraphService.graph.createNode(commonNode(commonNodeParams));
        node.setData({ nodeName: _nodeName, nodeType, isOpen: isOpen });
        return node;
      });
      this.viewGraphService.stencil.load(loadNodes, group);
    });
  }

  // 自定义菜单添加节点
  addNode(node) {
    this.viewGraphService.handleClickAddNode(node);
  }
}
