import { Component, EventEmitter, Input, OnChanges, OnDestroy, OnInit, Output, SimpleChanges } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NameValidators } from '../../../config/utils';
import { cloneDeep, debounce } from 'lodash';
import { ViewStoreService } from '../../../service/store.service';
@Component({
  selector: 'app-auto-evoke-wait',
  templateUrl: './auto-evoke-wait.component.html',
  styleUrls: ['./auto-evoke-wait.component.less'],
})
export class AutoEvokeWaitComponent implements OnInit, OnChanges {
  // 输入的渲染数据
  @Input() data;
  @Input() nodeId;
  // 通知父组件关闭属性面板
  @Output() close: EventEmitter<any> = new EventEmitter();
  // 表单数据改变通过校验后向父组件通知更新数据
  @Output() changeData: EventEmitter<any> = new EventEmitter();
  dataFormGroup: FormGroup;
  errorTips: Record<string, Record<string, string>> = {
    default: {
      required: this.translate.instant('dj-必填'),
      pattern: this.translate.instant('dj-超长'),
    },
  };
  formGroupValidityFlag = false;
  // 多语言单独处理，name
  nameLang: any;
  actionModal: boolean;
  actionData = [];

  public readonly api: string =
    'https://z0lxpczot6u.feishu.cn/wiki/RQevwHtTAiDvrCkCjzJcjgN0nMh?fromScene=spaceOverview';

  constructor(public translate: TranslateService, private fb: FormBuilder, public viewStoreService: ViewStoreService) {
    this.initForm();
  }

  ngOnInit() {
    this.handleInit();
    this.getCurrentData();
    this.dataFormGroup.valueChanges.subscribe(() => {
      if (!this.formGroupValidityFlag) {
        this.getCurrentData();
      }
    });
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (Object.keys(changes).includes('nodeId')) {
      if (!changes.nodeId.firstChange) {
        this.handleChangeValue();
      }
    }
  }

  handleInit(): void {
    this.dataFormGroup.patchValue({
      id: this.data?.id,
      name: this.data?.name,
      waitnode: this.data?.config?.wakeWaitingConfig?.wakeWaitingList?.map((item) => item.name).join(','),
    });
    if (this.data?.config?.wakeWaitingConfig?.wakeWaitingList?.length) {
      this.actionData = this.data?.config?.wakeWaitingConfig?.wakeWaitingList.map((item) => ({
        ...item,
        key: item?.nodeId,
        title: item?.name,
      }));
    } else {
      this.actionData = [];
    }
    this.nameLang = this.data.lang;
  }

  initForm(): void {
    this.dataFormGroup = this.fb.group({
      id: [null, [Validators.required]],
      name: [null, [NameValidators.trim, Validators.required, Validators.pattern('^.{0,255}$')]],
      waitnode: [null, [Validators.required]],
    });
  }

  handleClosePanel(): void {
    this.close.emit();
  }

  handlePatchApp(key: any, data: any): void {
    if (data.needLang) {
      this.nameLang = {
        ...(this.nameLang || {}),
        [key]: data.lang,
      };
    }
    this.dataFormGroup.patchValue({
      [key]: data?.value,
    });
  }

  handleChangeValue(): void {
    this.formGroupValidityFlag = true;
    this.dataFormGroup.patchValue({
      id: this.data?.id,
      name: this.data?.name,
      waitnode: this.data?.config?.wakeWaitingConfig?.wakeWaitingList?.map((item) => item.name).join(','),
    });
    if (this.data?.config?.wakeWaitingConfig?.wakeWaitingList?.length) {
      this.actionData = this.data?.config?.wakeWaitingConfig?.wakeWaitingList?.map((item) => ({
        ...item,
        key: item?.nodeId,
        title: item?.name,
      })) ?? [];
    } else {
      this.actionData = [];
    }
    this.formGroupValidityFlag = false;
    this.nameLang = this.data.lang;
    this.getCurrentData();
  }

  getCurrentData = debounce(
    () => {
      this.formGroupValidityFlag = true;
      for (const i of Object.keys(this.dataFormGroup?.controls)) {
        this.dataFormGroup.controls[i].markAsDirty();
        this.dataFormGroup.controls[i].updateValueAndValidity();
      }
      this.formGroupValidityFlag = false;
      const currentData = this.dataFormGroup.getRawValue();
      const isVerificationPassed = this.viewStoreService.transformVerificationPassed(this.dataFormGroup.valid);
      const wakeWaitingList = cloneDeep(this.actionData);
      const returnData = {
        data: Object.assign(this.data, {
          name: currentData.name,
          lang: this.nameLang,
          config: {
            wakeWaitingConfig: {
              wakeWaitingList: wakeWaitingList.map(item=>{
                // 处理数据，去掉不需要的字段,防止切换流程时跳保存提示
                delete item['key'];
                delete item['title'];
                return item;
              })
            },
          },
        }),
        isVerificationPassed,
      };
      console.log('returnData', returnData);
      this.changeData.emit(returnData);
      return returnData;
    },
    50,
    { leading: false, trailing: true },
  );
  handleOpenAction(): void {
    this.actionModal = true;
  }
  handleSelectAction(data: any): void {
    this.dataFormGroup.get('waitnode').setValue(data.map((item) => item.title).join(','));
    this.actionModal = false;
    this.actionData = data;
  }
}
