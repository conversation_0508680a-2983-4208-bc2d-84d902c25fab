<div class="auto-sub-project-property-root submit-form">
  <div class="header">
    <span>{{ 'node-子项目' | translate }}</span>
    <i adIcon iconfont="icondanchuxiaoxiguanbi" class="iconfont" aria-hidden="true" (click)="handleClosePanel()"></i>
  </div>
  <form nz-form class="content" [formGroup]="dataFormGroup">
    <nz-collapse [nzBordered]="false">
      <!--基本信息的面板-->
      <nz-collapse-panel [nzHeader]="'dj-基本信息' | translate" [nzActive]="true" [nzExpandedIcon]="'caret-right'">
        <nz-form-item class="nz-form-item-content">
          <nz-form-control [nzAutoTips]="errorTips">
            <!--节点id-->
            <div class="form-item">
              <div class="item-title">
                {{ 'dj-节点id' | translate }}
                <span class="item-required">*</span>
              </div>
              <app-component-input
                [attr]="{
                  name: '节点id',
                  required: true,
                  needLang: false,
                  readOnly: true
                }"
                style="width: 100%"
                formControlName="id"
                [value]="dataFormGroup.get('id')?.value"
                ngDefaultControl
              >
              </app-component-input>
            </div>
          </nz-form-control>
        </nz-form-item>

        <nz-form-item class="nz-form-item-content">
          <nz-form-control [nzAutoTips]="errorTips">
            <!--节点名称-->
            <div class="form-item">
              <div class="item-title">
                {{ 'dj-节点名称' | translate }}
                <span class="item-required">*</span>
              </div>
              <app-modal-input
                class="language-input"
                [attr]="{
                  name: 'name',
                  required: true,
                  lang: this.nameLang?.name,
                  needLang: true
                }"
                [innerLabel]="false"
                [value]="nameLang?.name?.[('dj-LANG' | translate)] || dataFormGroup.get('name')?.value"
                (callBack)="handlePatchApp('name', $event)"
                [formControlName]="'name'"
                ngDefaultControl
              ></app-modal-input>
            </div>
          </nz-form-control>
        </nz-form-item>
      </nz-collapse-panel>
      <nz-collapse-panel [nzHeader]="'dj-节点设置' | translate" [nzActive]="true" [nzExpandedIcon]="'caret-right'">
        <nz-form-item class="nz-form-item-content">
          <nz-form-control [nzAutoTips]="errorTips">
            <div class="form-item">
              <div class="item-title">
                {{ 'dj-选择项目' | translate }}
                <span class="item-required">*</span>
                <i
                  adIcon
                  iconfont="iconshuomingwenzi"
                  aria-hidden="true"
                  class="question-icon"
                  nzTooltipTrigger="hover"
                  nz-tooltip
                  [nzTooltipTitle]="'dj-请选择需发起的项目，子项目仅支持同步子项目' | translate"
                >
                </i>
              </div>
              <div>
                <nz-spin [nzSpinning]="loading" nzSize="small" class="loading-spin">
                  <ad-select
                    formControlName="projectCode"
                    [nzPlaceHolder]="'dj-请选择' | translate"
                    nzAllowClear="false"
                  >
                    <ad-option
                      *ngFor="let item of projects"
                      [nzLabel]="item.name + '-' + item.code"
                      [nzValue]="item.code"
                    ></ad-option>
                  </ad-select>
                </nz-spin>
              </div>
            </div>
          </nz-form-control>
        </nz-form-item>
        <div class="form-item">
          <div class="item-title">
            <span class="label-text"
              >{{ 'dj-输入数据' | translate }}
              <i
                adIcon
                iconfont="iconshuomingwenzi"
                aria-hidden="true"
                class="question-icon"
                nzTooltipTrigger="hover"
                nz-tooltip
                [nzTooltipTitle]="'dj-用于发起子项目的输入数据，默认为上一个节点的输出数据' | translate"
              >
              </i>
            </span>
          </div>
          <div>
            <nz-input-group [nzSuffix]="suffixIcona">
              <input
                nz-input
                readonly
                formControlName="inputVariables"
                [placeholder]="'dj-请输入' | translate"
                style="width: 100%"
                (dblclick)="handleOpenScriptEditor('inputVariables')"
              />
            </nz-input-group>
            <ng-template #suffixIcona>
              <i adIcon iconfont="icongaodaima" (click)="handleOpenScriptEditor('inputVariables')"></i>
            </ng-template>
          </div>
        </div>
        <div class="form-item">
          <div class="item-title">
            <span class="label-text"
              >{{ 'dj-输出数据' | translate }}
              <i
                adIcon
                iconfont="iconshuomingwenzi"
                aria-hidden="true"
                class="question-icon"
                nzTooltipTrigger="hover"
                nz-tooltip
                [nzTooltipTitle]="'dj-组装子项目的返回值；默认为子项目最后一个活动的输出数据' | translate"
              >
              </i>
            </span>
          </div>
          <div>
            <nz-input-group [nzSuffix]="suffixIconb">
              <input
                nz-input
                readonly
                formControlName="outputVariables"
                [placeholder]="'dj-请输入' | translate"
                style="width: 100%"
                (dblclick)="handleOpenScriptEditor('outputVariables')"
              />
            </nz-input-group>
            <ng-template #suffixIconb>
              <i adIcon iconfont="icongaodaima" (click)="handleOpenScriptEditor('outputVariables')"></i>
            </ng-template>
          </div>
        </div>
      </nz-collapse-panel>
    </nz-collapse>
  </form>
</div>
<app-script-editor
  *ngIf="requestScriptModal"
  [scriptModal]="requestScriptModal"
  [script]="requestScriptContent"
  (confirm)="handleSaveRequestScript($event)"
  (close)="requestScriptModal = false"
></app-script-editor>
