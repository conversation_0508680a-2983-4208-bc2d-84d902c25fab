import { Component, EventEmitter, Input, OnChanges, OnDestroy, OnInit, Output, SimpleChanges } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NameValidators } from '../../../config/utils';
import { debounce, omit } from 'lodash';
import { ViewStoreService } from '../../../service/store.service';
import { EVariableRange } from '../variable-select-input/components/variable-modal/variable-modal.component';
import { VariableType } from '../../../config/typings';
import { validatorForm } from 'common/utils/core.utils';
import { dateType } from 'pages/app/dtd/drive-execution-new/components/project-widget/project-set';
import { ViewApiService } from '../../../service/api.service';
@Component({
  selector: 'app-wait-property',
  templateUrl: './wait-property.component.html',
  styleUrls: ['./wait-property.component.less'],
})
export class WaitPropertyComponent implements OnInit, OnChanges {
  // 输入的渲染数据
  @Input() data;
  @Input() nodeId;
  // 通知父组件关闭属性面板
  @Output() close: EventEmitter<any> = new EventEmitter();
  // 表单数据改变通过校验后向父组件通知更新数据
  @Output() changeData: EventEmitter<any> = new EventEmitter();
  dataFormGroup: FormGroup;
  errorTips: Record<string, Record<string, string>> = {
    default: {
      required: this.translate.instant('dj-必填'),
      pattern: this.translate.instant('dj-超长'),
    },
  };
  formGroupValidityFlag = false;
  // 多语言单独处理，name
  nameLang: any;
  /** 业务主键选项 */
  optionList = [];
  scriptModalVisible: boolean = false;
  script: string = '';
  sourceKeyData: any;
  defaultScript: string = `/*
  返回数组包裹主键形式，例如return["staff_id"]
*/`;
  get variableRange() {
    const mode = EVariableRange.NODE | EVariableRange.MODEL;
    return mode;
  }
  get item() {
    const { dataType, modelCode, valueCode, variableCode } = this.sourceKeyData;
    if (!dataType) return undefined;
    const isModel = [VariableType.BUSINESS_OBJECT, VariableType.MODEL].includes(dataType);
    return {
      valueType: dataType,
      valuePath: isModel ? variableCode : valueCode, // 模型/业务对象
      valueCode: isModel ? variableCode : valueCode, // 变量
    };
  }

  public readonly api: string =
    'https://z0lxpczot6u.feishu.cn/wiki/RQevwHtTAiDvrCkCjzJcjgN0nMh?fromScene=spaceOverview';

  constructor(
    public translate: TranslateService,
    private fb: FormBuilder,
    public viewStoreService: ViewStoreService,
    private viewApiService: ViewApiService,
  ) {
    this.initForm();
  }

  ngOnInit() {
    this.handleInit();
    this.getCurrentData();
    this.dataFormGroup.get('wakeupStrategy').valueChanges.subscribe((val) => {
      if (val === 'auto') {
        this.dataFormGroup.get('dateType').setValidators([Validators.required]);
        this.dataFormGroup.get('dateValue').setValidators([Validators.required]);
        this.dataFormGroup.get('sourceKey').setValidators(null);
        this.dataFormGroup.get('script').setValidators(null);
        this.dataFormGroup.get('uniKeys').setValidators(null);
        this.sourceKeyData = {};
      } else {
        this.dataFormGroup.get('dateType').setValidators(null);
        this.dataFormGroup.get('dateValue').setValidators(null);
        this.dataFormGroup.get('sourceKey').setValidators(null);
        this.dataFormGroup.get('script').setValidators([Validators.required]);
        this.dataFormGroup.get('uniKeys').setValidators([Validators.required]);
      }
    });
    this.dataFormGroup.get('type').valueChanges.subscribe((val) => {
      if (this.dataFormGroup.get('wakeupStrategy').value !== 'auto') {
        if (val === 'mapping') {
          this.dataFormGroup.get('uniKeys').setValidators([Validators.required]);
          this.dataFormGroup.get('script').setValidators(null);
        } else {
          this.dataFormGroup.get('script').setValidators([Validators.required]);
          this.dataFormGroup.get('uniKeys').setValidators(null);
        }
      }
    });
    this.dataFormGroup.valueChanges.subscribe(() => {
      if (!this.formGroupValidityFlag) {
        this.getCurrentData();
      }
    });
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (Object.keys(changes).includes('nodeId')) {
      if (!changes.nodeId.firstChange) {
        this.handleChangeValue();
      }
    }
  }

  handleInit(): void {
    const isModel = [VariableType.BUSINESS_OBJECT, VariableType.MODEL].includes(this.data?.config?.wakeupConfig?.dataType);
    this.sourceKeyData = {
      valueType: this.data?.config?.wakeupConfig?.dataType,
      dataType: this.data?.config?.wakeupConfig?.dataType,
      valueCode: this.data?.config?.wakeupConfig?.valueCode,
      modelCode: this.data?.config?.wakeupConfig?.modelCode,
      serviceCode: this.data?.config?.wakeupConfig?.serviceCode,
      variableCode: isModel ? this.data?.config?.wakeupConfig?.modelCode : ''
    };
    this.dataFormGroup.patchValue({
      id: this.data?.id,
      name: this.data?.name,
      wakeupStrategy: this.data?.config?.wakeupConfig?.wakeupStrategy ?? 'manual',
      dateType: this.data?.config?.wakeupConfig?.dateType,
      dateValue: this.data?.config?.wakeupConfig?.dateValue,
      settingType: this.data?.config?.wakeupConfig?.settingType ?? 'calculation',
      type: this.data?.config?.wakeupConfig?.type || 'mapping',
      sourceKey: this.data?.config?.wakeupConfig?.nodeId || this.data?.config?.wakeupConfig?.modelCode,
      script: this.data?.config?.wakeupConfig?.script,
      uniKeys: this.data?.config?.wakeupConfig?.uniKeys,
    });
    this.nameLang = this.data.lang;
    if (this.data?.config?.wakeupConfig?.type === 'mapping') {
      this.queryFieldsByDataTypeFunc(this.sourceKeyData);
    }
  }

  initForm(): void {
    this.dataFormGroup = this.fb.group({
      id: [null, [Validators.required]],
      name: [null, [NameValidators.trim, Validators.required, Validators.pattern('^.{0,255}$')]],
      wakeupStrategy: [null, [Validators.required]],
      dateType: [null],
      dateValue: [null],
      settingType: [null],
      sourceKey: [null],
      type: ['mapping'],
      script: [null, [Validators.required]],
      uniKeys: [null, [Validators.required]],
    });
  }

  handleClosePanel(): void {
    this.close.emit();
  }

  handlePatchApp(key: any, data: any): void {
    if (data.needLang) {
      this.nameLang = {
        ...(this.nameLang || {}),
        [key]: data.lang,
      };
    }
    this.dataFormGroup.patchValue({
      [key]: data?.value,
    });
  }

  handleChangeValue(): void {
    this.formGroupValidityFlag = true;
    this.dataFormGroup.patchValue({
      id: this.data?.id,
      name: this.data?.name,
      wakeupStrategy: this.data?.config?.wakeupConfig?.wakeupStrategy ?? 'manual',
      dateType: this.data?.config?.wakeupConfig?.dateType,
      dateValue: this.data?.config?.wakeupConfig?.dateValue,
      settingType: this.data?.config?.wakeupConfig?.settingType,
      sourceKey: this.data?.config?.wakeupConfig?.nodeId || this.data?.config?.wakeupConfig?.modelCode,
      type: this.data?.config?.wakeupConfig?.type ?? 'mapping',
      script: this.data?.config?.wakeupConfig?.script,
      uniKeys: this.data?.config?.wakeupConfig?.uniKeys,
    });
    // bug-161263 防止丢失数据
    const isModel = [VariableType.BUSINESS_OBJECT, VariableType.MODEL].includes(this.data?.config?.wakeupConfig?.dataType);
    this.sourceKeyData = {
      valueType: this.data?.config?.wakeupConfig?.dataType,
      dataType: this.data?.config?.wakeupConfig?.dataType,
      valueCode: this.data?.config?.wakeupConfig?.valueCode,
      modelCode: this.data?.config?.wakeupConfig?.modelCode,
      serviceCode: this.data?.config?.wakeupConfig?.serviceCode,
      variableCode: isModel ? this.data?.config?.wakeupConfig?.modelCode : ''
    };
    this.formGroupValidityFlag = false;
    this.nameLang = this.data.lang;
    this.getCurrentData();
    if (this.sourceKeyData?.dataType && this.data?.config?.wakeupConfig?.type === 'mapping') {
      this.queryFieldsByDataTypeFunc(this.sourceKeyData);
    }
  }

  getCurrentData = debounce(
    () => {
      this.formGroupValidityFlag = true;
      for (const i of Object.keys(this.dataFormGroup?.controls)) {
        this.dataFormGroup.controls[i].markAsDirty();
        this.dataFormGroup.controls[i].updateValueAndValidity();
      }
      this.formGroupValidityFlag = false;
      const currentData = this.dataFormGroup.getRawValue();
      const isVerificationPassed = this.viewStoreService.transformVerificationPassed(this.dataFormGroup.valid);
      const wakeupConfig = {
        wakeupStrategy: currentData.wakeupStrategy,
        settingType: 'calculation',
        dateValue: currentData.dateValue ?? 0,
      }
      if (wakeupConfig.wakeupStrategy === 'auto') {
        wakeupConfig['dateType'] = currentData.dateType;
      } else {
        wakeupConfig['type'] = currentData?.type;
        wakeupConfig['nodeId'] = this.sourceKeyData?.valueType === VariableType.NODE ? this.sourceKeyData?.valueCode : null;
        wakeupConfig['modelCode'] = this.sourceKeyData?.valueType === VariableType.MODEL ? this.sourceKeyData?.modelCode : null;
        wakeupConfig['serviceCode'] = this.sourceKeyData?.valueType === VariableType.MODEL ? this.sourceKeyData?.serviceCode : null;
        wakeupConfig['valueCode'] = this.sourceKeyData?.valueCode;
        wakeupConfig['dataType'] = this.sourceKeyData?.dataType;
        if (currentData?.type === 'mapping') {
          wakeupConfig['uniKeys'] = currentData?.uniKeys;
        } else {
          wakeupConfig['script'] = currentData.script;
        }
      }
      const returnData = {
        data: Object.assign(this.data, {
          name: currentData.name,
          lang: this.nameLang,
          config: {
            wakeupConfig,
          },
        }),
        isVerificationPassed,
      };
      console.log('returnData', returnData);
      this.changeData.emit(returnData);
      return returnData;
    },
    50,
    { leading: false, trailing: true },
  );

  handleClick(): void {
    window.open(this.api, '_blank');
  }
  handleValueChanged(value: any): void {
    this.queryFieldsByDataTypeFunc(value);
    this.dataFormGroup.get('uniKeys').setValue(null);
    this.sourceKeyData = value;
  }
  handleChangeType() {
    this.queryFieldsByDataTypeFunc(this.sourceKeyData);
  }
  /**
   * 点击打开脚本编辑器
   */
  handleOpenScript(): void {
    this.scriptModalVisible = true;
    this.script =
      this.dataFormGroup.get('script').value || this.defaultScript;
  }
  /**
   * 关闭或确认打开脚本编辑器
   * @param type
   * @param data
   */
  handleCloseScript(type, data): void {
    this.scriptModalVisible = false;
    if (type === 'confirm') {
      this.dataFormGroup.get('script').setValue(data);
    }
  }

  /**
   * @description: 
   * @return {*}
   */
  queryFieldsByDataTypeFunc(value: any) {
    const param = {
      type: value.valueType,
      serviceCode: value.serviceCode,
      nodeId: value.valueCode,
      modelCode: value.modelCode,
    };
    this.viewApiService.queryFieldsByDataType(param).subscribe((res) => {
      if (res.code === 0) {
        this.optionList = res?.data?.[0]?.modelFileInfos || [];
      }
    });
  }
}
