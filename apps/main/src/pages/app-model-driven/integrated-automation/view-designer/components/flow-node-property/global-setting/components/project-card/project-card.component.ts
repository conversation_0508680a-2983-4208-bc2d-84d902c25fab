import { Component, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';
import { AppService } from 'pages/apps/app.service';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { NameValidators } from 'pages/app-model-driven/integrated-automation/view-designer/config/utils';
import { TranslateService } from '@ngx-translate/core';
import { cloneDeep, size } from 'lodash';
import dayjs from 'dayjs';
import { DataSourceService } from 'components/page-design/components/data-source/data-source.service';
import { ExtendedInfoComponent } from 'components/bussiness-components/extended-info/extended-info.component';
import { ViewApiService } from 'pages/app-model-driven/integrated-automation/view-designer/service/api.service';
import { dataTypeToVariableType, variableTypeToDataType } from 'pages/app-model-driven/utils/utils';
import { EVariableRange } from '../../../variable-select-input/components/variable-modal/variable-modal.component';
import { VariableType } from 'pages/app-model-driven/integrated-automation/view-designer/config/typings';
import { ActivatedRoute } from '@angular/router';
import { ViewStoreService } from 'pages/app-model-driven/integrated-automation/view-designer/service/store.service';

@Component({
  selector: 'app-project-card',
  templateUrl: './project-card.component.html',
  styleUrls: ['./project-card.component.less'],
})
export class ProjectCardComponent implements OnInit {
  // 输入的渲染数据
  @Input() data;
  // 表单数据改变通过校验后向父组件通知更新数据
  @Output() changeData: EventEmitter<any> = new EventEmitter();

  dataFormGroup;
  // 多语言单独处理，name title
  lang: any;

  dateType = [
    { value: 'WEEK_DAY', label: 'WEEK_DAY' },
    { value: 'LAST_MONTH', label: 'LAST_MONTH' },
    { value: 'DAY', label: 'DAY' },
    { value: 'MULTI_DAY', label: 'MULTI_DAY' },
    { value: 'CURRENT_MONTH', label: 'CURRENT_MONTH' },
  ];

  errorTips: Record<string, Record<string, string>> = {
    default: {
      required: this.translate.instant('dj-必填'),
      pattern: this.translate.instant('dj-超长'),
    },
  };

  workData: any; // 项目界面设计数据
  workVisible = false;

  // updateValueAndValidity 会触发form的改变，误走单改变的监听事件中，导致再次调用getCurrentData造成死循环
  formGroupValidityFlag = false;

  sourceData: any = {};
  planEndTime: any = {};
  fieldType = {
    constant: {
      icon: 'iconchangliang1',
      name: this.translate.instant('dj-常量'),
    },
    variable: {
      icon: 'iconbianliang',
      name: this.translate.instant('dj-变量'),
    },
  };

  isTenantProcessId: boolean = false;

  fieldTypeEum = ['constant', 'variable'];

  bindForm = {};

  get variableRange() {
    const mode = EVariableRange.BUSINESS | EVariableRange.CUSTOM | EVariableRange.SYSTEM | EVariableRange.MECHANISM;
    if (this.viewStoreService.isFusionMode()) {
      return mode | EVariableRange.DTD;
    }
    return mode;
  }

  get item() {
    const { variableCode, variablePath, variableSuffix, variableType } = this.sourceData;
    // console.log(this.data.planEndTime, 'this.data.planEndTime');
    if (!variableCode) return undefined;
    const type = dataTypeToVariableType(variableType);
    const isModel = [VariableType.BUSINESS_OBJECT, VariableType.MODEL].includes(type);
    return {
      variableSuffix: variableSuffix,
      valueType: type,
      valuePath: isModel ? variableCode : variablePath, // 模型/业务对象
      valueCode: isModel ? variablePath : variableCode, // 变量
    };
  }

  get projectCatetory() {
    return this.viewStoreService.state.projectCategory;
  }

  @ViewChild('extendedInfoRef') extendedInfoRef: ExtendedInfoComponent;

  personInCharge: any = {};

  constructor(
    public translate: TranslateService,
    private fb: FormBuilder,
    public appService: AppService,
    public dataSourceService: DataSourceService,
    public service: ViewApiService,
    private activatedRoute: ActivatedRoute,
    private viewStoreService: ViewStoreService,
  ) {
    this.activatedRoute.paramMap.subscribe((params) => {
      this.isTenantProcessId = !!params.get('objectId');
    });
  }

  ngOnInit() {
    this.handleInit();
  }

  // 根据输入的组装fromgroup
  handleInit(): void {
    // 初始化fromgroup
    // 直接校验
    // 对父组件发出change事件主要里面混入isVerificationPassed
    this.dataFormGroup = this.fb.group({
      processId: [this.data?.processId || '', [Validators.required]],
      processName: [
        this.data?.processName || '',
        [NameValidators.trim, Validators.required, Validators.pattern('^.{0,255}$')],
      ],
      daemon: [this.data?.daemon || false],
      merge: [this.data?.merge, []],
      processConfig: this.fb.group({
        subjectRule: [
          this.data?.processConfig?.subjectRule || '',
          [NameValidators.trim, Validators.required, Validators.pattern('^.{0,255}$')],
        ],
        sourceName: [
          this.data?.processConfig?.sourceName || '',
          [NameValidators.trim, Validators.required, Validators.pattern('^.{0,255}$')],
        ],
        projectDescription: [this.data?.processConfig?.projectDescription || ''],
        enableCustomProjectCard: [this.data?.processConfig?.enableCustomProjectCard],
      }),
      planEndTime: this.fb.group({
        settingType: [this.data?.planEndTime?.settingType ?? 'no', [Validators.required]], // 节点设置类型
        sourceType: [this.data?.planEndTime?.sourceType ?? 'constant', [Validators.required]], // 节点设置类型
        sourceValue: [this.data?.planEndTime?.sourceValue, [Validators.required]], // 节点设置类型
        dateType: [this.data?.planEndTime?.dateType, [Validators.required]], // 节点设置类型
        dateValue: [this.data?.planEndTime?.dateValue, [Validators.required]], // 节点设置类型
        variableCode: [this.data?.planEndTime?.variableCode || '', [Validators.required]], // 节点设置类型
      }),
    });

    this.personInCharge = cloneDeep(this.data.personInCharge || {});
    this.bindForm = cloneDeep(this.data.bindForm || {});

    if (this.data.merge) {
      this.dataFormGroup.addControl(
        'dueDateTimeDistance',
        this.fb.group({
          type: [this.data?.dueDateTimeDistance?.type || ''],
          startDate: [this.data?.dueDateTimeDistance?.startDate || ''],
          count: [this.data?.dueDateTimeDistance?.count || ''],
        }),
      );
    }

    if (this.data?.planEndTime) {
      this.planEndTime = cloneDeep(this.data.planEndTime);
    }
    this.setPlanEndTime(this.planEndTime?.settingType);
    this.lang = cloneDeep(this.data.lang);

    this.dataFormGroup
      .get('planEndTime')
      ?.get('settingType')
      ?.valueChanges.subscribe((value) => {
        if (!this.formGroupValidityFlag) {
          this.setPlanEndTime(value);
        }
      });

    this.dataFormGroup.valueChanges.subscribe(() => {
      if (!this.formGroupValidityFlag) {
        this.getCurrentData();
      }
    });
  }

  ngAfterViewInit() {
    this.getCurrentData();
  }

  // 多语言栏位改变的事件处理
  handlePatchLang(key: any, data: any): void {
    if (data.needLang) {
      this.lang = {
        ...(this.lang || {}),
        [key]: data.lang,
      };
    }
    if ('subjectRule' === key) {
      // 特殊处理是嵌套在processConfig中的
      this.dataFormGroup.patchValue({ [`processConfig.${key}`]: data?.value });
    } else {
      this.dataFormGroup.patchValue({ [key]: data?.value });
    }
  }

  /**
   * dueDateTimeDistance值的修改
   */
  handlePatch(key, data) {
    this.dataFormGroup.patchValue({
      dueDateTimeDistance: {
        [key]: data?.value,
      },
    });
  }

  handleChangeMerge(merge) {
    if (merge) {
      this.dataFormGroup.addControl(
        'dueDateTimeDistance',
        this.fb.group({
          type: [this.data?.dueDateTimeDistance?.type || ''],
          startDate: [this.data?.dueDateTimeDistance?.startDate || ''],
          count: [this.data?.dueDateTimeDistance?.count || ''],
        }),
      );
    } else {
      this.dataFormGroup.removeControl('dueDateTimeDistance');
    }
  }

  /**
   * 处理扩展信息
   */
  handleExtendedInfo(event) {
    this.extendedInfoRef.handleOpenExtendedInfoModal(event);
  }

  // 默认task属性名称
  get customTaskName() {
    return {
      en_US: `${this?.data?.lang?.subjectRule?.['en_US'] ?? this.data?.processConfig?.subjectRule}`,
      zh_CN: `${this?.data?.lang?.subjectRule?.['zh_CN'] ?? this.data?.processConfig?.subjectRule}`,
      zh_TW: `${this?.data?.lang?.subjectRule?.['zh_TW'] ?? this.data?.processConfig?.subjectRule}`,
    };
  }

  // 自定义任务弹窗的title
  get customTaskTitle() {
    const app = this.appService?.selectedApp;
    return {
      en_US: `${app?.lang?.name?.['en_US'] ?? app?.name}-${this.customTaskName['en_US']}`,
      zh_CN: `${app?.lang?.name?.['zh_CN'] ?? app?.name}-${this.customTaskName['zh_CN']}`,
      zh_TW: `${app?.lang?.name?.['zh_TW'] ?? app?.name}-${this.customTaskName['zh_TW']}`,
    };
  }

  handleEdit() {
    const title = this.customTaskTitle;
    this.workData = {
      title, // 解决方案名称 + 作业名
      code: this.data?.projectCode ?? this.data?.processId,
      category: 'Activity',
      name: this.data.processConfig?.subjectRule ?? this.data.processName,
      project: this.data?.projectCode ?? this.data?.processId,
      data: {
        ...this.data,
        taskPattern: this.data?.extendFields?.taskPattern,
        executeType: 'user',
        id: this.data?.processId,
      },
      processId: this.data?.processId,
      nodeType: this.data?._nodeType,
    };
    this.workData.data.extendFields = {
      ...(this.workData.data.extendFields ?? {}),
      pageCode: this.data?.projectCode ?? this.data?.processId,
      pattern: 'BUSINESS',
    };
    this.workVisible = true;
  }
  /**
   * 处理任务设计器关闭
   */
  handleWorkPageClose(success) {
    if (success) {
      this.getCurrentData();
    }
    this.workData = null;
    this.workVisible = false;
  }

  /**
   * 处理dueDateTimeDistance
   */
  handleDueDateTimeDistance(currentData) {
    let backData: any = null;
    if (currentData.dueDateTimeDistance?.type) {
      backData = {};
      const { type, startDate, count } = currentData.dueDateTimeDistance;
      backData.type = type;
      if (type === 'MULTI_DAY') {
        backData.startDate = startDate ? dayjs(startDate).format('YYYY-MM-DD') : startDate;
        backData.count = count;
      }
      if (type === 'WEEK_DAY') {
        backData.timeDistanceBegin = '1';
        backData.timeDistanceEnd = '7';
        backData.interval = 'WEEK';
      }
      if (type === 'DAY') {
        backData.timeDistanceBegin = '1';
        backData.timeDistanceEnd = '1';
        backData.interval = 'DAY';
      }
    }
    //后端会默认给format， 前端用不到，但是为了检验通过
    if (this.data.dueDateTimeDistance?.format) {
      if (Object.keys(backData || {}).length === 0) {
        backData = {};
      }
      backData.format = this.data.dueDateTimeDistance.format;
    }
    return backData;
  }

  /**
   * 拆分出来此方法支持 当前组件+父组件调用
   * @returns
   */
  getFormValidate() {
    this.formGroupValidityFlag = true;
    for (const i of Object.keys(this.dataFormGroup?.controls)) {
      this.dataFormGroup.controls[i].markAsDirty();
      this.dataFormGroup.controls[i].updateValueAndValidity();
    }
    const deepValidateKey = ['subjectRule', 'sourceName', 'enableCustomProjectCard'];
    for (let i of deepValidateKey) {
      this.dataFormGroup.get('processConfig').get(i).markAsDirty();
      this.dataFormGroup.get('processConfig').get(i).updateValueAndValidity();
    }
    const planEndTime = this.dataFormGroup.get('planEndTime') as FormGroup;
    for (const i of Object.keys(planEndTime.controls)) {
      planEndTime.controls[i].markAsDirty();
      planEndTime.controls[i].updateValueAndValidity();
    }
    this.formGroupValidityFlag = false;
    return this.dataFormGroup.valid;
  }

  // 获取当前最新的数据
  getCurrentData() {
    this.getFormValidate();
    let currentData = this.dataFormGroup.value;
    let isVerificationPassed = this.dataFormGroup.valid;
    if (isVerificationPassed) {
      if (this.personInCharge?.type !== 'default') {
        // 签核人设置是否符合
        if (this.personInCharge?.source === 'personnel') {
          if (size(this.personInCharge?.personnel) === 0) {
            isVerificationPassed = false;
          }
        } else if (this.personInCharge?.source === 'variable') {
          if (size(this.personInCharge?.variable) === 0) {
            isVerificationPassed = false;
          }
        } else if (this.personInCharge?.source === 'activity') {
          if (size(this.personInCharge?.activity) === 0) {
            isVerificationPassed = false;
          }
        } else if (this.personInCharge?.source === 'formFiled') {
          if (size(this.personInCharge?.formFiled) === 0) {
            isVerificationPassed = false;
          }
        } else if (this.personInCharge?.source === 'department') {
          if (size(this.personInCharge?.department) === 0) {
            isVerificationPassed = false;
          }
        }
      }
    }
    const dueDateTimeDistance = this.handleDueDateTimeDistance(currentData);
    currentData.dueDateTimeDistance = dueDateTimeDistance;

    currentData.personInCharge = this.personInCharge;

    if (currentData.planEndTime.sourceValue) {
      currentData.planEndTime.sourceValue = dayjs(currentData.planEndTime.sourceValue).format('YYYY-MM-DD HH:mm:ss');
    }

    if (currentData.planEndTime.settingType === 'specify' && currentData.planEndTime.sourceType === 'variable') {
      currentData.planEndTime = {
        ...currentData.planEndTime,
        ...this.sourceData,
      };
    }

    const {
      propertiesObj,
      originalFlowData: { code },
    } = this.viewStoreService.state;
    const process = propertiesObj?.[code];
    const { dtdVariable, nodeVariables, mechanismVariables, businessObjectVariables, customVariables } = process;

    const returnData = {
      data: Object.assign(this.data, currentData, {
        lang: this.lang,
        dtdVariable,
        nodeVariables,
        mechanismVariables,
        businessObjectVariables,
        customVariables,
      }),
      valid: isVerificationPassed,
      componentType: 'projectCard',
    };

    this.changeData.emit(returnData);
    return this.data;
  }

  setSourceType(sourceType = 'constant', planEndTime) {
    const { sourceValue, variableCode, variablePath, variableSuffix, variableType } = planEndTime || {};
    if (sourceType === 'constant') {
      const planEndTime = this.dataFormGroup.get('planEndTime') as FormGroup;
      planEndTime.addControl('sourceType', new FormControl('constant', [Validators.required]));
      planEndTime.addControl('sourceValue', new FormControl(sourceValue ?? '', [Validators.required]));
      this.sourceData = {};
      planEndTime.removeControl('variableCode');
    }
    if (sourceType === 'variable') {
      const planEndTime = this.dataFormGroup.get('planEndTime') as FormGroup;
      planEndTime.addControl('sourceType', new FormControl('variable', [Validators.required]));
      planEndTime.addControl('variableCode', new FormControl(variableCode || '', [Validators.required]));
      this.sourceData = {
        variableCode: variableCode ?? '',
        variablePath: variablePath ?? '',
        variableSuffix: variableSuffix ?? '',
        variableType: variableType ?? '',
      };
      planEndTime.removeControl('sourceValue');
    }
  }

  /**
   * 修改字段类型
   * @param type
   * @param i
   */
  changeParamType(type) {
    const planEndTime = this.dataFormGroup.get('planEndTime') as FormGroup;
    if (planEndTime.get('sourceType').value === type) return;
    planEndTime.patchValue({
      sourceType: type,
    });

    this.setSourceType(type, this.data.planEndTime);
  }

  handleValueChanged(event: any): void {
    const { valueType, variableDesc, variableCode, valueCode, variableSuffix } = event;
    const type = variableTypeToDataType(valueType);
    const isModel = ['businessObjectVariable', 'modelVariable'].includes(type);
    const planEndTime = this.dataFormGroup.get('planEndTime') as FormGroup;
    planEndTime.patchValue({
      variableCode: valueCode || '',
    });
    this.sourceData = {
      variableCode: valueCode,
      variablePath: isModel ? variableCode : '',
      variableSuffix: variableSuffix,
      variableType: type,
    };
    this.getCurrentData();
  }

  setPlanEndTime(settingType) {
    const { dateValue, dateType, sourceType } = this.planEndTime || {};
    if (!settingType || settingType === 'no') {
      const planEndTime = this.dataFormGroup.get('planEndTime') as FormGroup;
      planEndTime.removeControl('dateValue');
      planEndTime.removeControl('dateType');
      planEndTime.removeControl('sourceType');
      planEndTime.removeControl('sourceValue');
      planEndTime.removeControl('variableCode');
    }
    if (settingType === 'calculation') {
      const planEndTime = this.dataFormGroup.get('planEndTime') as FormGroup;
      planEndTime.addControl('dateValue', new FormControl(dateValue ?? '', [Validators.required]));
      planEndTime.addControl('dateType', new FormControl(dateType ?? '', [Validators.required]));
      planEndTime.removeControl('sourceType');
      planEndTime.removeControl('sourceValue');
      planEndTime.removeControl('variableCode');
    }
    if (settingType === 'specify') {
      const planEndTime = this.dataFormGroup.get('planEndTime') as FormGroup;
      this.setSourceType(sourceType, this.planEndTime);
      planEndTime.removeControl('dateValue');
      planEndTime.removeControl('dateType');
    }
  }

  handlePeopleSettingChange(data) {
    const { executor } = data;
    this.personInCharge = executor;
    this.getCurrentData();
  }
}
