import { Component, EventEmitter, Input, OnInit, Output, SimpleChanges, ViewChild } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { QueryData } from 'components/page-design/entries/data-view/components/data-view-container/components/data-view-right/components/data-view-filter/data-view-filter-content/advanced-query.type';
import { GetDataService } from './get-multData-property.service';
import { NzMessageService } from 'ng-zorro-antd/message';
import { FormBuilder, FormControl, ValidatorFn, Validators } from '@angular/forms';
import { NameValidators } from '../../../config/utils';
import { cloneDeep, isEmpty, size } from 'lodash';
import { validatorForm } from 'common/utils/core.utils';
import { DataViewFilterComponent } from 'components/page-design/entries/data-view/components/data-view-container/components/data-view-right/components/data-view-filter/data-view-filter.component';
import { ViewStoreService } from '../../../service/store.service';
import { LocaleService } from 'common/service/locale.service';
import { ViewApiService } from '../../../service/api.service';
import { AppService } from 'pages/apps/app.service';
import { ActivatedRoute } from '@angular/router';

@Component({
  selector: 'app-get-multData-property',
  templateUrl: './get-multData-property.component.html',
  styleUrls: ['./get-multData-property.component.less'],
  providers: [GetDataService],
})
export class GetMultDataPropertyComponent implements OnInit {
  // 输入的渲染数据
  @Input() data;
  @Input() nodeId;
  // 通知父组件关闭属性面板
  @Output() close: EventEmitter<any> = new EventEmitter();
  // 表单数据改变通过校验后向父组件通知更新数据
  @Output() changeData: EventEmitter<any> = new EventEmitter();
  errorTips: Record<string, Record<string, string>> = {
    default: {
      required: this.translate.instant('dj-必填'),
      pattern: this.translate.instant('dj-超长'),
    },
  };
  dataFormGroup;
  formGroupValidityFlag = false;
  // 多语言单独处理，name
  nameLang: any;
  businessConstructorList: any = []; // 业务对象下拉

  allFields: any = [];
  currentLang;
  // 这个是自己界面使用的
  conditionList: QueryData[] = [];
  // 这个是平台使用的
  queryConditions = [];
  preGetNodesMode: any = [];
  businessDataViewList: any = [];
  businessCode: string = '';

  dataViewFilterError: boolean = false;
  constructor(
    private translate: TranslateService,
    private getDataService: GetDataService,
    private message: NzMessageService,
    private fb: FormBuilder,
    private viewStoreService: ViewStoreService,
    private language: LocaleService,
    public viewApiService: ViewApiService,
    public appService: AppService,
    public route: ActivatedRoute,
  ) {
    this.currentLang = this.translate.currentLang;
    this.route.paramMap.subscribe((params) => {
      this.businessCode = params.get('businessObjectId');
    });
  }

  ngOnInit(): void {
    this.handleInit();
    this.getBCList();
    this.getQueryPlanList();
    this.getCurrentData();
    this.dataFormGroup.valueChanges.subscribe(() => {
      if (!this.formGroupValidityFlag) {
        this.getCurrentData();
      }
    });
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (Object.keys(changes).includes('nodeId')) {
      if (!changes.nodeId.firstChange) {
        this.handleChangeValue();
      }
    }
  }

  getBCList() {
    this.getDataService.getBusinessDirList().subscribe((data) => {
      const trees = data.data || [];
      const _modelList: any[] = [];
      trees.forEach((item) => {
        const models = item.businessDirTree.find((e) => e.type === 'modelDesign')?.businessDirTree || [];
        models.forEach((model) => {
          _modelList.push({
            label: item.isOther
              ? model.lang?.modelName?.[this.translate.instant('dj-LANG')] || model.businessSubName
              : item.lang?.name?.[this.translate.instant('dj-LANG')] || item.businessName,
            modelCode: model.businessSubCode,
            serviceCode: model.serviceCode,
            value: `${item.businessCode}&${model.businessSubCode}&${model.serviceCode}`,
            groupLabel: item.isOther ? this.translate.instant('dj-其他') : undefined,
          });
        });
      });
      this.businessConstructorList = _modelList;
    });
  }

  getQueryPlanList() {
    const language = this.language?.currentLanguage || 'zh_CN';
    const params = {
      application: this.appService?.selectedApp?.code,
      businessCode: this.businessCode,
    };
    this.viewApiService.queryQueryplanList(params).subscribe(
      (res) => {
        if (res.code === 0) {
          this.businessDataViewList = (res.data?.businessDataViewList ?? []).map((businessDateView) => {
            return {
              value: businessDateView.businessCode,
              label: businessDateView.lang?.['businessName']?.[language] ?? businessDateView.businessName,
              children: businessDateView.dataViewList.map((dataView) => {
                return {
                  value: dataView.code,
                  label: dataView.lang?.['name']?.[language] ?? dataView.name,
                  isLeaf: true,
                };
              }),
            };
          });
        }
      },
      () => {},
    );
  }

  /**
   * 切换相同节点类型的不同节点
   */
  handleChangeValue() {
    this.formGroupValidityFlag = true;
    const formCode = this.data?.bindForm?.formCode
      ? `${this.data?.bindForm?.formCode}&${this.data?.bindForm?.modelCode}&${this.data?.bindForm?.serviceCode}`
      : undefined;
    this.dataFormGroup.patchValue({
      id: this.data?.id,
      name: this.data?.name,
      dataSource: this.data?.dataSource,
      orderRule: this.data?.orderRule,
      dataFilter: this.data?.dataFilter,
      bindForm: {
        formCode: formCode,
        modelCode: this.data?.bindForm?.modelCode,
        serviceCode: this.data?.bindForm?.serviceCode,
      },
      nodeList: this.data?.nodeList?.[0] ?? '',
      prod: this.data?.prod ?? null,
      dataView: this.data?.dataView ?? null,
    });
    this.handleDefaultInit();
    this.formGroupValidityFlag = false;
    this.getCurrentData();
  }

  handleClosePanel(): void {
    this.close.emit();
  }

  /**
   * 初始化dataFormGroup
   */
  handleInit(): void {
    this.dataFormGroup = this.fb.group({
      id: [this.data?.id || null, [Validators.required]],
      name: [this.data?.name || '', [NameValidators.trim, Validators.required, Validators.pattern('^.{0,255}$')]],
      dataSource: [this.data?.dataSource || null, [Validators.required]], //数据来源
      orderRule: [this.data?.orderRule || null, [Validators.required]], // 排序规则
    });
    this.handleDefaultInit();
    this.handleFormDepend();
    this.dataFormGroup.get('dataSource').valueChanges.subscribe((value) => {
      if (!this.formGroupValidityFlag) {
        this.handleDataSourceChange(value);
      }
    });
  }

  /**
   * watch表单某些值改变
   */
  handleFormDepend() {
    this.dataFormGroup
      .get('bindForm')
      ?.get('formCode')
      ?.valueChanges?.subscribe((value) => {
        if (!this.formGroupValidityFlag) {
          this.handleChangeFormCode(value);
        }
      });
  }

  handleDefaultInit() {
    this.nameLang = this.data.lang;
    if (this.data?.bindForm.formCode) {
      this.loadDataViewFields(this.data?.bindForm);
    }
    this.handleDataSourceChange(this.data?.dataSource);
    this.conditionList = cloneDeep(this.data?.conditionList || []);
    this.queryConditions = cloneDeep(this.data?.queryConditions || []);
    this.preGetNodesMode = cloneDeep(this.data?.preGetNodesMode || []);
  }

  handlePatchApp(key: any, data: any): void {
    if (data.needLang) {
      this.nameLang = {
        ...(this.nameLang || {}),
        [key]: data.lang,
      };
    }
    this.dataFormGroup.patchValue({
      [key]: data?.value,
    });
  }

  /**
   * bindForm.formCode改变
   * @param code
   */
  handleChangeFormCode(code) {
    const modelItem = this.businessConstructorList.find((data) => data.value === code);
    if (!isEmpty(modelItem)) {
      const modalData = {
        modelCode: modelItem.modelCode,
        serviceCode: modelItem.serviceCode,
      };
      this.dataFormGroup.get('bindForm').patchValue(modalData);
      this.loadDataViewFields(modalData);
    } else {
      this.dataFormGroup.get('bindForm')?.patchValue({
        modelCode: '',
        serviceCode: '',
      });
    }
    // 之前配置的筛选器清空
    this.conditionList = [];
    this.queryConditions = [];
  }

  /**
   * dataSource 改变
   * 数据来源 business 业务对象 node数据节点
   */
  handleDataSourceChange(code) {
    if (code === 'business') {
      const formCode = this.data?.bindForm?.formCode
        ? `${this.data?.bindForm?.formCode}&${this.data?.bindForm?.modelCode}&${this.data?.bindForm?.serviceCode}`
        : undefined;
      this.dataFormGroup.addControl(
        'bindForm',
        this.fb.group({
          formCode: [formCode, [Validators.required]],
          modelCode: [this.data?.bindForm?.modelCode],
          serviceCode: [this.data?.bindForm?.serviceCode],
        }),
      );
      this.dataFormGroup.addControl('dataFilter', new FormControl(this.data?.dataFilter, [Validators.required]));
      this.dataFormGroup.removeControl('nodeList');
      this.dataFormGroup.removeControl('prod');
      this.dataFormGroup.removeControl('dataView');
      this.handleFormDepend();
    } else if (code === 'node') {
      const nodeList = Array.isArray(this.data?.nodeList) ? this.data?.nodeList?.[0] : this.data?.nodeList;
      this.dataFormGroup.addControl('nodeList', new FormControl(nodeList, [Validators.required]));
      this.dataFormGroup.removeControl('bindForm');
      this.dataFormGroup.removeControl('dataFilter');
      this.dataFormGroup.removeControl('prod');
      this.dataFormGroup.removeControl('dataView');
      this.conditionList = [];
      this.queryConditions = [];
    } else if (code === 'queryPlan') {
      const prod = this.data?.prod;
      const dataView = this.data?.dataView;
      this.dataFormGroup.addControl('prod', new FormControl(prod, [Validators.required]));
      this.dataFormGroup.addControl('dataView', new FormControl(dataView, [Validators.required]));
      this.dataFormGroup.removeControl('bindForm');
      this.dataFormGroup.removeControl('dataFilter');
      this.dataFormGroup.removeControl('nodeList');
    }
  }

  /**
   * 查询模型数据
   * @param param
   */
  async loadDataViewFields(param) {
    if (param && param.modelCode) {
      const res = await this.getDataService.queryDataViewFields(param).toPromise();
      this.allFields = res.data?.mainField || [];
    }
  }

  /**
   * 筛选器back
   * @param e
   */
  handleDataViewFilterChange(e) {
    this.conditionList = e.conditionList;
    this.queryConditions = e.queryConditions;
    this.getCurrentData();
  }

  /**
   * 条件的校验规则
   */
  getConditionPassed(data) {
    let childPassed = true;
    if (size(data.conditionList) == 0) {
      childPassed = false;
      this.dataViewFilterError = true;
    } else {
      this.dataViewFilterError = false;
      childPassed = true;
    }
    return childPassed;
  }

  // 获取当前最新的数据
  getCurrentData() {
    this.formGroupValidityFlag = true;
    validatorForm(this.dataFormGroup);
    this.formGroupValidityFlag = false;
    const currentData = this.dataFormGroup.getRawValue();
    if (currentData?.bindForm?.formCode) {
      const [businessCode, modelCode, serviceCode] = currentData.bindForm.formCode.split('&');
      currentData.bindForm.formCode = businessCode;
      currentData.bindForm.modelCode = modelCode;
      currentData.bindForm.serviceCode = serviceCode;
    }
    let data = Object.assign(this.data, currentData, { lang: this.nameLang });
    data.conditionList = this.conditionList;
    data.queryConditions = this.queryConditions;
    let childPassed = true;
    if (data.dataSource === 'business') {
      if (data.dataFilter === 'condition') {
        childPassed = this.getConditionPassed(data);
      }
      data.nodeList = [];
      data.prod = null;
      data.dataView = null;
    } else if (data.dataSource === 'node') {
      data.nodeList = data.nodeList && [data.nodeList];
      data.prod = null;
      data.dataView = null;
    } else if (data.dataSource === 'queryPlan') {
      data.nodeList = [];
    }

    // 混入isVerificationPassed 是否校验通过
    let isVerificationPassed = this.viewStoreService.transformVerificationPassed(
      this.dataFormGroup.valid && childPassed,
    );
    const returnData = {
      data: data,
      isVerificationPassed,
    };
    this.changeData.emit(returnData);
    return returnData;
  }
}
