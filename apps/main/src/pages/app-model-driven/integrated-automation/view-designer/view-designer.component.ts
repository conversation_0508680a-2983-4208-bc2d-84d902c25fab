import {
  Component,
  OnInit,
  After<PERSON><PERSON>w<PERSON>nit,
  <PERSON><PERSON><PERSON><PERSON>,
  HostListener,
  Input,
  TemplateRef,
  SimpleChanges,
  ViewChild,
  Output,
  EventEmitter,
} from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { Node } from '@antv/x6';
import { NzMessageService } from 'ng-zorro-antd/message';
import { ViewToolsService } from './service/tools.service';
import { ViewGraphService } from './service/graph.service';
import { ViewStoreService } from './service/store.service';
import { ViewApiService } from './service/api.service';
import { ModelNodes, NodeType, PannelTabsType } from './config/typings';
import { handleQueryFields, getSearchOperatorLabel, handleConditionList } from './config/utils';
import { takeUntil } from 'rxjs/operators';
import { Subject, Subscription } from 'rxjs';
import { isEmpty, cloneDeep, isEqual, omit, isObject, isNil } from 'lodash';
import { isJSON } from 'common/utils/core.utils';
import { VariableService } from './service/variable.service';
import { ActivatedRoute } from '@angular/router';
import { omitFrontData } from 'pages/app-model-driven/utils/utils';

@Component({
  selector: 'app-view-designer',
  templateUrl: './view-designer.component.html',
  styleUrls: ['./view-designer.component.less'],
})
export class ViewDesignerComponent implements OnInit, AfterViewInit, OnDestroy {
  @Input() flowDesignerCode = ''; // （必须）流程 code
  @Input() headerCustomTemplate: TemplateRef<any> | null = null; // （可选）定制头部（头部的左侧部分）
  @Input() refreshMenuAndCrumbsName: boolean = false;
  @Input() isHideSaveAndPublish: boolean = false; // 是否隐藏保存和发布按钮

  @Output() updateBreadcrumb: EventEmitter<any> = new EventEmitter(); // 更新面包屑
  @Output() saveAndPublishLoadingChange: EventEmitter<boolean> = new EventEmitter(); // 当保存和发布状态变化时触发的事件，配合优化需求，保存和发布时模型驱动解决方案左侧菜单不可点击
  @Output() doNotCheckStateChange: EventEmitter<boolean> = new EventEmitter(); // 当A流程没有记载完成时，不去校验流程状态变化，防止报错

  destroy$ = new Subject();

  NodeType = NodeType;

  isShowAlert: boolean = false;
  currentPropertiesPanel: any = null;
  saveAndPublishLoadingChange$: Subscription; // 保存和发布loading状态变化
  descriptionLang; // 项目描述

  queryUiKey: string = null;

  get graph() {
    return this.viewGraphService.graph;
  }

  @ViewChild('viewDesignerGraphRef', { static: false }) viewDesignerGraphRef: any;

  constructor(
    private translateService: TranslateService,
    private message: NzMessageService,
    public viewGraphService: ViewGraphService,
    public viewToolsService: ViewToolsService,
    public viewStoreService: ViewStoreService,
    public viewApiService: ViewApiService,
    private variableService: VariableService,
    public route: ActivatedRoute,
  ) {
    this.route.queryParams.pipe(takeUntil(this.destroy$)).subscribe((params) => {
      if (params['adpVersion']) {
        this.viewApiService.headers = {
          adpVersion: params['adpVersion'],
          adpStatus: params['adpStatus'],
        };
      }
      if (params['tenantProcessId']) {
        this.viewApiService.tenantHeaders = {
          tenantProcessId: params['tenantProcessId'],
          level: 'tenant',
        };
      }

      // 项目分类 combined：组合项目，single：单一项目
      if (params['projectCategory']) {
        this.viewStoreService.setState((state) => {
          state.projectCategory = params.projectCategory;
        });
      }
      // 是否是引用的dtd
      if ('isFromDtdReference' in params) {
        this.viewStoreService.setState((state) => {
          state.isFromDtdReference = params['isFromDtdReference'] === 'true';
        });
      } else {
        this.viewStoreService.setState((state) => {
          state.isFromDtdReference = false;
        });
      }
    });

    this.viewGraphService.isShowAlert$.pipe(takeUntil(this.destroy$)).subscribe((res: boolean) => {
      this.isShowAlert = res;
    });
    this.viewGraphService.isShowPropertiesPanel$.pipe(takeUntil(this.destroy$)).subscribe((res: any) => {
      const { isShow, propertiesPanel } = res || {};
      if (isShow) {
        const { propertiesObj, originalFlowData } = this.viewStoreService.state;
        const {
          nodeId,
          nodeType,
          preManualNodes,
          conditionType,
          conditionNodesLevel,
          preManualNodesMode,
          preGetNodesMode,
          parallelType,
          preModelNodes,
        } = propertiesPanel || {};

        if (!nodeId || !propertiesObj?.[nodeId]) return {};

        let nodeConfig = {
          ...propertiesObj?.[nodeId],
          processId: originalFlowData.processId,
        };

        switch (nodeType) {
          case NodeType.GLOBAL_SETTING:
          case NodeType.START_EVENT:
          case NodeType.START_MANUAL:
          case NodeType.START_TIMER:
          case NodeType.END_FLOW:
            break;
          case NodeType.CONDITION_BRANCH:
            nodeConfig = {
              ...nodeConfig,
              conditionType: conditionType,
              conditionNodesLevel: conditionNodesLevel,
              preManualNodesMode: preManualNodesMode,
            };
            break;
          case NodeType.PARALLEL_BRANCH:
            nodeConfig = {
              ...nodeConfig,
              parallelType,
              conditionNodesLevel,
              preManualNodesMode,
            };
            break;
          case NodeType.MANUAL_APPROVE:
          case NodeType.MANUAL_EXECUTION:
          case NodeType.MANUAL_NOTIFICATION:
            nodeConfig = {
              ...nodeConfig,
              preManualNodes: preManualNodes,
            };
            break;
          case NodeType.AUTO_HTTP:
          case NodeType.AUTO_ESP:
          case NodeType.AUTO_CALL_EVENT:
          case NodeType.AUTO_NOTIFY:
          case NodeType.AUTO_SCRIPT:
          case NodeType.AUTO_SUB_PROCESS:
          case NodeType.MSG_SEND:
          case NodeType.PARALLEL_BRANCH_START:
            nodeConfig = {
              ...nodeConfig,
            };
            break;
          case NodeType.DATA_ADD:
          case NodeType.DATA_UPDATE:
            nodeConfig = {
              ...nodeConfig,
              preModelNodes,
            };
            break;
          case NodeType.DATA_GET:
          case NodeType.DATA_GET_MULTI:
          case NodeType.DATA_DELETE:
          case NodeType.DATA_GROUPING:
            nodeConfig = {
              ...nodeConfig,
              preGetNodesMode,
            };
            break;
          case NodeType.START_PROJECT:
            nodeConfig = {
              ...nodeConfig,
              preGetNodesMode,
              descriptionLang: this.descriptionLang || '',
            };
            break;
          default:
            break;
        }
        this.currentPropertiesPanel = nodeConfig;
      } else {
        this.currentPropertiesPanel = null;
      }
    });
    this.saveAndPublishLoadingChange$ = this.viewGraphService.saveAndPublishLoadingChange$
      .pipe(takeUntil(this.destroy$))
      .subscribe((isLoading: boolean) => {
        this.saveAndPublishLoadingChange.emit(isLoading);
      });
    this.viewGraphService.adpVersion$.pipe(takeUntil(this.destroy$)).subscribe((data: any) => {
      this.viewApiService.headers = {
        adpVersion: data.adpVersion,
        adpStatus: data.adpStatus,
      };
      this.viewToolsService.toolsInit(this.viewGraphService.graph);
      this.viewGraphService.isShowPropertiesPanel$.next({
        isShow: false,
        propertiesPanel: null,
      });
      this.viewDesignerGraphRef.getFlowData();
    });

    this.viewGraphService.doNotCheckStateChange$.pipe(takeUntil(this.destroy$)).subscribe((data: boolean) => {
      this.doNotCheckStateChange.emit(data);
    });
  }

  @HostListener('document:mousedown', ['$event'])
  onDocumentClick() {
    this.viewGraphService.handleCloseNodeMenu();
  }

  @HostListener('window:resize', ['$event'])
  onResize(event?) {
    const width = window.innerWidth;
    const height = window.innerHeight;
    this.graph?.resize(width, height);
  }

  ngOnInit() {
    this.handleClosePropertiesPanel();
    // 默认关闭节点菜单
    this.onDocumentClick();
    this.onResize();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (Object.keys(changes).includes('flowDesignerCode')) {
      if (
        changes.flowDesignerCode.currentValue &&
        changes.flowDesignerCode.previousValue &&
        changes.flowDesignerCode.currentValue !== changes.flowDesignerCode.previousValue
      ) {
        this.viewToolsService.toolsInit(this.viewGraphService.graph);
        this.viewGraphService.isShowPropertiesPanel$.next({
          isShow: false,
          propertiesPanel: null,
        });
        this.viewApiService.headers = null;
        this.viewDesignerGraphRef.getFlowData();
      }
      // 清空辅助的缓存数据，key nodeId+bindForm.formCode 标识是否需要调用后台的差异化数据，为true表示已手动修改过无需再次调用
      this.viewStoreService.setState((state) => {
        state.hasInitFlag = {};
      });
    }
  }

  ngAfterViewInit() {
    this.route.queryParams.pipe(takeUntil(this.destroy$)).subscribe((params: any) => {
      const { uiKey } = params;
      this.queryUiKey = uiKey;
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    this.viewApiService.headers = null;
  }

  // 控制属性面板隐藏
  handleClosePropertiesPanel(): void {
    this.viewGraphService.isShowPropertiesPanel$.next({
      isShow: false,
      propertiesPanel: null,
    });
  }

  // 右侧面板数据改变
  handleChangePropertiesData(config) {
    const { data, isVerificationPassed } = config || {};
    const {
      name,
      allFields,
      conditionList,
      conditionMode = 'UI',
      queryConditionScript,
      conditionSortList,
      triggerConfig,
    } = data || {};
    const code = this.viewStoreService.state?.originalFlowData.code;

    if (!data) return;

    const { _nodeId, _nodeType } = this.currentPropertiesPanel || {};
    const modelVariables = this.handlePropertiesDataChange(data);
    if (_nodeType === NodeType.GLOBAL_SETTING) {
      this.descriptionLang = data?.lang?.projectDescription || '';
    }
    // 节点属性
    this.viewStoreService.setState((state) => {
      state.propertiesObj[_nodeId] = {
        ...data,
        actions: state.propertiesObj[_nodeId].actions,
        variablesMapping: state.propertiesObj[_nodeId].variablesMapping,
        delayConfig: state.propertiesObj[_nodeId].delayConfig,
        _isValidPassed: isJSON(isVerificationPassed)
          ? isVerificationPassed
          : { [PannelTabsType.BASE]: isVerificationPassed },
        _nodeId: _nodeId,
        _nodeType: _nodeType,
      };
    });

    if (ModelNodes.includes(_nodeType)) {
      this.viewStoreService.setState((state) => {
        state.propertiesObj[code].modelVariables = modelVariables;
      });
    }

    // 通知变量service，节点变量更新

    if (_nodeType !== NodeType.GLOBAL_SETTING) {
      const allNodes = this.graph.getNodes();
      const currentNode: Node = allNodes.find((node) => node.id === _nodeId);

      // 异常问题处理
      if (!currentNode) {
        this.message.error(this.translateService.instant('dj-未匹配到节点，请删除该节点重新配置'));
        return;
      }

      currentNode.attr('label/text', name || null);
      // 条件分支节点
      if (_nodeType === NodeType.CONDITION_BRANCH) {
        if (conditionMode === 'UI') {
          if (conditionList?.length) {
            const { queryGroup } = conditionList?.[0] || {};
            const { queryField, searchOperator, searchValue } = queryGroup?.[0] || {};
            const text = `${queryField.searchName} ${getSearchOperatorLabel(
              searchOperator,
              this.translateService,
            )} ${searchValue} ${conditionList?.length > 1 || queryGroup?.length > 1 ? '...' : ''}`;
            currentNode.setAttrByPath('condition/text', text);
            currentNode.setAttrByPath('condition/fill', '#666666');
          } else {
            currentNode.setAttrByPath('condition/text', this.translateService.instant(`dj-请设置条件`));
            currentNode.setAttrByPath('condition/fill', '#999999');
          }
        } else {
          if (queryConditionScript) {
            currentNode.setAttrByPath(
              'condition/text',
              queryConditionScript.length > 20 ? queryConditionScript.substr(0, 20) + '...' : queryConditionScript,
            );
            currentNode.setAttrByPath('condition/fill', '#666666');
          } else {
            currentNode.setAttrByPath('condition/text', this.translateService.instant(`dj-请设置条件`));
            currentNode.setAttrByPath('condition/fill', '#999999');
          }
        }

        const queryFields = handleQueryFields(allFields || [], this.translateService.currentLang || 'zh_CN');
        const condition = handleConditionList(conditionList);
        currentNode.setData({
          ...(currentNode?.data || {}),
          nodeName: name,
          isVerificationPassed,
          condition: JSON.stringify(condition || []),
          queryFields: JSON.stringify(queryFields || []),
        });
        conditionSortList.forEach((list) => {
          this.viewStoreService.setState((state) => {
            state.propertiesObj[list.nodeId] = {
              ...state.propertiesObj[list.nodeId],
              sort: list.sort,
            };
          });

          const node = this.graph.getCellById(list.nodeId);
          const branchNodeId = node.id;
          const levelName = this.translateService.instant(`dj-优先级`) + list.sort;
          node.setAttrByPath('level/text', levelName);
          node.setData({ ...(node?.data || {}), conditionSort: list.sort });

          // 条件节点到结束分支节点之间的节点，并设置序号
          const endBranchNodeId = node?.data?.conditionEndId;
          if (endBranchNodeId) {
            const endBranchNode = this.graph.getCellById(endBranchNodeId);
            const shortestNodeIds = this.graph.getShortestPath(node, endBranchNode);
            shortestNodeIds
              ?.filter((i) => ![branchNodeId, endBranchNodeId].includes(i))
              .map((j) => {
                const middleNode = this.graph.getCellById(j);
                middleNode.setData({ ...(middleNode?.data || {}), conditionSort: list.sort });
              });
          }
        });
      } else if (_nodeType === NodeType.START_EVENT) {
        this.viewStoreService.setState((state) => {
          state.originalFlowData = {
            ...(state.originalFlowData || {}),
            triggerConfig: {
              businessCode: triggerConfig?.businessCode,
              eventId: triggerConfig?.eventId,
            },
          };
        });

        currentNode.setData({
          ...(currentNode?.data || {}),
          nodeName: name,
          isVerificationPassed,
        });
      } else if (_nodeType === NodeType.PARALLEL_BRANCH) {
        if (conditionMode === 'UI') {
          if (conditionList?.length) {
            const { queryGroup } = conditionList?.[0] || {};
            const { queryField, searchOperator, searchValue } = queryGroup?.[0] || {};
            const text = `${queryField.searchName} ${getSearchOperatorLabel(
              searchOperator,
              this.translateService,
            )} ${searchValue} ${conditionList?.length > 1 || queryGroup?.length > 1 ? '...' : ''}`;
            currentNode.setAttrByPath('condition/text', text);
            currentNode.setAttrByPath('condition/fill', '#666666');
          } else {
            currentNode.setAttrByPath('condition/text', this.translateService.instant(`dj-请设置条件`));
            currentNode.setAttrByPath('condition/fill', '#999999');
          }
        } else {
          if (queryConditionScript) {
            currentNode.setAttrByPath(
              'condition/text',
              queryConditionScript.length > 20 ? queryConditionScript.substr(0, 20) + '...' : queryConditionScript,
            );
            currentNode.setAttrByPath('condition/fill', '#666666');
          } else {
            currentNode.setAttrByPath('condition/text', this.translateService.instant(`dj-请设置条件`));
            currentNode.setAttrByPath('condition/fill', '#999999');
          }
        }
        const queryFields = handleQueryFields(allFields || [], this.translateService.currentLang || 'zh_CN');
        const condition = handleConditionList(conditionList);
        currentNode.setData({
          ...(currentNode?.data || {}),
          nodeName: name,
          isVerificationPassed,
          condition: JSON.stringify(condition || []),
          queryFields: JSON.stringify(queryFields || []),
        });
      } else {
        currentNode.setData({
          ...(currentNode?.data || {}),
          nodeName: name,
          isVerificationPassed,
        });
      }
      this.viewToolsService.toolsNodeStyle(currentNode, 'highlight');
    }
  }

  /**
   * 在整体修改之前，对比数据变化，如果有改动更新节点变量和模型变量
   * @param data
   * @returns
   */
  handlePropertiesDataChange(data) {
    const { name, bindForm } = data || {};
    const { _nodeId, _nodeType } = this.currentPropertiesPanel || {};
    const oldData = this.viewStoreService.state?.propertiesObj?.[_nodeId];
    // 除去小齿轮的所有面板数据修改
    if (_nodeType !== NodeType.GLOBAL_SETTING && oldData?.name !== name) {
      this.variableService.handleChangeVariableData({
        type: 'update',
        item: {
          nodeId: data.id,
          _nodeId: data._nodeId,
          varName: data.name,
          name: data.name,
          dataType: 'Node',
        },
      });
    }
    // 含有模型的节点要更新模型变量
    if (ModelNodes.includes(_nodeType)) {
      return (
        this.viewGraphService.handleModelNodeVariable({
          processId: _nodeId,
          newBindForm: {
            modelCode: bindForm?.modelCode || '',
            serviceCode: bindForm?.serviceCode || '',
          },
        }) ?? null
      );
    }
    return null;
  }

  // 打开节点菜单
  toolBarOpenNodeMenu(e) {
    this.viewGraphService.handleOpenNodeMenu(e);
  }

  // 100% 视图
  toolBarOperateOriginalSize() {
    this.viewToolsService.toolsOriginalSize(this.graph);
  }

  // 放大
  toolBarOperateEnlarge() {
    this.viewToolsService.toolsEnlarge(this.graph);
  }

  // 缩小
  toolBarOperateReduce() {
    this.viewToolsService.toolsReduce(this.graph);
  }

  // 撤销
  toolBarOperateUndo() {
    this.viewToolsService.toolsUndo(this.graph);
  }

  // 重做
  toolBarOperateRedo() {
    this.viewToolsService.toolsRedo(this.graph);
  }

  setViewDesign() {
    this.viewToolsService.toolsClearAllNodesTools(this.graph);
    this.viewToolsService.toolsClearAllEdgesTools(this.graph);

    const { processId } = this.viewStoreService.state?.originalFlowData || {};
    this.viewGraphService.isShowPropertiesPanel$.next({
      isShow: true,
      propertiesPanel: {
        nodeId: processId,
        nodeType: NodeType.GLOBAL_SETTING,
      },
    });
  }

  // 更新父组件的面包屑
  handleUpdateBread(data: any): void {
    this.updateBreadcrumb.emit(data);
  }

  public checkContentChangeWithoutSave() {
    const params = this.viewGraphService.handleFlowDataParams(false);
    const originData = this.viewStoreService.state.originalFlowData;
    //去除掉前端使用的字段，不做校验
    const { _originData, _params } = omitFrontData(originData, params);
    const originClone = cloneDeep(_originData);
    const paramsClone = cloneDeep(_params);
    this.cleanNil(originClone);
    this.cleanNil(paramsClone);
    // console.log('diff', this.findDifferences(originClone, paramsClone));
    // 这里是因为项目中 _isValidPassed 的结构变了，从bool值变成了对象
    return !isEqual(originClone, paramsClone);
  }

  private cleanNil(obj: any): void {
    if (isObject(obj)) {
      for (var propName in obj) {
        if (isObject(obj[propName])) {
          this.cleanNil(obj[propName]);
        }
        if (
          obj[propName] === null ||
          obj[propName] === undefined ||
          (isObject(obj[propName]) && isEmpty(obj[propName]))
        ) {
          delete obj[propName];
        }
      }
    }
  }

  /**
   * 对比具体差异
   * @param obj1
   * @param obj2
   * @param path
   * @returns
   */
  findDifferences(obj1, obj2, path = '') {
    let differences = [];

    function addDifference(path, value1, value2) {
      differences.push({ path, value1, value2 });
    }

    function compare(obj1, obj2, path) {
      if (obj1 === obj2) return;

      if (typeof obj1 !== 'object' || obj1 === null || typeof obj2 !== 'object' || obj2 === null) {
        addDifference(path, obj1, obj2);
        return;
      }

      let keys1 = Object.keys(obj1);
      let keys2 = Object.keys(obj2);

      for (let key of keys1) {
        let newPath = path ? `${path}.${key}` : key;
        if (keys2.includes(key)) {
          compare(obj1[key], obj2[key], newPath);
        } else {
          addDifference(newPath, obj1[key], undefined);
        }
      }

      for (let key of keys2) {
        if (!keys1.includes(key)) {
          let newPath = path ? `${path}.${key}` : key;
          addDifference(newPath, undefined, obj2[key]);
        }
      }
    }

    compare(obj1, obj2, path);
    return differences;
  }

  handleReloadPage() {
    this.viewDesignerGraphRef?.getFlowData();
  }
}
