import { Graph, Shape } from '@antv/x6';
import { imgUrl } from './utils';
import { CellEditor } from './name-editor';
import { TooltipTool } from './node-tooltip';
import { NodeType } from './typings';

// 复制、删除图标
const copySvgPath =
  '<svg t="1700116655859" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="13540" width="200" height="200"><path d="M0 0m204.8 0l614.4 0q204.8 0 204.8 204.8l0 614.4q0 204.8-204.8 204.8l-614.4 0q-204.8 0-204.8-204.8l0-614.4q0-204.8 204.8-204.8Z" fill="#6868AE" p-id="13541"></path><path d="M665.6 292.5568H263.8848C231.2704 292.608 204.8 317.1328 204.8 347.4432v416.9216C204.8 794.5728 231.3216 819.2 263.8848 819.2H665.6c32.5632 0 59.0848-24.576 59.0848-54.8352V347.4432c0-30.208-26.5216-54.8864-59.0848-54.8864z m11.776 471.808c0 5.9904-5.2736 10.9568-11.776 10.9568H263.8848c-6.5536 0-11.776-4.9152-11.776-10.9568V347.4432c0-6.0928 5.2224-11.008 11.776-11.008H665.6c6.5024 0 11.776 4.9152 11.776 11.008v416.9216z" fill="#FFFFFF" p-id="13542"></path><path d="M760.1152 204.8H358.4c-13.056 0-23.6544 9.8304-23.6544 21.9648 0 12.0832 10.5984 21.9136 23.6544 21.9136h401.7152c6.5536 0 11.776 4.9152 11.776 10.9568v416.9216c0 12.1344 10.6496 21.9648 23.6544 21.9648 13.056 0 23.6544-9.8304 23.6544-21.9648V259.6352C819.2 229.4272 792.7296 204.8 760.1152 204.8z" fill="#FFFFFF" p-id="13543"></path><path d="M582.912 407.7568H346.5728c-13.056 0-23.6032 9.8304-23.6032 21.9648s10.5472 21.9136 23.6032 21.9136h236.3392c13.056 0 23.6032-9.7792 23.6032-21.9136s-10.5472-21.9648-23.6032-21.9648z m0 109.7216H346.5728c-13.056 0-23.6032 9.8304-23.6032 21.9648 0 12.0832 10.5472 21.9136 23.6032 21.9136h236.3392c13.056 0 23.6032-9.8304 23.6032-21.9136 0-12.1344-10.5472-21.9648-23.6032-21.9648z m-94.5664 108.8H346.624c-13.056 0-23.6032 9.8304-23.6032 21.9648 0 12.0832 10.5472 21.9136 23.6032 21.9136h141.824c13.0048 0 23.6032-9.8304 23.6032-21.9136 0-12.1344-10.5984-21.9648-23.6544-21.9648z" fill="#FFFFFF" p-id="13544"></path></svg>';
const deleteSvgPath =
  '<svg t="1700116580552" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="13390" width="200" height="200"><path d="M0 0m204.8 0l614.4 0q204.8 0 204.8 204.8l0 614.4q0 204.8-204.8 204.8l-614.4 0q-204.8 0-204.8-204.8l0-614.4q0-204.8 204.8-204.8Z" fill="#6868AE" p-id="13391"></path><path d="M684.3392 721.7152a37.4784 37.4784 0 0 1-37.376 37.4784h-269.824a37.4784 37.4784 0 0 1-37.4272-37.4784V324.3008h344.6272v397.4144zM414.6176 257.1264c0-4.3008 3.328-7.68 7.68-7.68h179.968c4.3008 0 7.68 3.3792 7.68 7.68v22.528H414.6176v-22.528z m382.0032 22.528h-142.08v-22.528c0-28.8256-23.4496-52.3264-52.2752-52.3264H422.2976a52.4288 52.4288 0 0 0-52.3264 52.3264v22.528H227.3792a22.528 22.528 0 0 0 0 45.1072h67.2256v396.9536a82.5344 82.5344 0 0 0 82.5856 82.5344h269.6704a82.5344 82.5344 0 0 0 82.5856-82.5344V324.3008h67.1744a22.528 22.528 0 0 0 22.5792-22.528 22.1184 22.1184 0 0 0-22.528-22.1184zM512 691.5072a22.528 22.528 0 0 0 22.5792-22.528V428.8512a22.528 22.528 0 1 0-45.1072 0v240.0256c0 12.4928 10.0864 22.528 22.528 22.528m-105.0624 0a22.528 22.528 0 0 0 22.528-22.528V428.9024a22.528 22.528 0 0 0-45.056 0v240.0256c0.4096 12.4928 10.496 22.528 22.528 22.528m210.176 0a22.528 22.528 0 0 0 22.6304-22.528V428.9024a22.5792 22.5792 0 0 0-45.1584 0v240.0256c0 12.4928 10.0864 22.528 22.528 22.528" fill="#FFFFFF" p-id="13392"></path></svg>';

// 连线配置
export const commonEdgeAttrs = {
  tools: {
    name: 'vertices',
    args: {
      // 小圆点样式
      attrs: {
        fill: '#A2B1C3',
        r: 3,
      },
    },
  },
  attrs: {
    line: {
      stroke: '#A2B1C3',
      strokeWidth: 1,
      targetMarker: {
        name: 'block',
        width: 6,
        height: 10,
      },
    },
  },
  connector: {
    name: 'rounded',
    args: {
      type: 'gap',
    },
  },
  router: {
    name: 'straightLine', // name: 'cornor',manhattan
    args: {
      startDirections: ['bottom'],
      endDirections: ['top'],
    },
  },
};

// 画布
export const flowsGraph = {
  grid: {
    size: 10,
    visible: true,
    type: 'doubleMesh',
    args: [
      {
        color: '#e6f7ff',
        thickness: 1,
      },
    ],
  },
  mousewheel: {
    enabled: true,
    zoomAtMousePosition: true,
    modifiers: 'ctrl',
    minScale: 0.5,
    maxScale: 2,
  },
  connecting: {
    snap: true,
    allowBlank: false,
    allowLoop: false,
    allowMulti: false,
    highlight: true,
    allowPort(this, args) {
      // 是否允许连接
      let flag = true;

      const {
        nodeType: targetNodeType,
        conditionStartId: targetConditionStartId,
        conditionEndId: targetConditionEndId,
      } = args?.targetCell?.data || {};

      const { conditionStartId: sourceConditionStartId, conditionEndId: sourceConditionEndId } =
        args?.sourceCell?.data || {};

      // 所有目标连接桩：已作为出线，不能再作为入线
      const outgoingEdges = this.getOutgoingEdges(args.targetCell);
      outgoingEdges?.forEach((edge) => {
        if (edge.source.port === args.targetPort) flag = false;
      });

      switch (targetNodeType) {
        case NodeType.START_FLOW:
        case NodeType.START_MANUAL:
        case NodeType.START_EVENT:
        case NodeType.START_TIMER:
          flag = false;
          break;
        case NodeType.CONDITION_BRANCH_START:
          if (targetConditionStartId === sourceConditionStartId) {
            flag = false;
          } else {
            const incomingEdges = this.getIncomingEdges(args.targetCell);
            flag = incomingEdges?.length ? false : flag;
          }
          break;
        default:
          const incomingEdges = this.getIncomingEdges(args.targetCell);
          flag = incomingEdges?.length ? false : flag;
          break;
      }

      return flag;
    },
    createEdge(args) {
      return new Shape.Edge({
        ...commonEdgeAttrs,
        attrs: {
          line: {
            stroke: '#6A4CFF',
            strokeWidth: 2,
            strokeDasharray: 5,
            targetMarker: {
              name: 'block',
              width: 6,
              height: 10,
            },
          },
        },
      });
    },
    validateConnection({ targetMagnet }) {
      return !!targetMagnet;
    },
  },
  highlighting: {
    magnetAdsorbed: {
      name: 'stroke',
      args: {
        attrs: {
          fill: '#5F95FF',
          stroke: '#5F95FF',
        },
      },
    },
  },
};

// 注册自定义 Node|Edge 工具
export const registerTools = () => {
  // 注册节点提示工具
  Graph.registerNodeTool('node-tooltip', TooltipTool, true);

  // 注册节点编辑节点名称工具
  Graph.registerNodeTool('node-text-editor', CellEditor, true);

  // 注册节点添加节点工具
  Graph.registerNodeTool(
    'node-add-node-button',
    {
      inherit: 'button',
      markup: [
        {
          tagName: 'circle',
          selector: 'button',
          attrs: {
            r: 7,
            stroke: '#6A4CFF',
            'stroke-width': 1,
            fill: 'white',
            cursor: 'pointer',
          },
        },
        {
          tagName: 'text',
          textContent: '+',
          selector: 'icon',
          attrs: {
            fill: '#6A4CFF',
            'font-size': 12,
            'text-anchor': 'middle',
            'pointer-events': 'none',
            y: '0.3em',
          },
        },
      ],
      attrs: {
        cursor: 'pointer',
      },
    },
    true,
  );

  // 注册节点复制工具
  Graph.registerNodeTool(
    'copy-node-button',
    {
      inherit: 'button',
      markup: [
        {
          tagName: 'rect',
          selector: 'rect',
          attrs: {
            width: 50,
            height: 25,
            stroke: '#fff',
            fill: 'white',
            opacity: 0.05,
            x: -50,
            y: -5,
          },
        },
        {
          tagName: 'image',
          selector: 'icon',
          attrs: {
            'xlink:href': imgUrl(copySvgPath),
            width: 20,
            height: 20,
            x: -50,
            y: -5,
          },
        },
      ],
      attrs: {
        cursor: 'pointer',
      },
    },
    true,
  );

  // 注册节点删除工具
  Graph.registerNodeTool(
    'delete-node-button',
    {
      inherit: 'button',
      markup: [
        {
          tagName: 'rect',
          selector: 'rect',
          attrs: {
            width: 20,
            height: 25,
            stroke: '#fff',
            fill: 'white',
            opacity: 0.05,
            x: -20,
            y: -5,
          },
        },
        {
          tagName: 'image',
          selector: 'icon',
          attrs: {
            'xlink:href': imgUrl(deleteSvgPath),
            width: 20,
            height: 20,
            x: -20,
            y: -5,
          },
        },
      ],
      attrs: {
        cursor: 'pointer',
      },
    },
    true,
  );

  // 注册边添加新增节点工具
  Graph.registerEdgeTool(
    'edge-add-node-button',
    {
      inherit: 'button',
      markup: [
        {
          tagName: 'circle',
          selector: 'button',
          attrs: {
            r: 7,
            stroke: '#6A4CFF',
            'stroke-width': 1,
            fill: 'white',
            cursor: 'pointer',
          },
        },
        {
          tagName: 'text',
          textContent: '+',
          selector: 'icon',
          attrs: {
            fill: '#6A4CFF',
            'font-size': 12,
            'text-anchor': 'middle',
            'pointer-events': 'none',
            y: '0.3em',
          },
        },
      ],
      attrs: {
        cursor: 'not-allowed',
      },
    },
    true,
  );

  // 注册边添加禁止删除工具
  Graph.registerEdgeTool(
    'edge-disabled-button-remove',
    {
      inherit: 'button',
      markup: [
        {
          tagName: 'circle',
          selector: 'button',
          attrs: {
            r: 7,
            stroke: '#F5A4A8',
            'stroke-width': 1,
            fill: '#F5A4A8',
            cursor: 'not-allowed',
          },
        },
        {
          tagName: 'text',
          textContent: 'x',
          selector: 'icon',
          attrs: {
            fill: 'white',
            'font-size': 12,
            'text-anchor': 'middle',
            'pointer-events': 'none',
            y: '0.3em',
          },
        },
      ],
      attrs: {
        cursor: 'not-allowed',
      },
    },
    true,
  );

  // 注册节点添加分支按钮工具
  Graph.registerNodeTool(
    'add-branch-button',
    {
      inherit: 'button',
      markup: [
        {
          tagName: 'rect',
          selector: 'button',
          attrs: {
            width: 104,
            height: 32,
            r: 14,
            stroke: '#ddd',
            'stroke-width': 1,
            fill: '#fff',
            cursor: 'pointer',
            rx: 10,
            filter: 'drop-shadow(0 0 2px rgb(73, 67, 163,0.4))',
            blur: '4px',
          },
        },
        {
          tagName: 'text',
          textContent: '添加分支',
          selector: 'icon',
          attrs: {
            fill: '#333',
            'font-size': 12,
            'text-anchor': 'middle',
            'pointer-events': 'none',
            y: 20,
            x: 54,
          },
        },
      ],
      attrs: { cursor: 'pointer' },
    },
    true,
  );
};
