import { Stencil } from '@antv/x6-plugin-stencil';
import {
  createUUID,
  imgUrl,
  commonNodeSize,
  conditionNodeSize,
  conditionStartNodeSize,
  conditionEndNodeSize,
  parallelMarkNodeSize,
  parallelInstanceNodeSize,
} from './utils';
import { NodeType, NodeTypeName } from './typings';

const flowStartIcon =
  '<svg t="1700729379871" class="icon" viewBox="0 0 1137 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="13600" width="200" height="200"><path d="M555.89893079 116.4921875a43.94067464 43.94067464 0 0 1 43.23762321 36.03135267L599.83960467 160.43286214V468.01758229H907.42432559a43.94067464 43.94067464 0 0 1 7.90932121 87.17829784L907.42432559 555.89893079H599.83960467v307.58472015a43.94067464 43.94067464 0 0 1-87.17829784 7.90932121L511.95825616 863.48365094V555.89893079H204.373536a43.94067464 43.94067464 0 0 1-7.9093212-87.17829782L204.373536 468.01758229h307.58472016V160.43286214A43.94067464 43.94067464 0 0 1 555.89893079 116.4921875z" p-id="13601"></path></svg>';

const flowEndIcon =
  '<svg t="1701141927800" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="13630" width="200" height="200"><path d="M810.688 64a149.312 149.312 0 0 1 29.824 295.68v122.432a59.712 59.712 0 0 1-59.712 59.776l-232.96-0.064v123.84a149.376 149.376 0 1 1-71.68 0V541.824H243.2a59.712 59.712 0 0 1-59.712-59.712V359.68a149.376 149.376 0 1 1 71.68-2.944v113.472h513.664l-0.064-113.472A149.376 149.376 0 0 1 810.688 64zM512 732.992a77.632 77.632 0 1 0 0 155.328 77.632 77.632 0 0 0 0-155.328zM213.312 135.68a77.632 77.632 0 1 0 0 155.328 77.632 77.632 0 0 0 0-155.328z m597.376 0a77.632 77.632 0 1 0 0 155.328 77.632 77.632 0 0 0 0-155.328z" p-id="13631"></path></svg>';

// 敬请期待 icon svg
export const stayTunedIcon =
  '<svg t="1699949621826" class="icon" viewBox="0 0 1441 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="12706" width="200" height="200"><path d="M0 0h846.392889L1441.185185 426.666667V1024z" fill="#CFCFF6" p-id="12707"></path><path d="M681.946074 48.848593l-8.836741 12.136296-20.593777-14.980741-13.122371 18.052741 20.631704 14.980741-7.205926 9.936592 18.014815 13.122371 7.205926-9.936593 20.821333 15.094519-9.253926 12.705185 18.052741 13.12237 9.253926-12.705185 18.204444 13.236148 13.122371-18.052741-18.204445-13.236148 8.912593-12.325926-18.014815-13.12237-8.988445 12.325926-20.783407-15.094519 8.836741-12.136296-18.052741-13.12237zM601.884444 135.774815l-31.440592 43.235555 48.431407 35.19526 40.922074-56.32-38.836148-28.216889c5.499259-2.199704 10.998519-4.702815 16.839111-7.736889l42.894223 31.175111c-28.975407 39.139556-46.383407 61.933037-52.337778 67.735704-7.509333 8.040296-14.715259 9.860741-20.973037 5.309629a342.622815 342.622815 0 0 1-18.849185-15.132444l-8.002371 20.328296c8.078222 6.712889 13.880889 11.491556 17.370074 14.032593 15.284148 11.112296 29.278815 10.05037 41.566815-2.996148 8.495407-8.988444 26.624-32.388741 54.423704-69.859556l0.986074 21.807407a168.011852 168.011852 0 0 0 16.611555-7.319703 316.036741 316.036741 0 0 0-15.549629 53.096296 123.259259 123.259259 0 0 1-58.55763 11.757037l-1.706667 24.348445c19.418074 0 38.229333-3.413333 56.69926-10.24-1.668741 19.304296-0.341333 36.826074 4.437333 52.717037l23.589926-8.495408c-5.499259-16.384-7.243852-34.512593-5.30963-54.992592 24.841481-14.032593 48.165926-35.346963 70.542223-64.132741l9.746963 7.092148 13.501629-18.583704-43.273481-31.478518c6.826667-6.257778 13.880889-13.274074 21.162666-20.593778l-16.19437-16.839111c-26.282667 28.444444-51.124148 48.355556-75.093333 59.657481l15.928889-21.921185-53.020445-38.532741c1.896296-1.137778 3.678815-2.351407 5.764741-3.678814l-15.284148-17.294223c-18.583704 12.401778-35.271111 20.252444-50.062223 23.552l1.668741 23.703704c0.265481-0.379259 0.758519-0.265481 1.403259-0.379259z m12.325926 50.024296l-15.473777-11.226074 17.521777-24.120889 15.473778 11.226074-17.521778 24.120889z m153.903408-7.433481a244.622222 244.622222 0 0 1-48.014222 47.521185c4.361481-17.635556 10.998519-36.446815 20.176592-56.813037l7.395556-5.575111 20.48 14.866963zM947.2 240.45037l-7.35763 10.126223-42.325333-30.757926-11.226074 15.435852 42.325333 30.795851-5.613037 7.698963-34.62637-25.144889-10.960593 15.094519 34.626371 25.144889-5.764741 7.964444-47.483259-34.512592-11.529482 15.815111 114.157037 82.906074 11.491556-15.815111-48.052148-34.891852 5.76474-7.964445 37.167408 27.041186 10.998518-15.094519-37.167407-27.003259 5.575111-7.736889 43.614815 31.706074 11.264-15.473778-43.614815-31.706074 7.35763-10.126222-18.583704-13.50163z m-102.893037 106.382223l52.679111 38.267259-6.561185 8.988444-52.641185-38.229333 6.523259-9.02637z m63.108741 23.893333l-52.679111-38.267259 6.712888-9.178074 52.641186 38.229333-6.674963 9.216z m-80.213334-0.341333l52.641186 38.267259-3.754667 5.157926c-2.806519 3.868444-5.916444 4.664889-9.216 2.275555l-14.032593-11.301926-7.585185 20.100741 15.322074 11.112296c11.946667 8.685037 22.452148 7.016296 30.985482-4.778666l52.186074-71.793778-89.125926-64.739556-65.915259 90.756741 18.014814 13.122371 20.48-28.178963z m41.718519-184.813037l-23.096889 3.147851c6.144 19.076741 9.860741 35.271111 10.657185 48.810667l22.907259-3.602963a298.552889 298.552889 0 0 0-10.429629-48.355555z m-64.625778 34.019555l-13.387852 18.394074 24.500148 17.825185-47.217777 64.967111a16.535704 16.535704 0 0 1-12.288 6.826667l-5.385482 22.528c18.432 0.151704 37.129481-1.137778 56.054519-4.020148l9.557333-23.969185c-8.078222 1.441185-15.853037 2.806519-23.324444 3.830518l54.575407-75.093333-43.083852-31.288889zM1040.801185 309.854815l-13.387852 18.394074-9.367703-6.826667-12.970667 17.863111 9.367704 6.826667-52.299852 71.983407-11.377778-8.305777-13.008593 17.863111 88.557037 64.360296 12.970667-17.863111-9.936592-7.205926 52.299851-72.021333 9.026371 6.561185 12.970666-17.825185-9.02637-6.599111 13.12237-18.014815-18.432-13.387852-13.084444 18.052741-30.378667-22.072889 13.349926-18.432-18.394074-13.349926z m-60.22637 121.628444l9.216-12.705185 30.340741 22.072889-9.178075 12.705185-30.378666-22.072889z m82.678518-49.910518l-9.367703 12.894815-30.378667-22.110815 9.367704-12.894815 30.34074 22.110815z m-61.288296 20.442074l9.367704-12.894815 30.34074 22.072889-9.329777 12.894815-30.378667-22.072889z m-2.23763 72.362666l-21.731555-1.441185c-0.265481 16.687407-1.251556 31.137185-3.26163 43.19763l16.687408 0.303407 4.589037 21.88326c23.627852-7.319704 45.549037-21.997037 65.839407-44.145778l29.468445 21.428148-23.021038 31.630222c-3.602963 5.006222-7.319704 6.219852-10.998518 3.565037a276.214519 276.214519 0 0 1-17.559704-14.184296l-8.495407 22.528 19.152592 13.918815c12.515556 9.102222 23.324444 7.395556 32.161186-4.778667l105.130666-144.687407-66.825481-48.545186-64.474074 88.746667c-16.763259 22.679704-35.802074 38.381037-57.192297 47.331556 0.872296-10.088296 1.024-22.33837 0.530963-36.788148z m-41.984-29.696a153.903407 153.903407 0 0 1-45.093926 13.653334l7.433482 22.300444a186.216296 186.216296 0 0 0 47.597037-17.445926l-9.936593-18.507852z m137.178074 6.295704l28.368593 20.593778-19.26637 26.548148-28.368593-20.631704 19.26637-26.510222z m41.073778 3.109926l-28.368592-20.593778 19.26637-26.510222 28.330667 20.593778-19.228445 26.548148zM1248.900741 457.386667c-20.328296 11.377778-43.311407 17.445926-68.79763 18.090666l-7.698963 22.528c34.512593-0.227556 63.412148-7.092148 87.305482-20.669629l-10.808889-19.949037z m-24.272593 47.331555a178.631111 178.631111 0 0 1-79.796148 22.755556l-7.547259 22.376296c9.860741-0.417185 19.721481-1.137778 29.278815-2.351407l-52.717037 72.51437 18.621629 13.50163 68.077037-93.677037c12.401778-3.944296 24.120889-9.216 34.891852-15.473778l-10.808889-19.64563z m92.34963 3.792593l-13.122371 18.052741-38.836148-28.216889-12.705185 17.445926 38.836148 28.254814-12.705185 17.483852-45.511111-32.995555-12.818963 17.635555 110.819556 80.516741 12.856888-17.673481-46.762666-33.98163 12.705185-17.445926 39.215407 28.444444 12.705186-17.445926-39.215408-28.520296 13.12237-18.052741-18.583703-13.501629z m-108.619852 54.992592l-12.970667 17.863112 69.214815 50.289777-27.003259 37.167408c-4.28563 5.916444-9.329778 7.054222-14.829037 3.034074a340.043852 340.043852 0 0 1-22.945185-18.356148l-8.836741 21.428148 26.168889 18.962963c13.615407 9.936593 25.78963 7.812741 36.105481-6.33363l30.492445-41.984 19.342222 14.032593 12.970667-17.825185-19.342223-14.032593 8.457482-11.643259-19.152593-13.918815-8.419555 11.605333-69.252741-50.289778z m9.216 39.594667l-21.731556-1.441185c2.199704 17.066667 3.26163 31.895704 2.958223 44.373333l22.528 0.303408a248.414815 248.414815 0 0 0-3.754667-43.235556z" fill="#7177FC" p-id="12708"></path></svg>';

export const newTag =
  '<svg t="1738744449616" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4425" xmlns:xlink="http://www.w3.org/1999/xlink" width="200" height="200"><path d="M889.6 272l-758.4 0c-41.6 0-73.6 32-73.6 73.6l0 361.6c0 41.6 32 73.6 73.6 73.6l758.4 0c41.6 0 73.6-32 73.6-73.6l0-361.6C963.2 304 931.2 272 889.6 272zM320 646.4l-51.2 0-99.2-195.2 0 195.2-32 0 0-233.6 44.8 0 102.4 201.6 3.2 0 0-201.6 32 0L320 646.4zM400 512l124.8 0 0 32-124.8 0c0 3.2 0 3.2 0 6.4l0 0c0 41.6 12.8 60.8 38.4 60.8l89.6 0 0 32-89.6 0c-19.2 0-35.2-6.4-48-22.4-12.8-16-19.2-41.6-19.2-70.4 0-12.8 0-28.8 0-54.4 0-54.4 35.2-76.8 70.4-83.2l0 0c12.8 0 80 0 86.4 0l0 32c-28.8 0-73.6 0-83.2 0-12.8 3.2-41.6 12.8-41.6 51.2C400 502.4 400 505.6 400 512zM816 646.4l-44.8 0-44.8-185.6-51.2 185.6-48 0-67.2-233.6 35.2 0 57.6 192 3.2 0 51.2-192 38.4 0 51.2 201.6 60.8-201.6 32 0L816 646.4z" p-id="4426" fill="#FFB22B"></path></svg>';

// 节点 icon svg
export const nodeIconSvg = {
  [NodeType.START_PROJECT]:
    '<svg t="1720070830168" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="20275" xmlns:xlink="http://www.w3.org/1999/xlink" width="200" height="200"><path d="M512 512m-512 0a512 512 0 1 0 1024 0 512 512 0 1 0-1024 0Z" fill="#A6AFBC" p-id="20276"></path><path d="M413.538462 216.615385c35.328 0 68.923077 28.672 68.923076 64v88.615384a63.881846 63.881846 0 0 1 73.846154 18.195693 63.960615 63.960615 0 0 1 104.329846 8.270769 68.923077 68.923077 0 0 1 106.023385 43.480615 3.072 3.072 0 0 1 0.275692 0.590769l0.07877 0.196923 0.039384 0.236308 0.118154 0.393846 0.078769 0.275693 0.039385 0.275692 0.039385 0.157538 0.078769 0.354462 0.039384 0.315077 0.039385 0.393846 0.078769 0.393846 0.039385 0.590769 0.078769 0.393847v0.472615l0.315077 8.704-50.491077 305.624615a59.076923 59.076923 0 0 1-58.171077 48.836923H413.538462a59.076923 59.076923 0 0 1-51.2-29.538461L245.366154 550.203077a64 64 0 0 1 23.394461-87.433846l8.546462-4.923077a64 64 0 0 1 67.347692 2.048V280.615385c0-35.328 28.672-64 64-64h4.923077z m0 39.384615a29.538462 29.538462 0 0 0-29.538462 29.065846v234.929231a19.692308 19.692308 0 0 1-36.745846 9.846154l-14.178462-24.615385a29.538462 29.538462 0 0 0-51.436307 29.144616l114.766769 223.625846a19.692308 19.692308 0 0 0 17.132308 10.003692h249.147076a19.692308 19.692308 0 0 0 19.456-16.462769L728.615385 452.923077a29.538462 29.538462 0 0 0-59.076923-0.472615v49.703384a19.692308 19.692308 0 0 1-39.384616 0.315077v-74.161231a24.615385 24.615385 0 0 0-49.230769-0.393846v74.24a19.692308 19.692308 0 0 1-39.384615 0.315077V433.230769a29.538462 29.538462 0 0 0-59.076924-0.472615v69.395692a19.692308 19.692308 0 0 1-39.384615 0.315077V285.538462a29.538462 29.538462 0 0 0-29.538461-29.538462z" fill="#FFFFFF" p-id="20277"></path><path d="M413.538462 212.676923c38.360615 0 72.861538 31.153231 72.861538 67.938462v82.944l0.787692-0.236308c4.292923-1.299692 8.664615-2.166154 13.115077-2.638769l6.774154-0.315077c20.48 0 39.424 9.097846 52.263385 24.536615l-3.032616-3.347692 1.457231-1.575385a67.741538 67.741538 0 0 1 40.999385-19.298461l6.774154-0.315077c24.260923 0 46.316308 12.839385 58.486153 33.319384l-2.205538-3.308307 2.756923-1.654154c8.388923-4.489846 17.604923-7.364923 27.214769-8.270769l7.286154-0.393847c34.816 0 64.590769 24.615385 70.852923 56.950154l0.708923 1.417846 0.196923 0.669539 0.157539 0.590769 0.354461 1.732923 0.07877 0.393846 0.039384 0.472616 0.078769 0.669538 0.039385 0.472616 0.078769 0.669538 0.196923 6.537846v2.914462l-50.451692 305.664A63.015385 63.015385 0 0 1 659.298462 811.323077H413.538462c-22.528 0-43.323077-12.012308-54.665847-31.704615l-116.972307-227.446154a67.938462 67.938462 0 0 1 24.851692-92.829539l8.546462-4.923077a67.899077 67.899077 0 0 1 60.33723-3.741538l5.041231 2.363077V280.615385c0-35.564308 27.293538-64.708923 62.070154-67.702154l5.868308-0.236308h4.923077z m0 7.876923h-4.923077c-33.161846 0-60.061538 26.899692-60.061539 60.061539v179.278769l-6.104615 3.308308a60.061538 60.061538 0 0 0-63.212308-1.969231l-8.546461 4.962461a60.100923 60.100923 0 0 0-21.858462 82.195693l116.972308 227.485538c9.846154 17.053538 28.041846 27.569231 47.734154 27.569231h245.76a55.138462 55.138462 0 0 0 54.350769-45.528616L764.061538 452.923077l-0.275692-8.349539-0.078769-0.787692-0.039385-0.512-0.118154-0.630154-0.078769-0.551384-0.118154-0.551385-0.590769-1.575385a64.984615 64.984615 0 0 0-99.918769-40.96l-5.592615-1.299692a60.022154 60.022154 0 0 0-97.910154-7.758769h-6.065231a59.904 59.904 0 0 0-69.316923-17.092923l-5.435077-3.623385v-88.615384c0-32.216615-30.798769-60.061538-64.984615-60.061539z m0 31.507692c18.510769 0 33.476923 14.966154 33.476923 33.476924v216.891076a15.753846 15.753846 0 0 0 31.507692-0.275692v-69.474461a33.476923 33.476923 0 0 1 66.953846 0.551384v69.198769a15.753846 15.753846 0 0 0 31.507692-0.275692v-74.318769a28.553846 28.553846 0 0 1 57.107693 0.472615v74.121846a15.753846 15.753846 0 0 0 31.507692-0.275692v-49.782154a33.476923 33.476923 0 0 1 33.476923-32.925538c18.510769 0 33.476923 14.966154 33.437539 34.067692l-46.473847 298.692923a23.630769 23.630769 0 0 1-23.355077 19.731693H413.538462a23.670154 23.670154 0 0 1-20.637539-12.169847l-114.688-223.507692a33.476923 33.476923 0 0 1 58.249846-33.004308l14.178462 24.615385a15.753846 15.753846 0 0 0 29.420307-7.876923V284.987077a33.476923 33.476923 0 0 1 33.476924-32.925539z m0 7.876924a25.6 25.6 0 0 0-25.6 25.127384v234.929231a23.630769 23.630769 0 0 1-44.11077 11.815385l-14.178461-24.615385a25.6 25.6 0 0 0-44.504616 25.363692l114.688 223.507693a15.753846 15.753846 0 0 0 13.705847 7.995076h249.147076a15.753846 15.753846 0 0 0 15.556924-13.115076L724.676923 452.923077a25.6 25.6 0 0 0-51.2-0.472615v49.703384a23.630769 23.630769 0 0 1-47.261538 0.354462v-74.200616a20.676923 20.676923 0 0 0-41.353847-0.393846v74.24a23.630769 23.630769 0 0 1-47.261538 0.354462V433.230769a25.6 25.6 0 0 0-51.2-0.472615v69.395692a23.630769 23.630769 0 0 1-47.261538 0.354462V285.538462a25.6 25.6 0 0 0-25.6-25.6z" fill="#FFFFFF" p-id="20278"></path></svg>',
  [NodeType.START_FLOW]:
    '<svg t="1700020197491" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="13176" width="200" height="200"><path d="M512 512m-512 0a512 512 0 1 0 1024 0 512 512 0 1 0-1024 0Z" fill="#A6AFBC" p-id="13177"></path><path d="M734.404923 546.067692a39.384615 39.384615 0 0 1-11.027692 10.791385l-332.721231 215.945846a39.384615 39.384615 0 0 1-60.809846-33.004308V296.172308a39.384615 39.384615 0 0 1 61.636923-32.492308l332.681846 227.643077a39.384615 39.384615 0 0 1 10.24 54.744615z m-32.492308-22.252307L369.230769 296.172308v443.628307l332.681846-215.98523z" fill="#FFFFFF" p-id="13178"></path></svg>',
  [NodeType.START_EVENT]:
    '<svg t="1703211504826" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="15294" width="200" height="200"><path d="M512 512m-512 0a512 512 0 1 0 1024 0 512 512 0 1 0-1024 0Z" fill="#A6AFBC" p-id="15295"></path><path d="M689.624615 236.307692C721.92 236.307692 748.307692 263.010462 748.307692 295.778462v432.443076c0 32.768-26.348308 59.470769-58.722461 59.47077H334.414769A59.195077 59.195077 0 0 1 275.692308 728.221538V295.778462C275.692308 263.049846 302.001231 236.307692 334.375385 236.307692h355.24923zM364.465231 283.293538h-30.050462a12.484923 12.484923 0 0 0-12.366769 11.736616V728.221538c0 6.931692 5.513846 12.524308 12.327385 12.524308h355.209846a12.445538 12.445538 0 0 0 12.366769-12.524308V295.817846a12.445538 12.445538 0 0 0-12.366769-12.524308h-30.129231v48.561231c0 22.843077-18.274462 41.432615-40.920615 41.472h-213.110154c-22.567385 0-40.96-18.589538-40.96-41.472V283.293538z m265.570461 341.307077c9.964308 0 17.801846 8.625231 17.801846 18.983385s-7.876923 18.944-17.801846 18.944H390.262154c-9.964308 0-17.801846-8.585846-17.801846-18.944s7.876923-18.983385 17.801846-18.983385h239.773538z m-119.886769-158.208c48.364308 0 87.670154 39.936 87.670154 89.048616 0 8.900923-1.339077 17.801846-3.938462 26.466461a32.531692 32.531692 0 0 1-30.956307 23.11877h-105.55077a32.492308 32.492308 0 0 1-30.956307-23.158154 89.796923 89.796923 0 0 1-3.938462-26.387693c0-49.152 39.305846-89.088 87.670154-89.088z m-120.753231 68.096c11.854769 0 21.464615 9.728 21.464616 21.740308a21.622154 21.622154 0 0 1-21.464616 21.779692h-16.30523a21.622154 21.622154 0 0 1-21.504-21.779692c0-12.012308 9.609846-21.740308 21.504-21.740308h16.30523z m257.772308 0c11.894154 0 21.504 9.728 21.504 21.740308a21.622154 21.622154 0 0 1-21.504 21.779692h-16.305231a21.622154 21.622154 0 0 1-21.464615-21.779692c0-12.012308 9.609846-21.740308 21.464615-21.740308h16.305231z m-137.019077-30.12923a50.845538 50.845538 0 0 0-49.191385 62.503384l98.304 0.236308c0.787692-3.387077 1.181538-6.537846 1.299693-9.728l0.039384-1.929846c0-28.16-22.646154-51.081846-50.412307-51.081846z m-80.620308-56.595693l9.924923 10.043077a22.016 22.016 0 0 1 0 30.798769 21.267692 21.267692 0 0 1-30.326153 0l-9.964308-10.003692a21.976615 21.976615 0 0 1 0-30.798769 21.267692 21.267692 0 0 1 30.326154 0z m190.818462-2.363077a22.016 22.016 0 0 1 0 30.759385l-9.924923 10.003692a21.110154 21.110154 0 0 1-30.404923 0 21.976615 21.976615 0 0 1 0.039384-30.798769l9.924923-10.043077a21.267692 21.267692 0 0 1 30.365539 0.078769z m-112.088615-44.110769c11.854769 0 21.464615 9.728 21.464615 21.740308v14.178461a21.661538 21.661538 0 0 1-21.464615 21.779693 21.622154 21.622154 0 0 1-21.464616-21.779693v-14.178461c0-12.012308 9.609846-21.740308 21.464616-21.740308z m104.88123-117.996308h-202.318769v43.08677h202.318769V283.293538z" fill="#FFFFFF" p-id="15296"></path></svg>',
  [NodeType.START_TIMER]:
    '<svg t="1701076694068" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="13750" width="200" height="200"><path d="M512 512m-512 0a512 512 0 1 0 1024 0 512 512 0 1 0-1024 0Z" fill="#A6AFBC" p-id="13751"></path><path d="M512.984615 723.298462c-114.412308 0-207.517538-91.844923-207.517538-204.681847s93.105231-204.681846 207.556923-204.681846c114.372923 0 207.478154 91.844923 207.478154 204.681846s-93.105231 204.681846-207.517539 204.681847m0-454.852924c-139.815385 0-253.636923 112.246154-253.636923 250.171077 0 72.388923 31.507692 137.491692 81.526154 183.256616a22.449231 22.449231 0 0 0-9.531077 5.356307l-42.141538 41.629539a22.528 22.528 0 0 0 10.318769 38.006154 23.236923 23.236923 0 0 0 22.252308-5.907693l42.141538-41.590153a22.331077 22.331077 0 0 0 6.380308-14.099693 254.424615 254.424615 0 0 0 142.690461 43.52 254.424615 254.424615 0 0 0 139.185231-41.235692c0.708923 4.450462 2.756923 8.585846 5.907692 11.815385l42.180924 41.629538a23.158154 23.158154 0 0 0 39.384615-16.068923c0-6.065231-2.441846-11.815385-6.774154-16.108308l-42.180923-41.590154a22.646154 22.646154 0 0 0-6.695385-4.371692c50.648615-45.764923 82.628923-111.300923 82.628923-184.241231 0-137.924923-113.821538-250.171077-253.636923-250.171077m23.079385 245.799385V382.188308a22.882462 22.882462 0 0 0-23.079385-22.764308 22.882462 22.882462 0 0 0-23.04 22.764308v141.469538c0 6.025846 2.402462 11.815385 6.73477 16.108308l77.312 76.248615a23.158154 23.158154 0 0 0 32.610461 0 22.528 22.528 0 0 0 0-32.177231l-70.537846-69.592615z m-260.411077-175.261538l65.220923-64.393847a22.528 22.528 0 0 0-0.512-31.625846 23.276308 23.276308 0 0 0-32.059077-0.512L243.003077 306.806154a22.528 22.528 0 0 0 0 32.177231c9.019077 8.861538 23.630769 8.861538 32.610461 0m505.304616-31.704616L715.697231 243.003077a23.276308 23.276308 0 0 0-32.610462 0 22.528 22.528 0 0 0 0 32.177231l65.220923 64.315077A23.158154 23.158154 0 0 0 787.692308 323.347692c0-6.025846-2.441846-11.815385-6.774154-16.068923" fill="#FFFFFF" p-id="13752"></path></svg>',
  [NodeType.START_MANUAL]:
    '<svg t="1719973759645" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="20216" xmlns:xlink="http://www.w3.org/1999/xlink" width="200" height="200"><path d="M0 0m512 0l0 0q512 0 512 512l0 0q0 512-512 512l0 0q-512 0-512-512l0 0q0-512 512-512Z" fill="#A6AFBC" p-id="20217"></path><path d="M479.98976 256a20.6848 20.6848 0 0 1 16.93696 8.8064l54.6048 77.9008h186.33728c24.7296 0 44.84096 19.69664 45.47584 44.23168l0.01536 1.18784v334.45376c0 25.088-20.36736 45.41952-45.4912 45.41952h-446.6176c-25.12384 0-45.4912-20.33664-45.4912-45.41952V301.41952c0-25.088 20.36736-45.41952 45.4912-45.41952z m-10.77248 41.2928h-177.9712a4.13184 4.13184 0 0 0-4.13184 4.12672v421.16096c0 2.2784 1.85344 4.12672 4.13696 4.12672h446.6176a4.13184 4.13184 0 0 0 4.13696-4.12672V388.12672a4.13184 4.13184 0 0 0-4.13696-4.12672h-197.10464a20.6848 20.6848 0 0 1-16.93696-8.8064L469.21728 297.2928zM555.96032 419.84c13.7216 0 24.83712 11.04384 24.83712 24.66816v90.45504h53.82144c18.31936 0 27.47904 21.79584 15.0016 34.72896l-0.62464 0.6144-120.05888 115.1232a20.80256 20.80256 0 0 1-28.75392 0l-120.064-115.11808c-13.3888-12.84608-4.23424-35.34848 14.38208-35.34848l53.82144-0.00512V444.50816c0-13.312 10.61888-24.1664 23.9104-24.6528l0.92672-0.01536z m-16.55808 41.1136h-49.68448v94.5664c0 11.04896-8.7808 20.0704-19.79904 20.5312l-0.896 0.02048h-23.29088l68.82816 65.99168 68.8128-65.98656h-23.27552c-11.13088 0-20.20864-8.72448-20.67968-19.6608l-0.01536-0.896V460.9536z" fill="#FFFFFF" p-id="20218"></path></svg>',
  [NodeType.END_FLOW]:
    '<svg t="1700020218609" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="13326" width="200" height="200"><path d="M512 512m-512 0a512 512 0 1 0 1024 0 512 512 0 1 0-1024 0Z" fill="#A6AFBC" p-id="13327"></path><path d="M660.834462 275.101538a298.535385 298.535385 0 0 1 74.436923 60.219077 291.209846 291.209846 0 0 1 74.555077 194.756923c0 162.579692-133.395692 294.281846-297.826462 294.281847-164.430769 0-297.865846-131.702154-297.865846-294.281847 0-72.743385 26.860308-141.390769 74.594461-194.756923a298.141538 298.141538 0 0 1 74.436923-60.219077 22.134154 22.134154 0 1 1 21.976616 38.47877 254.267077 254.267077 0 0 0-63.409231 51.239384 246.902154 246.902154 0 0 0-63.291077 165.257846c0 138.003692 113.467077 249.974154 253.558154 249.974154s253.518769-111.970462 253.518769-249.974154a246.902154 246.902154 0 0 0-63.251692-165.257846 253.833846 253.833846 0 0 0-63.409231-51.2 22.173538 22.173538 0 0 1 21.976616-38.518154zM512 214.134154c12.209231 0 22.134154 9.924923 22.134154 22.173538v290.185846a22.173538 22.173538 0 0 1-44.307692 0V236.307692c0-12.209231 9.924923-22.173538 22.173538-22.173538z" fill="#FFFFFF" p-id="13328"></path></svg>',
  [NodeType.MANUAL_APPROVE]:
    '<svg t="1700020245545" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="13476" width="200" height="200"><path d="M512 512m-512 0a512 512 0 1 0 1024 0 512 512 0 1 0-1024 0Z" fill="#FAAD14" p-id="13477"></path><path d="M742.478769 690.648615H275.101538c-4.647385 0-13.981538-4.686769-18.668307-4.686769s-4.686769-9.334154-4.686769-18.668308v-107.52c0-18.707692 18.707692-37.376 37.415384-37.376h135.483077v-14.020923l-13.981538-13.981538c-18.668308-18.747077-56.083692-56.083692-56.083693-126.227692 0-107.480615 84.125538-144.777846 158.956308-144.777847 74.752 0 154.190769 37.336615 154.190769 144.817231 0 70.104615-32.689231 102.833231-56.044307 121.501539-9.373538 4.726154-18.747077 18.707692-18.747077 18.707692v14.020923h135.561846c18.668308 0 37.376 18.668308 37.376 37.415385v107.480615c0 14.020923-9.373538 23.355077-23.355077 23.355077zM298.535385 643.938462h420.627692v-74.752h-135.561846c-32.689231 0-32.689231-28.081231-32.689231-60.770462 0-23.355077 14.020923-37.376 28.041846-51.396923 18.707692-18.668308 42.062769-42.062769 42.062769-88.812308 0-79.399385-56.083692-98.107077-107.480615-98.107077-32.689231 0-112.167385 9.334154-112.167385 98.107077 0 51.436308 23.355077 74.791385 46.749539 98.146462 13.981538 14.020923 28.002462 28.081231 28.002461 46.749538 0 14.020923 0 28.002462-4.686769 37.415385-4.647385 9.334154-18.668308 18.668308-37.336615 18.668308H298.496v74.752z m443.982769 130.875076H275.101538c-13.981538 0-23.355077-9.373538-23.355076-23.394461s9.373538-23.355077 23.355076-23.355077h467.416616c13.981538 0 23.355077 9.334154 23.355077 23.355077s-9.373538 23.394462-23.355077 23.394461z" fill="#FFFFFF" p-id="13478"></path></svg>',
  [NodeType.MANUAL_EXECUTION]:
    '<svg t="1700020281059" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="13626" width="200" height="200"><path d="M512 512m-512 0a512 512 0 1 0 1024 0 512 512 0 1 0-1024 0Z" fill="#FAAD14" p-id="13627"></path><path d="M512 196.923077a148.086154 148.086154 0 1 1 0 296.172308A148.086154 148.086154 0 0 1 512 196.923077z m0 46.552615a101.572923 101.572923 0 1 0 0 203.106462 101.572923 101.572923 0 0 0 0-203.106462z" fill="#FFFFFF" p-id="13628"></path><path d="M512 469.858462a256 256 0 0 1 256 256 23.236923 23.236923 0 0 1-46.552615 0 209.447385 209.447385 0 1 0-418.89477 0 23.236923 23.236923 0 0 1-46.552615 0 256 256 0 0 1 256-256z" fill="#FFFFFF" p-id="13629"></path></svg>',
  [NodeType.MANUAL_NOTIFICATION]:
    '<svg t="1700020294521" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="13777" width="200" height="200"><path d="M512 512m-512 0a512 512 0 1 0 1024 0 512 512 0 1 0-1024 0Z" fill="#FAAD14" p-id="13778"></path><path d="M695.808 275.692308A91.884308 91.884308 0 0 1 787.692308 367.576615v275.692308a91.884308 91.884308 0 0 1-91.884308 91.923692l-95.744-0.039384-55.296 57.737846-0.984615 0.984615a45.961846 45.961846 0 0 1-64.984616-0.590769l-57.068307-58.131692H328.192A91.884308 91.884308 0 0 1 236.307692 643.308308v-275.692308A91.884308 91.884308 0 0 1 328.192 275.692308h367.616z m0 45.961846H328.192c-25.363692 0-45.922462 20.558769-45.922462 45.922461v275.692308c0 25.403077 20.558769 45.961846 45.922462 45.961846h112.836923l70.577231 71.837539 68.804923-71.837539h115.396923c25.363692 0 45.922462-20.558769 45.922462-45.961846v-275.692308c0-25.363692-20.558769-45.922462-45.922462-45.922461z m-309.720615 152.260923a33.870769 33.870769 0 1 1 0 67.741538 33.870769 33.870769 0 0 1 0-67.741538z m120.713846 0a33.870769 33.870769 0 1 1 0 67.741538 33.870769 33.870769 0 0 1 0-67.741538z m119.886769-0.472615a33.870769 33.870769 0 1 1 0 67.780923 33.870769 33.870769 0 0 1 0-67.780923z" fill="#FFFFFF" p-id="13779"></path></svg>',
  [NodeType.AUTO_HTTP]:
    '<svg t="1700020313340" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="13927" width="200" height="200"><path d="M512 512m-512 0a512 512 0 1 0 1024 0 512 512 0 1 0-1024 0Z" fill="#0089FE" p-id="13928"></path><path d="M786.116923 266.515692l-28.632615-28.672a5.592615 5.592615 0 0 0-7.719385 0l-51.436308 51.436308a134.656 134.656 0 0 0-75.736615-23.158154c-34.619077 0-69.198769 13.154462-95.625846 39.581539l-68.844308 68.883692a5.435077 5.435077 0 0 0 0 7.601231l183.571692 183.611077c1.102769 1.102769 2.441846 1.575385 3.859693 1.575384 1.378462 0 2.756923-0.551385 3.859692-1.575384l68.844308-68.844308a135.286154 135.286154 0 0 0 16.423384-171.323077l51.436308-51.396923a5.513846 5.513846 0 0 0 0-7.719385z m-100.430769 197.947077l-40.132923 40.132923-126.227693-126.227692 40.172308-40.172308a88.733538 88.733538 0 0 1 63.094154-26.151384c23.867077 0 46.198154 9.255385 63.094154 26.151384 16.856615 16.856615 26.151385 39.266462 26.151384 63.133539s-9.294769 46.237538-26.151384 63.133538z m-128.512 70.931693a5.435077 5.435077 0 0 0-7.640616 0l-45.016615 45.016615-61.006769-61.046154 45.056-45.056a5.435077 5.435077 0 0 0 0-7.640615l-24.576-24.576a5.435077 5.435077 0 0 0-7.640616 0l-45.056 45.056-29.065846-29.065846a5.316923 5.316923 0 0 0-3.859692-1.575385 5.710769 5.710769 0 0 0-3.859692 1.575385L305.742769 526.966154a135.286154 135.286154 0 0 0-16.423384 171.323077L237.883077 749.646769a5.435077 5.435077 0 0 0 0 7.640616l28.632615 28.632615c1.102769 1.102769 2.441846 1.575385 3.859693 1.575385 1.417846 0 2.756923-0.551385 3.859692-1.575385l51.436308-51.396923c22.764308 15.438769 49.230769 23.158154 75.736615 23.158154 34.619077 0 69.198769-13.193846 95.625846-39.581539l68.844308-68.883692a5.435077 5.435077 0 0 0 0-7.640615l-29.026462-29.026462 45.056-45.095385a5.435077 5.435077 0 0 0 0-7.640615l-24.733538-24.418461z m-92.711385 150.291692a88.733538 88.733538 0 0 1-63.133538 26.151384 88.576 88.576 0 0 1-63.094154-26.151384 88.733538 88.733538 0 0 1-26.151385-63.133539c0-23.827692 9.255385-46.198154 26.151385-63.094153l40.132923-40.172308 126.227692 126.267077-40.172307 40.132923z" fill="#FFFFFF" p-id="13929"></path></svg>',
  [NodeType.AUTO_ESP]:
    '<svg t="1700020329771" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="14077" width="200" height="200"><path d="M512 512m-512 0a512 512 0 1 0 1024 0 512 512 0 1 0-1024 0Z" fill="#0089FE" p-id="14078"></path><path d="M671.232 456.861538V258.363077c0-12.209231-9.176615-22.055385-20.519385-22.055385H373.287385c-11.342769 0-20.519385 9.846154-20.519385 22.055385v198.498461C310.232615 456.861538 275.692308 492.780308 275.692308 536.812308v245.366154c0 3.032615 2.323692 5.513846 5.12 5.513846h35.958154c2.835692 0 5.12-2.481231 5.12-5.513846v-245.366154c0-16.738462 13.902769-30.326154 30.916923-30.326154h318.38523c17.014154 0 30.877538 13.587692 30.877539 30.326154v245.366154c0 3.032615 2.323692 5.513846 5.12 5.513846h35.997538c2.796308 0 5.12-2.481231 5.12-5.513846v-245.366154c0-44.032-34.540308-79.950769-77.075692-79.95077z m-272.265846 0V285.932308h226.067692v170.92923h-226.067692z m77.075692-126.818461h-30.838154c-2.796308 0-5.12 2.481231-5.12 5.513846v33.083077c0 3.032615 2.323692 5.513846 5.12 5.513846h30.838154c2.835692 0 5.12-2.481231 5.12-5.513846v-33.083077c0-3.032615-2.284308-5.513846-5.12-5.513846z m102.754462 0h-30.838154c-2.835692 0-5.12 2.481231-5.12 5.513846v33.083077c0 3.032615 2.284308 5.513846 5.12 5.513846h30.838154c2.796308 0 5.12-2.481231 5.12-5.513846v-33.083077c0-3.032615-2.323692-5.513846-5.12-5.513846z" fill="#FFFFFF" p-id="14079"></path></svg>',
  [NodeType.AUTO_CALL_EVENT]:
    '<svg t="1703211552970" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="15444" width="200" height="200"><path d="M512 512m-512 0a512 512 0 1 0 1024 0 512 512 0 1 0-1024 0Z" fill="#0089FE" p-id="15445"></path><path d="M743.305846 670.838154c11.815385 0 21.425231 10.24 21.425231 22.961231 0 12.681846-9.570462 22.961231-21.425231 22.96123H280.812308c-11.815385 0-21.425231-10.279385-21.425231-22.96123 0-12.681846 9.570462-22.961231 21.425231-22.961231h462.532923z m-231.266461-289.476923c86.134154 0 156.199385 69.435077 156.199384 154.978461 0 15.517538-2.363077 30.956308-7.010461 45.961846-6.498462 20.637538-25.718154 34.658462-47.379693 34.658462h-203.618461c-21.661538 0-40.920615-14.020923-47.379692-34.658462a152.891077 152.891077 0 0 1-7.010462-45.961846c0-85.543385 70.104615-154.978462 156.199385-154.978461z m0 45.961846c-60.849231 0-110.276923 48.955077-110.276923 109.016615 0 11.027692 1.654154 21.819077 4.923076 32.216616a3.662769 3.662769 0 0 0 3.544616 2.441846h203.618461a3.702154 3.702154 0 0 0 3.544616-2.402462c3.229538-10.515692 4.923077-21.385846 4.923077-32.295384 0-60.022154-49.427692-108.977231-110.276923-108.977231z m-215.748923 82.235077c15.753846 0 28.514462 12.642462 28.514461 28.238769a28.356923 28.356923 0 0 1-28.514461 28.238769h-31.507693A28.356923 28.356923 0 0 1 236.307692 537.796923a28.356923 28.356923 0 0 1 28.514462-28.238769h31.507692z m462.887384 0c15.753846 0 28.514462 12.642462 28.514462 28.238769a28.356923 28.356923 0 0 1-28.514462 28.238769h-31.507692a28.356923 28.356923 0 0 1-28.475077-28.238769 28.356923 28.356923 0 0 1 28.514461-28.238769h31.507693zM355.958154 351.035077l19.140923 18.904615c11.145846 11.027692 11.145846 28.868923 0.039385 39.936a28.593231 28.593231 0 0 1-40.329847 0.039385l-19.101538-18.904615a28.081231 28.081231 0 0 1-0.078769-39.896616 28.750769 28.750769 0 0 1 40.329846-0.078769z m350.916923-4.529231a28.081231 28.081231 0 0 1-0.039385 39.936l-19.140923 18.904616a28.593231 28.593231 0 0 1-40.329846-0.07877 28.081231 28.081231 0 0 1 0.078769-39.936l19.140923-18.904615a28.750769 28.750769 0 0 1 40.329847 0.078769zM508.416 275.692308c15.753846 0 28.514462 12.603077 28.514462 28.238769v26.702769a28.396308 28.396308 0 0 1-28.514462 28.238769 28.356923 28.356923 0 0 1-28.514462-28.238769v-26.702769A28.356923 28.356923 0 0 1 508.376615 275.692308z" fill="#FFFFFF" p-id="15446"></path></svg>',
  [NodeType.AUTO_NOTIFY]:
    '<svg t="1700020377824" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="14377" width="200" height="200"><path d="M512 512m-512 0a512 512 0 1 0 1024 0 512 512 0 1 0-1024 0Z" fill="#0089FE" p-id="14378"></path><path d="M533.858462 242.845538c116.105846 11.579077 204.721231 111.419077 204.72123 230.715077h-0.236307a22.843077 22.843077 0 0 1 0.236307 3.229539v153.796923h27.175385c12.130462 0 21.937231 10.043077 21.937231 22.449231 0 12.366769-9.846154 22.449231-21.937231 22.44923h-145.329231c-7.955692 55.020308-54.114462 95.783385-108.504615 95.783385-54.390154 0-100.588308-40.763077-108.504616-95.822769H258.244923A22.173538 22.173538 0 0 1 236.307692 652.996923c0-12.366769 9.846154-22.409846 21.937231-22.409846h27.017846v-153.796923a22.843077 22.843077 0 0 1 0.236308-3.229539h-0.236308c0-119.335385 88.615385-219.136 204.721231-230.715077V219.372308c0-12.406154 9.846154-22.449231 21.937231-22.449231 12.091077 0 21.937231 10.043077 21.937231 22.449231v23.47323z m-204.721231 387.741539h365.568v-153.796923a22.843077 22.843077 0 0 1 0.236307-3.229539h-0.236307c0-103.187692-81.841231-186.919385-182.784-186.919384s-182.784 83.692308-182.784 186.919384H328.861538a22.843077 22.843077 0 0 1 0.236308 3.229539v153.796923z m246.587077 44.898461h-127.606154c7.325538 29.892923 33.634462 50.845538 63.803077 50.845539 30.129231 0 56.438154-20.952615 63.803077-50.884923z" fill="#FFFFFF" p-id="14379"></path></svg>',
  [NodeType.MSG_SEND]:
    '<svg t="1700020377824" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="14377" width="200" height="200"><path d="M512 512m-512 0a512 512 0 1 0 1024 0 512 512 0 1 0-1024 0Z" fill="#0089FE" p-id="14378"></path><path d="M533.858462 242.845538c116.105846 11.579077 204.721231 111.419077 204.72123 230.715077h-0.236307a22.843077 22.843077 0 0 1 0.236307 3.229539v153.796923h27.175385c12.130462 0 21.937231 10.043077 21.937231 22.449231 0 12.366769-9.846154 22.449231-21.937231 22.44923h-145.329231c-7.955692 55.020308-54.114462 95.783385-108.504615 95.783385-54.390154 0-100.588308-40.763077-108.504616-95.822769H258.244923A22.173538 22.173538 0 0 1 236.307692 652.996923c0-12.366769 9.846154-22.409846 21.937231-22.409846h27.017846v-153.796923a22.843077 22.843077 0 0 1 0.236308-3.229539h-0.236308c0-119.335385 88.615385-219.136 204.721231-230.715077V219.372308c0-12.406154 9.846154-22.449231 21.937231-22.449231 12.091077 0 21.937231 10.043077 21.937231 22.449231v23.47323z m-204.721231 387.741539h365.568v-153.796923a22.843077 22.843077 0 0 1 0.236307-3.229539h-0.236307c0-103.187692-81.841231-186.919385-182.784-186.919384s-182.784 83.692308-182.784 186.919384H328.861538a22.843077 22.843077 0 0 1 0.236308 3.229539v153.796923z m246.587077 44.898461h-127.606154c7.325538 29.892923 33.634462 50.845538 63.803077 50.845539 30.129231 0 56.438154-20.952615 63.803077-50.884923z" fill="#FFFFFF" p-id="14379"></path></svg>',
  [NodeType.AUTO_SCRIPT]:
    '<svg t="1700020738943" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="14981" width="200" height="200"><path d="M512 512m-512 0a512 512 0 1 0 1024 0 512 512 0 1 0-1024 0Z" fill="#0089FE" p-id="14982"></path><path d="M619.362462 315.076923l2.914461 0.787692c14.178462 4.017231 20.795077 20.322462 13.548308 33.083077-13.508923 23.709538-27.096615 47.340308-40.684308 70.931693l-164.430769 287.192615a25.363692 25.363692 0 0 1-8.625231 9.570462 27.451077 27.451077 0 0 1-7.876923 3.072 4.253538 4.253538 0 0 0-1.024 0.472615h-5.828923a20.164923 20.164923 0 0 0-2.166154-0.748308 22.685538 22.685538 0 0 1-14.651077-33.398154l121.619692-212.283077c27.884308-48.758154 55.847385-97.476923 83.692308-146.235076a23.236923 23.236923 0 0 1 11.224616-10.555077c1.693538-0.669538 3.426462-1.024 5.198769-1.536 0.315077-0.078769 0.590769-0.236308 0.866461-0.354462h6.22277zM196.923077 517.041231c0.275692-0.590769 0.630154-1.142154 0.787692-1.732923a23.04 23.04 0 0 1 6.301539-11.421539c39.148308-39.463385 78.336-79.005538 117.563077-118.429538a21.267692 21.267692 0 0 1 22.96123-5.513846c8.704 2.756923 13.902769 9.058462 15.36 18.116923 1.181538 7.522462-1.024 14.178462-6.419692 19.652923l-81.644308 82.235077-22.724923 22.96123a29.459692 29.459692 0 0 0-1.102769 1.181539 25.166769 25.166769 0 0 0 1.417846 1.575385l104.014769 104.802461a22.764308 22.764308 0 0 1-9.846153 38.203077 21.464615 21.464615 0 0 1-21.976616-5.828923c-7.364923-7.325538-14.611692-14.729846-21.897846-22.055385L204.8 545.083077a24.064 24.064 0 0 1-7.207385-12.642462c-0.078769-0.472615-0.433231-0.905846-0.669538-1.339077v-14.060307z m630.153846 15.202461l-0.669538 2.363077a22.488615 22.488615 0 0 1-5.632 9.452308l-39.660308 40.132923-13.390769 13.587692-47.064616 47.576616c-5.789538 5.868308-11.460923 11.815385-17.368615 17.565538a21.504 21.504 0 0 1-23.079385 5.356308 21.543385 21.543385 0 0 1-15.202461-18.510769 21.858462 21.858462 0 0 1 6.577231-19.298462l51.396923-52.066461 44.189538-44.701539c3.111385-3.150769 6.262154-6.183385 9.570462-9.452308l-1.457231-1.536c-7.68-7.758769-15.36-15.478154-22.961231-23.236923l-59.352615-60.061538c-7.246769-7.286154-14.572308-14.572308-21.740308-22.016a21.779692 21.779692 0 0 1-5.435077-22.528 21.622154 21.622154 0 0 1 16.935385-15.675077 21.661538 21.661538 0 0 1 20.91323 6.380308l47.340308 47.891692 52.657231 53.248c5.750154 5.907692 11.579077 11.697231 17.329231 17.604923 2.993231 3.072 5.12 6.695385 5.750154 11.027692 0.039385 0.196923 0.236308 0.315077 0.315076 0.472616l0.039385 16.423384z" fill="#FFFFFF" p-id="14983"></path></svg>',
  [NodeType.AUTO_SUB_PROCESS]:
    '<svg t="1700020405428" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="14527" width="200" height="200"><path d="M512 512m-512 0a512 512 0 1 0 1024 0 512 512 0 1 0-1024 0Z" fill="#0089FE" p-id="14528"></path><path d="M428.701538 236.307692a113.624615 113.624615 0 0 1 22.646154 224.964923v93.105231h140.288a68.174769 68.174769 0 1 1 0 45.489231l-140.248615-0.039385v136.349539h140.209231a68.174769 68.174769 0 1 1 0 45.449846h-146.668308c-21.543385 0-38.990769-20.322462-38.990769-45.449846V463.556923l0.118154-2.244923A113.664 113.664 0 0 1 428.701538 236.307692z m0 45.449846a68.135385 68.135385 0 1 0 0 136.310154 68.135385 68.135385 0 0 0 0-136.310154z" fill="#FFFFFF" p-id="14529"></path></svg>',
  [NodeType.AUTO_SUB_PROJECT]:
    '<?xml version="1.0" standalone="no"?><!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd"><svg t="1734339313132" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="46987" xmlns:xlink="http://www.w3.org/1999/xlink" width="200" height="200"><path d="M0 512c0 282.7776 229.2224 512 512 512s512-229.2224 512-512S794.7776 0 512 0 0 229.2224 0 512z" fill="#0089FE" p-id="46988"></path><path d="M547.5328 339.456L483.84 275.456a65.792 65.792 0 0 0-93.3888 0L326.8096 339.456a66.4064 66.4064 0 0 0 0 94.208l63.6416 64a65.9456 65.9456 0 0 0 93.7984 0l63.6928-64a66.56 66.56 0 0 0-0.4096-94.208zM460.1856 473.088a32.8704 32.8704 0 0 1-46.6432 0L350.5152 409.7536a33.28 33.28 0 0 1 0-46.848l63.0272-63.232a32.768 32.768 0 0 1 46.6432 0l63.0272 63.232a33.28 33.28 0 0 1 0 46.848L460.288 473.088z m155.5968 80.9984a60.416 60.416 0 0 0-42.752 17.8688 60.928 60.928 0 0 0-17.7152 43.008v82.9952c0 33.6384 27.136 60.928 60.6208 60.928h82.5344a60.8256 60.8256 0 0 0 60.416-60.928v-82.9952c0-33.5872-27.0336-60.8256-60.416-60.928h-82.688z" fill="#FFFFFF" p-id="46989"></path><path d="M478.8224 633.856a36.5056 36.5056 0 0 1-24.4736-9.216 30.0032 30.0032 0 0 1-10.1376-22.2208v-84.736c0-1.8944 0.256-3.7888 0.6144-5.6832H409.6v90.7776c0.0512 34.6624 31.0272 62.7712 69.2224 62.8224H563.2v-31.744H478.8224z" fill="#FFFFFF" p-id="46990"></path></svg>',
  [NodeType.AUTO_WAIT]:
    '<?xml version="1.0" standalone="no"?><!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd"><svg t="1734339306472" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="46839" xmlns:xlink="http://www.w3.org/1999/xlink" width="200" height="200"><path d="M0 512c0 282.7776 229.2224 512 512 512s512-229.2224 512-512S794.7776 0 512 0 0 229.2224 0 512z" fill="#0089FE" p-id="46840"></path><path d="M532.48 640h-40.96c-12.288 0-20.48 8.4992-20.48 21.2992 0 12.8 8.192 21.3504 20.48 21.3504h40.96c12.288 0 20.48-8.5504 20.48-21.3504 0-12.8-8.192-21.2992-20.48-21.2992z m163.84 85.3504h-20.48c0-89.6-49.152-140.8-81.92-179.2-12.288-12.8-28.672-25.6-28.672-34.1504 0-4.3008 12.288-12.8 20.48-17.0496 32.768-25.6 90.112-72.5504 90.112-196.3008h20.48c12.288 0 20.48-8.5504 20.48-21.2992 0-12.8-8.192-21.3504-20.48-21.3504H327.68c-12.288 0-20.48 8.5504-20.48 21.3504 0 12.8 8.192 21.2992 20.48 21.2992h20.48c0 123.7504 57.344 170.6496 90.112 196.3008 8.192 4.1984 20.48 12.8 20.48 17.0496 0 8.4992-16.384 21.2992-28.672 38.4-32.768 34.0992-81.92 85.3504-81.92 174.9504h-20.48c-12.288 0-20.48 8.5504-20.48 21.2992 0 12.8 8.192 21.3504 20.48 21.3504h368.64c12.288 0 20.48-8.5504 20.48-21.3504 0-12.8-8.192-21.2992-20.48-21.2992z m-307.2 0c0-72.5504 40.96-115.2 69.632-149.3504 20.48-21.3504 40.96-42.7008 40.96-64 0-25.6-16.384-38.4-36.864-51.2C430.08 435.2 389.12 401.0496 389.12 298.6496h245.76c0 102.4-40.96 136.5504-73.728 162.1504-20.48 12.8-36.864 25.6-36.864 51.2s20.48 42.6496 40.96 64c28.672 34.0992 69.632 76.8 69.632 149.3504H389.12z" fill="#FFFFFF" p-id="46841"></path></svg>',
  [NodeType.AUTO_EVOKE_WAIT]:
    '<svg t="1744090938777" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="26976" width="200" height="200"><path d="M1022.076962 512.961519a512.929323 512.929323 0 0 1-29.895751 172.168864 513.082635 513.082635 0 0 1-86.109984 152.033948 507.767835 507.767835 0 0 1-111.099766 100.674581A509.096535 509.096535 0 0 1 635.220832 1008.668846a504.548292 504.548292 0 0 1-99.141465 14.717908 512.929323 512.929323 0 0 1-197.20975-29.282505 512.571597 512.571597 0 0 1-171.04458-102.463216 515.075685 515.075685 0 0 1-81.715053-94.746534A510.118612 510.118612 0 0 1 0.613246 538.002404 511.038481 511.038481 0 1 1 1022.076962 512.961519z" fill="#0089FE" p-id="26977"></path><path d="M695.012334 257.442278c12.264924 0 20.441539 8.534343 20.44154 21.310305 0 11.140639-6.285773 19.061735-16.046609 20.85037l-4.394931 0.408831h-20.441539c0 123.518001-57.23631 170.329126-89.942773 195.932153-8.176616 4.190516-20.441539 12.775962-20.441539 17.017582 0 8.483239 16.353231 21.259201 28.618155 34.086267 30.968932 36.283732 76.655772 84.06583 81.357326 165.065429l0.408831 13.798039h20.441539c12.264924 0 20.441539 8.534343 20.44154 21.259201 0 11.242847-6.285773 19.112839-16.046609 20.952578L695.012334 768.480759H327.064628c-12.264924 0-20.441539-8.534343-20.441539-21.310304 0-11.140639 6.285773-19.061735 16.046608-20.85037l4.394931-0.408831h20.441539c0-89.431734 49.059694-140.535582 81.766157-174.621849 12.264924-17.068685 28.618155-29.844647 28.618155-38.327886 0-4.292723-12.264924-12.775962-20.441539-17.017582-31.47997-24.632055-85.752257-68.990195-89.687254-182.542945L347.506167 300.011784h-20.441539c-12.264924 0-20.441539-8.534343-20.441539-21.259201 0-11.242847 6.285773-19.112839 16.046608-20.952578L327.064628 257.442278h367.947706zM488.297269 482.912456c6.490189 7.921096 10.476289 17.426412 10.476289 30.049063 0 21.310305-20.441539 42.569505-40.883079 63.87981-28.618155 34.035163-69.501233 76.655772-69.501233 149.069925h245.298471c0-72.414153-40.883078-114.983658-69.501234-149.069925-20.441539-21.310305-40.883078-38.327886-40.883078-63.87981 0-12.060508 3.679477-21.310305 9.709731-29.026986-7.461162 2.299673-14.820116 3.475062-22.17907 3.475062-6.796812 0-14.513493-1.686427-22.485693-4.497139zM511.038481 589.617291c12.775962 0 21.310305 7.358954 21.310305 19.623878v41.70074c0 12.264924-8.534343 20.441539-21.310305 20.441539-12.775962 0-21.259201-8.176616-21.259201-20.441539v-40.883079c0-12.264924 8.483239-20.441539 21.259201-20.441539z m122.649236-289.605507H388.389246c0 41.394117 6.745708 71.647595 16.86427 94.644327 29.691336 10.731808 64.901887 16.097712 105.58055 16.097712 40.780871 0 76.144734-5.417008 105.989381-16.19992 10.169666-22.996732 16.86427-53.148002 16.86427-94.542119z" fill="#FFFFFF" p-id="26978"></path></svg>',
  [NodeType.CONDITION_BRANCH]:
    '<svg t="1700020419852" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="14677" width="200" height="200"><path d="M512 512m-512 0a512 512 0 1 0 1024 0 512 512 0 1 0-1024 0Z" fill="#70B601" p-id="14678"></path><path d="M396.169846 236.307692a81.092923 81.092923 0 0 1 25.166769 158.208l-0.078769-1.142154c0.551385 25.6 35.052308 57.895385 106.456616 92.790154 94.562462 45.449846 146.038154 92.278154 149.582769 142.296616a81.053538 81.053538 0 1 1-39.620923-0.945231c-5.632-31.625846-46.355692-67.623385-126.936616-106.338462-39.463385-19.298462-70.025846-38.596923-91.451077-58.368l0.07877 166.084923a81.132308 81.132308 0 1 1-39.384616-1.732923v-230.32123A81.132308 81.132308 0 0 1 396.091077 236.307692z m286.72 39.384616c8.664615-0.393846 15.832615 6.537846 15.832616 15.163077l-0.039385 62.542769h62.621538a15.123692 15.123692 0 1 1 0 30.286769l-62.621538-0.039385 0.039385 62.582154a15.123692 15.123692 0 1 1-30.326154-0.236307V383.606154h-62.503385a15.123692 15.123692 0 1 1 0-30.247385h62.503385V291.052308c0-8.113231 6.340923-14.966154 14.493538-15.36z" fill="#FFFFFF" p-id="14679"></path></svg>',
  [NodeType.PARALLEL_BRANCH]:
    '<svg t="1700020430963" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="14827" width="200" height="200"><path d="M512 512m-512 0a512 512 0 1 0 1024 0 512 512 0 1 0-1024 0Z" fill="#70B601" p-id="14828"></path><path d="M651.342769 727.236923a58.683077 58.683077 0 0 0 59.273846 59.273846 58.683077 58.683077 0 0 0 59.273847-59.273846 58.683077 58.683077 0 0 0-59.273847-59.313231 59.470769 59.470769 0 0 0-59.273846 59.313231z" fill="#FFFFFF" p-id="14829"></path><path d="M710.616615 804.312615a76.918154 76.918154 0 0 1-77.075692-77.075692c0-42.692923 34.382769-77.075692 77.075692-77.075692S787.692308 684.504615 787.692308 727.236923a76.918154 76.918154 0 0 1-77.075693 77.075692z m0-118.587077c-22.528 0-41.511385 18.944-41.511384 41.511385 0 22.528 18.983385 41.472 41.511384 41.472 22.528 0 41.511385-18.944 41.511385-41.472 0-22.567385-18.983385-41.511385-41.511385-41.511385zM254.109538 727.236923a58.683077 58.683077 0 0 0 59.273847 59.273846 58.683077 58.683077 0 0 0 59.273846-59.273846 58.683077 58.683077 0 0 0-59.273846-59.313231 59.470769 59.470769 0 0 0-59.273847 59.313231z" fill="#FFFFFF" p-id="14830"></path><path d="M313.383385 804.312615A76.918154 76.918154 0 0 1 236.307692 727.236923c0-42.692923 34.382769-77.075692 77.075693-77.075692s77.075692 34.382769 77.075692 77.075692a76.918154 76.918154 0 0 1-77.075692 77.075692z m0-118.587077c-22.528 0-41.511385 18.944-41.511385 41.511385 0 22.528 18.983385 41.472 41.511385 41.472 22.528 0 41.511385-18.944 41.511384-41.472 0-22.567385-18.983385-41.511385-41.511384-41.511385zM453.316923 313.383385a58.683077 58.683077 0 0 0 59.273846 59.273846 58.683077 58.683077 0 0 0 59.273846-59.273846 58.683077 58.683077 0 0 0-59.273846-59.273847 59.470769 59.470769 0 0 0-59.273846 59.273847z" fill="#FFFFFF" p-id="14831"></path><path d="M512.590769 390.459077a76.918154 76.918154 0 0 1-77.075692-77.075692c0-42.692923 34.382769-77.075692 77.075692-77.075693s77.075692 34.382769 77.075693 77.075693a76.918154 76.918154 0 0 1-77.075693 77.075692z m0-118.547692c-22.528 0-41.511385 18.904615-41.511384 41.472 0 22.528 18.983385 41.511385 41.511384 41.511384 22.528 0 41.511385-18.983385 41.511385-41.511384 0-22.528-18.983385-41.511385-41.511385-41.511385zM727.945846 281.284923c8.664615-0.393846 15.832615 6.537846 15.832616 15.163077l-0.039385 62.542769h62.582154a15.123692 15.123692 0 1 1 0 30.286769l-62.621539-0.039384 0.07877 62.542769a15.123692 15.123692 0 1 1-30.326154-0.196923V389.238154h-62.503385a15.123692 15.123692 0 1 1 0-30.247385h62.503385V296.644923c0-8.113231 6.340923-14.966154 14.493538-15.36z" fill="#FFFFFF" p-id="14832"></path><path d="M332.130462 560.049231l-1.575385 126.464a17.801846 17.801846 0 0 1-35.564308-0.393846l1.811693-144.108308V540.908308l0.039384-0.196923v-0.236308l0.039385-0.236308 0.039384-0.275692v-0.236308l0.07877-0.315077 0.039384-0.196923 0.039385-0.236307 0.078769-0.315077v-0.196923l0.039385-0.07877v-0.118154l0.118154-0.315076 0.039384-0.196924v-0.118153l0.039385-0.118154 0.118154-0.275693 0.039384-0.196923v-0.078769l0.078769-0.157538 0.07877-0.275693 0.078769-0.196923 0.078769-0.196923 0.039385-0.157538 0.118154-0.315077a17.683692 17.683692 0 0 1 0.315077-0.708923l0.078769-0.196923a13.942154 13.942154 0 0 1 0.315077-0.630154l0.157538-0.315077 0.078769-0.157539 0.039385-0.039384 0.078769-0.118154 0.196923-0.315077 0.039385-0.118154 0.039385-0.078769 0.078769-0.118154 0.118154-0.196923 0.157538-0.236308a17.841231 17.841231 0 0 1 0.393846-0.590769l0.157539-0.157538a15.517538 15.517538 0 0 1 0.393846-0.59077l0.275692-0.315076 0.078769-0.07877h0.039385l0.118154-0.157538 0.196923-0.236308 0.118154-0.118154 0.078769-0.078769 0.078769-0.118154 0.196923-0.196923 0.157539-0.157538 0.078769-0.07877 0.078769-0.078769 0.196923-0.196923 0.157539-0.118154v-0.039384l0.157538-0.118154 0.315077-0.275692 0.078769-0.039385v-0.039385l0.157539-0.118154a17.880615 17.880615 0 0 1 0.551384-0.43323l0.196924-0.157539 0.196923-0.157538a17.841231 17.841231 0 0 1 0.590769-0.393846l0.157538-0.118154 0.157539-0.078769 0.315077-0.196924 0.078769-0.039384 0.118154-0.078769 0.196923-0.118154 0.236308-0.118154 0.118153-0.078769h0.07877l0.157538-0.118154 0.275692-0.157539 0.157539-0.078769h0.039384l0.118154-0.039384 0.354462-0.196924 0.118154-0.039384h0.078769l0.157538-0.118154 0.315077-0.118154 0.157539-0.078769h0.039384l0.118154-0.039385 0.393846-0.118154 0.07877-0.039384 0.118153-0.039385 0.275693-0.118154 0.196923-0.039384 0.118154-0.039385h0.078769l0.236308-0.078769 0.275692-0.078769 0.118154-0.039385h0.078769l0.393846-0.118154h0.196923l0.157539-0.078769 0.315077-0.039385 0.196923-0.039384h0.118154l0.118153-0.039385 0.354462-0.078769h0.393846l0.315077-0.078769h0.433231l0.236307-0.039385h182.468924V378.564923c0-9.570462 7.561846-17.368615 17.053538-17.762461h0.787692c9.806769 0 17.723077 7.916308 17.723077 17.723076v145.880616H710.459077l0.354461 0.039384h0.236308l0.157539 0.039385 0.393846 0.039385h0.157538l0.078769 0.039384h0.118154l0.236308 0.039385 0.315077 0.078769h0.196923l0.157538 0.078769 0.393847 0.039385h0.078769l0.118154 0.039385 0.196923 0.078769 0.315077 0.078769h0.157538l0.039385 0.039385 0.157538 0.039384 0.354462 0.118154h0.118154l0.078769 0.039385 0.236308 0.078769 0.275692 0.118154h0.078769l0.118154 0.039384 0.236308 0.118154 0.236307 0.07877 0.118154 0.039384 0.078769 0.039385 0.196923 0.078769 0.275693 0.118154 0.118154 0.039384 0.078769 0.07877 0.275692 0.118154 0.196923 0.078769 0.118154 0.078769h0.078769l0.196923 0.157538 0.236308 0.118154h0.078769l0.118154 0.07877 0.630154 0.393846 0.236308 0.157538 0.196923 0.118154 0.118154 0.078769h0.039384l0.393846 0.315077 0.157539 0.118154 0.630154 0.433231 0.315077 0.275692 0.039384 0.078769h0.039385l0.157538 0.118154 0.196923 0.196923 0.157539 0.157539 0.078769 0.039384 0.078769 0.07877 0.157539 0.157538 0.196923 0.196923 0.118154 0.118154 0.039384 0.039385 0.196923 0.196923 0.157539 0.196923 0.157538 0.157538 0.236308 0.275692 0.078769 0.07877 0.039385 0.039384 0.078769 0.118154 0.472615 0.590769 0.118154 0.157539 0.196923 0.236308 0.393847 0.590769 0.078769 0.118154v0.078769l0.393846 0.590769 0.157538 0.236308 0.118154 0.196923 0.078769 0.157538v0.039385l0.07877 0.118154 0.157538 0.354461 0.078769 0.07877v0.118153l0.236308 0.433231 0.078769 0.157539v0.039384l0.118154 0.196923 0.118154 0.315077v0.078769l0.078769 0.118154 0.078769 0.196923 0.07877 0.275693a17.644308 17.644308 0 0 1 0.236307 0.708923v0.039384l0.039385 0.157539 0.118154 0.315077 0.039384 0.196923v0.078769l0.039385 0.118154a17.683692 17.683692 0 0 1 0.157539 0.708923l0.078769 0.315077 0.039384 0.236308v0.196923a17.762462 17.762462 0 0 1 0.118154 0.748307l0.039385 0.393847v0.236307l0.039384 0.118154v0.275692l0.039385 0.275693v0.984615l-1.772308 147.652923a17.801846 17.801846 0 0 1-35.564307-0.433231l1.575384-129.654153H332.130462z" fill="#FFFFFF" p-id="14833"></path></svg>',
  [NodeType.CONDITION_BRANCH_START]:
    '<svg t="1744867229126" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="27328" width="200" height="200"><path d="M1023.43711 511.718555q0 12.537105-0.614063 25.074209-0.614062 12.588276-1.842186 25.074209-1.228125 12.537105-3.070312 24.920694-1.842187 12.434761-4.298436 24.767178-2.456249 12.281245-5.52656 24.511319-3.070311 12.178902-6.652341 24.204287-3.684374 12.025386-7.931638 23.846085-4.196092 11.820699-9.006246 23.43671-4.810154 11.616011-10.234372 22.924991-5.321873 11.411324-11.257808 22.464445t-12.383589 21.850382q-6.447654 10.74609-13.407026 21.23632-7.010544 10.439059-14.481635 20.519914-7.471091 10.080856-15.4539 19.803508t-16.374994 19.03593q-8.443356 9.262106-17.347259 18.166009-8.903903 8.903903-18.166009 17.296087-9.313278 8.443356-19.03593 16.426166t-19.803508 15.4539q-10.080856 7.471091-20.519914 14.481635-10.439059 6.959372-21.23632 13.407026-10.74609 6.447654-21.850382 12.383589-11.053121 5.935935-22.464445 11.257808-11.30898 5.424217-22.924991 10.234372-11.616011 4.810154-23.43671 9.006246-11.820699 4.247264-23.846085 7.880466-12.025386 3.684374-24.204287 6.703513-12.178902 3.070311-24.562491 5.52656-12.281245 2.456249-24.716006 4.298436-12.383589 1.842187-24.920694 3.070312-12.485933 1.228125-25.074209 1.842186Q524.306831 1023.43711 511.718555 1023.43711t-25.074209-0.614063q-12.588276-0.614062-25.07421-1.842186-12.485933-1.228125-24.920693-3.070312t-24.767178-4.298436q-12.281245-2.456249-24.511319-5.52656-12.178902-3.070311-24.204288-6.652341-12.025386-3.684374-23.846084-7.931638-11.820699-4.196092-23.43671-9.006246-11.616011-4.810154-22.976163-10.234372-11.30898-5.321873-22.413273-11.257808-11.053121-5.935935-21.850382-12.383589-10.74609-6.447654-21.23632-13.407026-10.439059-7.010544-20.519914-14.481635-10.080856-7.471091-19.803508-15.4539t-19.035931-16.374994q-9.262106-8.443356-18.166008-17.347259-8.903903-8.903903-17.347259-18.166009-8.392184-9.313278-16.374994-19.03593t-15.4539-19.803508q-7.471091-10.080856-14.481636-20.519914-6.959372-10.439059-13.407026-21.23632-6.447654-10.74609-12.383589-21.850382-5.935935-11.053121-11.257808-22.464445-5.424217-11.30898-10.234371-22.924991-4.810154-11.616011-9.006247-23.43671-4.247264-11.820699-7.880465-23.846085-3.684374-12.025386-6.703513-24.204287-3.070311-12.178902-5.526561-24.562491-2.456249-12.281245-4.298436-24.716006-1.842187-12.383589-3.070311-24.920694-1.228125-12.485933-1.842187-25.074209Q0 524.306831 0 511.718555t0.614062-25.074209q0.614062-12.588276 1.842187-25.07421 1.228125-12.485933 3.070311-24.920693t4.298436-24.767178Q12.281245 399.652191 15.351557 387.370946q3.070311-12.178902 6.652341-24.204288 3.684374-12.025386 7.931637-23.846084 4.196092-11.820699 9.006247-23.43671 4.810154-11.616011 10.234371-22.976163 5.321873-11.30898 11.257808-22.413273 5.935935-11.053121 12.383589-21.850382 6.447654-10.74609 13.407026-21.23632 7.010544-10.439059 14.481636-20.519914 7.471091-10.080856 15.4539-19.803508t16.374994-19.035931q8.443356-9.262106 17.347259-18.166008 8.903903-8.903903 18.166008-17.347259 9.313278-8.392184 19.035931-16.374994t19.803508-15.4539q10.080856-7.471091 20.519914-14.481636 10.439059-6.959372 21.23632-13.407026 10.74609-6.447654 21.850382-12.383589 11.104293-5.935935 22.413273-11.257808 11.360152-5.424217 23.027335-10.234371Q327.499875 34.131628 339.269402 29.935535q11.820699-4.247264 23.846084-7.880465Q375.192044 18.370696 387.370946 15.351557q12.178902-3.070311 24.562491-5.526561 12.281245-2.456249 24.716006-4.298436 12.434761-1.842187 24.920693-3.070311t25.07421-1.842187Q499.130278 0 511.718555 0t25.074209 0.614062q12.588276 0.614062 25.074209 1.842187 12.537105 1.228125 24.920694 3.070311 12.434761 1.842187 24.767178 4.298436 12.281245 2.456249 24.511319 5.526561 12.178902 3.070311 24.204287 6.652341 12.025386 3.684374 23.846085 7.931637 11.820699 4.196092 23.43671 9.006247 11.616011 4.810154 22.924991 10.234371 11.411324 5.321873 22.464445 11.257808t21.850382 12.383589q10.74609 6.447654 21.23632 13.407026 10.439059 7.010544 20.519914 14.481636 10.080856 7.471091 19.803508 15.4539t19.03593 16.374994q9.262106 8.443356 18.166009 17.347259 8.903903 8.903903 17.296087 18.166008 8.443356 9.313278 16.426166 19.035931t15.4539 19.803508q7.471091 10.080856 14.481635 20.519914 6.959372 10.439059 13.407026 21.23632 6.447654 10.74609 12.383589 21.850382 5.935935 11.104293 11.257808 22.413273 5.424217 11.360152 10.234372 23.027335 4.810154 11.564839 9.006246 23.385538 4.247264 11.820699 7.880466 23.846084 3.684374 12.025386 6.703513 24.204288 3.070311 12.178902 5.52656 24.562491 2.456249 12.281245 4.298436 24.716006 1.842187 12.434761 3.070312 24.920693t1.842186 25.07421Q1023.43711 499.130278 1023.43711 511.718555z" fill="#70B601" p-id="27329"></path><path d="M460.546699 358.202988c-6.242966-40.221078-9.671481 25.585928-49.483184 25.585928H358.202988c-41.398031 0.307031-46.208185-95.486682-50.353105-53.372245-4.093748 42.063265 23.487882 80.595672 63.964819 89.294888v235.083504c-40.067563 8.392184-67.751537 45.952326-64.52771 87.657388 3.223827 41.705062 36.332017 74.352706 77.167158 76.246065a81.465594 81.465594 0 0 0 83.410125-69.030833c6.805857-41.244516-17.551946-81.158563-56.749588-93.132777l-0.051172-169.481186c21.389836 20.161711 51.888261 39.914047 91.29059 59.56404 80.442157 39.504672 121.07261 76.246065 126.701514 108.535506-39.811704 9.210934-66.676928 47.385138-62.583179 88.936685 4.042577 41.500375 37.713657 73.482784 78.548798 74.455049a81.465594 81.465594 0 0 0 81.977313-70.565988c5.987107-41.295687-19.03593-80.698016-58.387087-91.853481-3.58203-51.069512-54.907401-98.864025-149.319475-145.225726-71.282395-35.615611-105.721053-68.570286-106.283943-94.719104l0.102343 1.176952c37.867173-12.639448 53.730448-18.933587 47.43631-59.154665z" fill="#FFFFFF" p-id="27330"></path><path d="M394.023287 421.349058a102.343711 102.343711 0 1 0 0-204.687422 102.343711 102.343711 0 0 0 0 204.687422z m-30.703113-71.538254V288.19989a16.374994 16.374994 0 0 1 25.432412-13.662885l46.208186 30.805457a16.374994 16.374994 0 0 1 0 27.274599l-46.208186 30.805457a16.374994 16.374994 0 0 1-25.483584-13.611714z" fill="#FFFFFF" p-id="27331"></path></svg>',
  [NodeType.CONDITION_BRANCH_END]:
    '<svg t="1744867212596" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="27179" width="200" height="200"><path d="M1023.43711 511.718555q0 12.537105-0.614063 25.074209-0.614062 12.588276-1.842186 25.074209-1.228125 12.537105-3.070312 24.920694-1.842187 12.434761-4.298436 24.767178-2.456249 12.281245-5.52656 24.511319-3.070311 12.178902-6.652341 24.204287-3.684374 12.025386-7.931638 23.846085-4.196092 11.820699-9.006246 23.43671-4.810154 11.616011-10.234372 22.924991-5.321873 11.411324-11.257808 22.464445t-12.383589 21.850382q-6.447654 10.74609-13.407026 21.23632-7.010544 10.439059-14.481635 20.519914-7.471091 10.080856-15.4539 19.803508t-16.374994 19.03593q-8.443356 9.262106-17.347259 18.166009-8.903903 8.903903-18.166009 17.296087-9.313278 8.443356-19.03593 16.426166t-19.803508 15.4539q-10.080856 7.471091-20.519914 14.481635-10.439059 6.959372-21.23632 13.407026-10.74609 6.447654-21.850382 12.383589-11.053121 5.935935-22.464445 11.257808-11.30898 5.424217-22.924991 10.234372-11.616011 4.810154-23.43671 9.006246-11.820699 4.247264-23.846085 7.880466-12.025386 3.684374-24.204287 6.703513-12.178902 3.070311-24.562491 5.52656-12.281245 2.456249-24.716006 4.298436-12.383589 1.842187-24.920694 3.070312-12.485933 1.228125-25.074209 1.842186Q524.306831 1023.43711 511.718555 1023.43711t-25.074209-0.614063q-12.588276-0.614062-25.07421-1.842186-12.485933-1.228125-24.920693-3.070312t-24.767178-4.298436q-12.281245-2.456249-24.511319-5.52656-12.178902-3.070311-24.204288-6.652341-12.025386-3.684374-23.846084-7.931638-11.820699-4.196092-23.43671-9.006246-11.616011-4.810154-22.976163-10.234372-11.30898-5.321873-22.413273-11.257808-11.053121-5.935935-21.850382-12.383589-10.74609-6.447654-21.23632-13.407026-10.439059-7.010544-20.519914-14.481635-10.080856-7.471091-19.803508-15.4539t-19.035931-16.374994q-9.262106-8.443356-18.166008-17.347259-8.903903-8.903903-17.347259-18.166009-8.392184-9.313278-16.374994-19.03593t-15.4539-19.803508q-7.471091-10.080856-14.481636-20.519914-6.959372-10.439059-13.407026-21.23632-6.447654-10.74609-12.383589-21.850382-5.935935-11.053121-11.257808-22.464445-5.424217-11.30898-10.234371-22.924991-4.810154-11.616011-9.006247-23.43671-4.247264-11.820699-7.880465-23.846085-3.684374-12.025386-6.703513-24.204287-3.070311-12.178902-5.526561-24.562491-2.456249-12.281245-4.298436-24.716006-1.842187-12.383589-3.070311-24.920694-1.228125-12.485933-1.842187-25.074209Q0 524.306831 0 511.718555t0.614062-25.074209q0.614062-12.588276 1.842187-25.07421 1.228125-12.485933 3.070311-24.920693t4.298436-24.767178Q12.281245 399.652191 15.351557 387.370946q3.070311-12.178902 6.652341-24.204288 3.684374-12.025386 7.931637-23.846084 4.196092-11.820699 9.006247-23.43671 4.810154-11.616011 10.234371-22.976163 5.321873-11.30898 11.257808-22.413273 5.935935-11.053121 12.383589-21.850382 6.447654-10.74609 13.407026-21.23632 7.010544-10.439059 14.481636-20.519914 7.471091-10.080856 15.4539-19.803508t16.374994-19.035931q8.443356-9.262106 17.347259-18.166008 8.903903-8.903903 18.166008-17.347259 9.313278-8.392184 19.035931-16.374994t19.803508-15.4539q10.080856-7.471091 20.519914-14.481636 10.439059-6.959372 21.23632-13.407026 10.74609-6.447654 21.850382-12.383589 11.104293-5.935935 22.413273-11.257808 11.360152-5.424217 23.027335-10.234371Q327.499875 34.131628 339.269402 29.935535q11.820699-4.247264 23.846084-7.880465Q375.192044 18.370696 387.370946 15.351557q12.178902-3.070311 24.562491-5.526561 12.281245-2.456249 24.716006-4.298436 12.434761-1.842187 24.920693-3.070311t25.07421-1.842187Q499.130278 0 511.718555 0t25.074209 0.614062q12.588276 0.614062 25.074209 1.842187 12.537105 1.228125 24.920694 3.070311 12.434761 1.842187 24.767178 4.298436 12.281245 2.456249 24.511319 5.526561 12.178902 3.070311 24.204287 6.652341 12.025386 3.684374 23.846085 7.931637 11.820699 4.196092 23.43671 9.006247 11.616011 4.810154 22.924991 10.234371 11.411324 5.321873 22.464445 11.257808t21.850382 12.383589q10.74609 6.447654 21.23632 13.407026 10.439059 7.010544 20.519914 14.481636 10.080856 7.471091 19.803508 15.4539t19.03593 16.374994q9.262106 8.443356 18.166009 17.347259 8.903903 8.903903 17.296087 18.166008 8.443356 9.313278 16.426166 19.035931t15.4539 19.803508q7.471091 10.080856 14.481635 20.519914 6.959372 10.439059 13.407026 21.23632 6.447654 10.74609 12.383589 21.850382 5.935935 11.104293 11.257808 22.413273 5.424217 11.360152 10.234372 23.027335 4.810154 11.564839 9.006246 23.385538 4.247264 11.820699 7.880466 23.846084 3.684374 12.025386 6.703513 24.204288 3.070311 12.178902 5.52656 24.562491 2.456249 12.281245 4.298436 24.716006 1.842187 12.434761 3.070312 24.920693t1.842186 25.07421Q1023.43711 499.130278 1023.43711 511.718555z" fill="#70B601" p-id="27180"></path><path d="M460.546699 358.202988c-6.242966-40.221078-9.671481 25.585928-49.483184 25.585928H358.202988c-41.398031 0.307031-46.208185-95.486682-50.353105-53.372245-4.093748 42.063265 23.487882 80.595672 63.964819 89.294888v235.083504c-40.067563 8.392184-67.751537 45.952326-64.52771 87.657388 3.223827 41.705062 36.332017 74.352706 77.167158 76.246065a81.465594 81.465594 0 0 0 83.410125-69.030833c6.805857-41.244516-17.551946-81.158563-56.749588-93.132777l-0.051172-169.481186c21.389836 20.161711 51.888261 39.914047 91.29059 59.56404 80.442157 39.504672 121.07261 76.246065 126.701514 108.535506-39.811704 9.210934-66.676928 47.385138-62.583179 88.936685 4.042577 41.500375 37.713657 73.482784 78.548798 74.455049a81.465594 81.465594 0 0 0 81.977313-70.565988c5.987107-41.295687-19.03593-80.698016-58.387087-91.853481-3.58203-51.069512-54.907401-98.864025-149.319475-145.225726-71.282395-35.615611-105.721053-68.570286-106.283943-94.719104l0.102343 1.176952c37.867173-12.639448 53.730448-18.933587 47.43631-59.154665z" fill="#FFFFFF" p-id="27181"></path><path d="M394.023287 421.349058a102.343711 102.343711 0 1 0 0-204.687422 102.343711 102.343711 0 0 0 0 204.687422z m-30.703113-71.538254V288.19989a16.374994 16.374994 0 0 1 25.432412-13.662885l46.208186 30.805457a16.374994 16.374994 0 0 1 0 27.274599l-46.208186 30.805457a16.374994 16.374994 0 0 1-25.483584-13.611714z" fill="#FFFFFF" p-id="27182"></path></svg>',
  [NodeType.PARALLEL_BRANCH_START]:
    '<svg t="1744867167740" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="26881" width="200" height="200"><path d="M1023.43711 511.718555q0 12.537105-0.614063 25.074209-0.614062 12.588276-1.842186 25.074209-1.228125 12.537105-3.070312 24.920694-1.842187 12.434761-4.298436 24.767178-2.456249 12.281245-5.52656 24.511319-3.070311 12.178902-6.652341 24.204287-3.684374 12.025386-7.931638 23.846085-4.196092 11.820699-9.006246 23.43671-4.810154 11.616011-10.234372 22.924991-5.321873 11.411324-11.257808 22.464445t-12.383589 21.850382q-6.447654 10.74609-13.407026 21.23632-7.010544 10.439059-14.481635 20.519914-7.471091 10.080856-15.4539 19.803508t-16.374994 19.03593q-8.443356 9.262106-17.347259 18.166009-8.903903 8.903903-18.166009 17.296087-9.313278 8.443356-19.03593 16.426166t-19.803508 15.4539q-10.080856 7.471091-20.519914 14.481635-10.439059 6.959372-21.23632 13.407026-10.74609 6.447654-21.850382 12.383589-11.053121 5.935935-22.464445 11.257808-11.30898 5.424217-22.924991 10.234372-11.616011 4.810154-23.43671 9.006246-11.820699 4.247264-23.846085 7.880466-12.025386 3.684374-24.204287 6.703513-12.178902 3.070311-24.562491 5.52656-12.281245 2.456249-24.716006 4.298436-12.383589 1.842187-24.920694 3.070312-12.485933 1.228125-25.074209 1.842186Q524.306831 1023.43711 511.718555 1023.43711t-25.074209-0.614063q-12.588276-0.614062-25.07421-1.842186-12.485933-1.228125-24.920693-3.070312t-24.767178-4.298436q-12.281245-2.456249-24.511319-5.52656-12.178902-3.070311-24.204288-6.652341-12.025386-3.684374-23.846084-7.931638-11.820699-4.196092-23.43671-9.006246-11.616011-4.810154-22.976163-10.234372-11.30898-5.321873-22.413273-11.257808-11.053121-5.935935-21.850382-12.383589-10.74609-6.447654-21.23632-13.407026-10.439059-7.010544-20.519914-14.481635-10.080856-7.471091-19.803508-15.4539t-19.035931-16.374994q-9.262106-8.443356-18.166008-17.347259-8.903903-8.903903-17.347259-18.166009-8.392184-9.313278-16.374994-19.03593t-15.4539-19.803508q-7.471091-10.080856-14.481636-20.519914-6.959372-10.439059-13.407026-21.23632-6.447654-10.74609-12.383589-21.850382-5.935935-11.053121-11.257808-22.464445-5.424217-11.30898-10.234371-22.924991-4.810154-11.616011-9.006247-23.43671-4.247264-11.820699-7.880465-23.846085-3.684374-12.025386-6.703513-24.204287-3.070311-12.178902-5.526561-24.562491-2.456249-12.281245-4.298436-24.716006-1.842187-12.383589-3.070311-24.920694-1.228125-12.485933-1.842187-25.074209Q0 524.306831 0 511.718555t0.614062-25.074209q0.614062-12.588276 1.842187-25.07421 1.228125-12.485933 3.070311-24.920693t4.298436-24.767178Q12.281245 399.652191 15.351557 387.370946q3.070311-12.178902 6.652341-24.204288 3.684374-12.025386 7.931637-23.846084 4.196092-11.820699 9.006247-23.43671 4.810154-11.616011 10.234371-22.976163 5.321873-11.30898 11.257808-22.413273 5.935935-11.053121 12.383589-21.850382 6.447654-10.74609 13.407026-21.23632 7.010544-10.439059 14.481636-20.519914 7.471091-10.080856 15.4539-19.803508t16.374994-19.035931q8.443356-9.262106 17.347259-18.166008 8.903903-8.903903 18.166008-17.347259 9.313278-8.392184 19.035931-16.374994t19.803508-15.4539q10.080856-7.471091 20.519914-14.481636 10.439059-6.959372 21.23632-13.407026 10.74609-6.447654 21.850382-12.383589 11.104293-5.935935 22.413273-11.257808 11.360152-5.424217 23.027335-10.234371Q327.499875 34.131628 339.269402 29.935535q11.820699-4.247264 23.846084-7.880465Q375.192044 18.370696 387.370946 15.351557q12.178902-3.070311 24.562491-5.526561 12.281245-2.456249 24.716006-4.298436 12.434761-1.842187 24.920693-3.070311t25.07421-1.842187Q499.130278 0 511.718555 0t25.074209 0.614062q12.588276 0.614062 25.074209 1.842187 12.537105 1.228125 24.920694 3.070311 12.434761 1.842187 24.767178 4.298436 12.281245 2.456249 24.511319 5.526561 12.178902 3.070311 24.204287 6.652341 12.025386 3.684374 23.846085 7.931637 11.820699 4.196092 23.43671 9.006247 11.616011 4.810154 22.924991 10.234371 11.411324 5.321873 22.464445 11.257808t21.850382 12.383589q10.74609 6.447654 21.23632 13.407026 10.439059 7.010544 20.519914 14.481636 10.080856 7.471091 19.803508 15.4539t19.03593 16.374994q9.262106 8.443356 18.166009 17.347259 8.903903 8.903903 17.296087 18.166008 8.443356 9.313278 16.426166 19.035931t15.4539 19.803508q7.471091 10.080856 14.481635 20.519914 6.959372 10.439059 13.407026 21.23632 6.447654 10.74609 12.383589 21.850382 5.935935 11.104293 11.257808 22.413273 5.424217 11.360152 10.234372 23.027335 4.810154 11.564839 9.006246 23.385538 4.247264 11.820699 7.880466 23.846084 3.684374 12.025386 6.703513 24.204288 3.070311 12.178902 5.52656 24.562491 2.456249 12.281245 4.298436 24.716006 1.842187 12.434761 3.070312 24.920693t1.842186 25.07421Q1023.43711 499.130278 1023.43711 511.718555z" fill="#70B601" p-id="26882"></path><path d="M460.546699 383.788916c9.876168 9.569137 20.878117 24.050772 34.387487 26.762881V501.995902H323.713158l-0.25586 0.051172h-0.409374l-0.25586 0.102344H322.38269l-0.307032 0.051172H321.870971l-0.204687 0.102343H321.359252l-0.153515 0.102344h-0.153516l-0.409375 0.153516h-0.153515l-0.255859 0.102343-0.25586 0.102344H319.824097l-0.204688 0.051172-0.255859 0.102344-0.102344 0.051172-0.051172 0.051171-0.409374 0.102344-0.102344 0.051172-0.153516 0.102344-0.307031 0.102343-0.153515 0.153516h-0.204688l-0.307031 0.204687-0.102344 0.051172-0.204687 0.102344-0.255859 0.153515-0.153516 0.102344h-0.051172l-0.153515 0.102344-0.204688 0.102344-0.153515 0.153515-0.153516 0.051172-0.051172 0.051172-0.307031 0.204687-0.153515 0.102344-0.153516 0.102344a16.374994 16.374994 0 0 0-0.511719 0.409375l-0.204687 0.153515-0.204687 0.153516a17.193743 17.193743 0 0 0-0.511719 0.511718l-0.153515 0.102344-0.051172 0.051172-0.307031 0.307031-0.153516 0.102344-0.153516 0.153515-0.153515 0.204688-0.102344 0.102343-0.051172 0.102344-0.153515 0.153516-0.204688 0.204687-0.051172 0.102344-0.102343 0.102343-0.102344 0.102344-0.153516 0.255859-0.153515 0.153516-0.102344 0.102344-0.255859 0.307031a16.016791 16.016791 0 0 0-0.358203 0.614062l-0.153516 0.153516a17.910149 17.910149 0 0 0-0.358203 0.614062l-0.153515 0.255859-0.102344 0.204688-0.102344 0.102343v0.102344l-0.051172 0.102344-0.204687 0.358203-0.051172 0.102344-0.102344 0.204687-0.153515 0.307031a15.044526 15.044526 0 0 0-0.307031 0.665234l-0.051172 0.204688a19.445305 19.445305 0 0 0-0.307031 0.716406l-0.102344 0.358203-0.051172 0.153515-0.153515 0.409375-0.051172 0.307031-0.102344 0.153516v0.255859l-0.153515 0.307031v0.255859l-0.051172 0.204688-0.102344 0.307031v0.102344l-0.051172 0.102343v0.204688l-0.051172 0.307031-0.051172 0.255859v0.204688l-0.102343 0.307031v0.255859l-0.051172 0.307031V518.831443l-0.102344 0.204687v1.176953l-1.279296 115.392534c-42.779671 2.558593-76.450752 36.69022-76.450752 78.702314 0 20.929289 8.5457 40.988656 23.846084 55.777322 15.300385 14.839838 36.024986 23.129679 57.670681 23.078507 21.645695 0 42.421468-8.238669 57.721853-23.027335 15.300385-14.839838 23.846085-34.899205 23.794913-55.777323 0-34.131628-22.106242-62.992554-53.269901-74.096846l1.12578-101.371446v-0.102344h337.222528l-1.125781 101.883165c-34.796862 6.038279-61.099195 37.560142-61.099195 75.734346 0 20.366398 7.675778 39.914047 21.389835 54.34451 13.765229 14.379291 32.391785 22.464445 51.83709 22.413273 19.445305 0 38.123032-8.033981 51.83709-22.413273 13.765229-14.430463 21.492179-33.978112 21.441007-54.34451 0-34.796862-21.901554-64.015991-52.041777-73.533957l1.330468-122.300734V519.087302l-0.102343-0.153516V518.21738a20.724601 20.724601 0 0 0-0.153516-0.767577v-0.409375l-0.102344-0.358203a20.724601 20.724601 0 0 0-0.153515-0.716406v-0.204688l-0.051172-0.204687-0.102344-0.307031v-0.204688a10.029684 10.029684 0 0 0-0.255859-0.767577l-0.102344-0.25586-0.051172-0.204687-0.102343-0.153516v-0.051172l-0.102344-0.358203-0.102344-0.204687-0.051172-0.204687-0.255859-0.409375v-0.153516l-0.051172-0.051172-0.153515-0.409374-0.051172-0.102344-0.102344-0.204688-0.102344-0.204687-0.153515-0.255859-0.358203-0.614063v-0.051171l-0.102344-0.153516-0.358203-0.614062-0.153515-0.204688-0.153516-0.204687-0.409375-0.614062-0.102344-0.102344-0.102343-0.102344-0.204688-0.307031-0.153515-0.153516-0.153516-0.204687-0.204687-0.204687-0.153516-0.153516-0.153515-0.204687-0.153516-0.153516-0.051172-0.102344h-0.102344l-0.153515-0.204687-0.153516-0.204687-0.153515-0.102344-0.102344-0.102344-0.307031-0.307031-0.56289-0.409375-0.153516-0.153515-0.358203-0.307032-0.153516-0.102343-0.204687-0.102344-0.204687-0.153516-0.614063-0.409374-0.102343-0.102344h-0.051172l-0.25586-0.102344-0.153515-0.153515h-0.102344l-0.102344-0.102344-0.153515-0.102344-0.255859-0.102344-0.102344-0.102343h-0.102344l-0.255859-0.153516-0.204688-0.051172-0.051171-0.051172h-0.102344l-0.255859-0.153515-0.204688-0.102344h-0.204687l-0.255859-0.153515-0.204688-0.102344h-0.204687l-0.307031-0.153516h-0.153516l-0.204687-0.051171-0.25586-0.102344-0.204687-0.102344h-0.204688l-0.358203-0.051172-0.153515-0.102343h-0.153516l-0.307031-0.051172-0.204687-0.051172h-0.153516l-0.051172-0.051172h-0.511718l-0.153516-0.102344H528.298236V409.374844c11.616011-3.121483 25.892959-17.193743 34.592174-25.585928 13.304682-12.792964 25.585928-7.419919 25.585928-25.585928 0-37.867173-37.50897 51.171855-76.757783 51.171856S434.960772 320.335815 434.960772 358.202988c0 18.166009 12.281245 12.792964 25.585927 25.585928z" fill="#FFFFFF" p-id="26883"></path><path d="M511.718555 409.374844q5.014842 0 10.029683-0.511719t9.92734-1.432812q4.96367-1.023437 9.722653-2.456249 4.861326-1.483984 9.466793-3.377342 4.656639-1.944531 9.108591-4.298436 4.40078-2.353905 8.596871-5.168357 4.196092-2.814452 8.085153-5.987108 3.889061-3.172655 7.419919-6.754684 3.58203-3.58203 6.754685-7.41992 3.172655-3.889061 5.987107-8.085153 2.814452-4.196092 5.117186-8.596871 2.405077-4.451951 4.349608-9.108591 1.893359-4.605467 3.377342-9.415621 1.432812-4.810154 2.456249-9.722653 0.972265-4.96367 1.432812-9.978511Q614.062266 312.045975 614.062266 307.031133t-0.511719-10.029684q-0.511719-5.014842-1.432812-9.92734-1.023437-4.96367-2.456249-9.722652-1.483984-4.861326-3.377342-9.466794-1.944531-4.656639-4.298436-9.10859-2.353905-4.40078-5.168358-8.596872-2.814452-4.196092-5.987107-8.085153-3.172655-3.889061-6.754685-7.419919-3.58203-3.58203-7.419919-6.754685-3.889061-3.172655-8.085153-5.987107-4.196092-2.814452-8.596871-5.117185-4.451951-2.405077-9.108591-4.349608-4.605467-1.893359-9.415621-3.377343-4.810154-1.432812-9.722653-2.456249-4.96367-0.972265-9.978512-1.432812Q516.733397 204.687422 511.718555 204.687422t-10.029684 0.511718q-5.014842 0.511719-9.92734 1.432812-4.96367 1.023437-9.722652 2.456249-4.861326 1.483984-9.466794 3.377343-4.656639 1.944531-9.10859 4.298436-4.40078 2.353905-8.596872 5.168357-4.196092 2.814452-8.085153 5.987107-3.889061 3.172655-7.419919 6.754685-3.58203 3.58203-6.754685 7.419919-3.172655 3.889061-5.987107 8.085153-2.814452 4.196092-5.117185 8.596872-2.405077 4.451951-4.349608 9.10859-1.893359 4.605467-3.377343 9.415622-1.432812 4.810154-2.456249 9.722652-0.972265 4.96367-1.432812 9.978512Q409.374844 302.016291 409.374844 307.031133t0.511718 10.029684q0.511719 5.014842 1.432812 9.92734 1.023437 4.96367 2.456249 9.722652 1.483984 4.861326 3.377343 9.466793 1.944531 4.656639 4.298436 9.108591 2.353905 4.40078 5.168357 8.596871 2.814452 4.196092 5.987107 8.085153 3.172655 3.889061 6.754685 7.41992 3.58203 3.58203 7.419919 6.754684 3.889061 3.172655 8.085153 5.987108 4.196092 2.814452 8.596872 5.117185 4.451951 2.405077 9.10859 4.349608 4.605467 1.893359 9.415622 3.377342 4.810154 1.432812 9.722652 2.456249 4.96367 0.972265 9.978512 1.432812Q506.703713 409.374844 511.718555 409.374844z m-30.703113-71.538254V276.225676a16.374994 16.374994 0 0 1 25.483584-13.611714l46.208185 30.805457a16.374994 16.374994 0 0 1 0 27.223427l-46.208185 30.805457a16.374994 16.374994 0 0 1-25.483584-13.611713z" fill="#FFFFFF" p-id="26884"></path></svg>',
  [NodeType.PARALLEL_BRANCH_END]:
    '<svg t="1744867192307" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="27030" width="200" height="200"><path d="M1023.43711 511.718555q0 12.537105-0.614063 25.074209-0.614062 12.588276-1.842186 25.074209-1.228125 12.537105-3.070312 24.920694-1.842187 12.434761-4.298436 24.767178-2.456249 12.281245-5.52656 24.511319-3.070311 12.178902-6.652341 24.204287-3.684374 12.025386-7.931638 23.846085-4.196092 11.820699-9.006246 23.43671-4.810154 11.616011-10.234372 22.924991-5.321873 11.411324-11.257808 22.464445t-12.383589 21.850382q-6.447654 10.74609-13.407026 21.23632-7.010544 10.439059-14.481635 20.519914-7.471091 10.080856-15.4539 19.803508t-16.374994 19.03593q-8.443356 9.262106-17.347259 18.166009-8.903903 8.903903-18.166009 17.296087-9.313278 8.443356-19.03593 16.426166t-19.803508 15.4539q-10.080856 7.471091-20.519914 14.481635-10.439059 6.959372-21.23632 13.407026-10.74609 6.447654-21.850382 12.383589-11.053121 5.935935-22.464445 11.257808-11.30898 5.424217-22.924991 10.234372-11.616011 4.810154-23.43671 9.006246-11.820699 4.247264-23.846085 7.880466-12.025386 3.684374-24.204287 6.703513-12.178902 3.070311-24.562491 5.52656-12.281245 2.456249-24.716006 4.298436-12.383589 1.842187-24.920694 3.070312-12.485933 1.228125-25.074209 1.842186Q524.306831 1023.43711 511.718555 1023.43711t-25.074209-0.614063q-12.588276-0.614062-25.07421-1.842186-12.485933-1.228125-24.920693-3.070312t-24.767178-4.298436q-12.281245-2.456249-24.511319-5.52656-12.178902-3.070311-24.204288-6.652341-12.025386-3.684374-23.846084-7.931638-11.820699-4.196092-23.43671-9.006246-11.616011-4.810154-22.976163-10.234372-11.30898-5.321873-22.413273-11.257808-11.053121-5.935935-21.850382-12.383589-10.74609-6.447654-21.23632-13.407026-10.439059-7.010544-20.519914-14.481635-10.080856-7.471091-19.803508-15.4539t-19.035931-16.374994q-9.262106-8.443356-18.166008-17.347259-8.903903-8.903903-17.347259-18.166009-8.392184-9.313278-16.374994-19.03593t-15.4539-19.803508q-7.471091-10.080856-14.481636-20.519914-6.959372-10.439059-13.407026-21.23632-6.447654-10.74609-12.383589-21.850382-5.935935-11.053121-11.257808-22.464445-5.424217-11.30898-10.234371-22.924991-4.810154-11.616011-9.006247-23.43671-4.247264-11.820699-7.880465-23.846085-3.684374-12.025386-6.703513-24.204287-3.070311-12.178902-5.526561-24.562491-2.456249-12.281245-4.298436-24.716006-1.842187-12.383589-3.070311-24.920694-1.228125-12.485933-1.842187-25.074209Q0 524.306831 0 511.718555t0.614062-25.074209q0.614062-12.588276 1.842187-25.07421 1.228125-12.485933 3.070311-24.920693t4.298436-24.767178Q12.281245 399.652191 15.351557 387.370946q3.070311-12.178902 6.652341-24.204288 3.684374-12.025386 7.931637-23.846084 4.196092-11.820699 9.006247-23.43671 4.810154-11.616011 10.234371-22.976163 5.321873-11.30898 11.257808-22.413273 5.935935-11.053121 12.383589-21.850382 6.447654-10.74609 13.407026-21.23632 7.010544-10.439059 14.481636-20.519914 7.471091-10.080856 15.4539-19.803508t16.374994-19.035931q8.443356-9.262106 17.347259-18.166008 8.903903-8.903903 18.166008-17.347259 9.313278-8.392184 19.035931-16.374994t19.803508-15.4539q10.080856-7.471091 20.519914-14.481636 10.439059-6.959372 21.23632-13.407026 10.74609-6.447654 21.850382-12.383589 11.104293-5.935935 22.413273-11.257808 11.360152-5.424217 23.027335-10.234371Q327.499875 34.131628 339.269402 29.935535q11.820699-4.247264 23.846084-7.880465Q375.192044 18.370696 387.370946 15.351557q12.178902-3.070311 24.562491-5.526561 12.281245-2.456249 24.716006-4.298436 12.434761-1.842187 24.920693-3.070311t25.07421-1.842187Q499.130278 0 511.718555 0t25.074209 0.614062q12.588276 0.614062 25.074209 1.842187 12.537105 1.228125 24.920694 3.070311 12.434761 1.842187 24.767178 4.298436 12.281245 2.456249 24.511319 5.526561 12.178902 3.070311 24.204287 6.652341 12.025386 3.684374 23.846085 7.931637 11.820699 4.196092 23.43671 9.006247 11.616011 4.810154 22.924991 10.234371 11.411324 5.321873 22.464445 11.257808t21.850382 12.383589q10.74609 6.447654 21.23632 13.407026 10.439059 7.010544 20.519914 14.481636 10.080856 7.471091 19.803508 15.4539t19.03593 16.374994q9.262106 8.443356 18.166009 17.347259 8.903903 8.903903 17.296087 18.166008 8.443356 9.313278 16.426166 19.035931t15.4539 19.803508q7.471091 10.080856 14.481635 20.519914 6.959372 10.439059 13.407026 21.23632 6.447654 10.74609 12.383589 21.850382 5.935935 11.104293 11.257808 22.413273 5.424217 11.360152 10.234372 23.027335 4.810154 11.564839 9.006246 23.385538 4.247264 11.820699 7.880466 23.846084 3.684374 12.025386 6.703513 24.204288 3.070311 12.178902 5.52656 24.562491 2.456249 12.281245 4.298436 24.716006 1.842187 12.434761 3.070312 24.920693t1.842186 25.07421Q1023.43711 499.130278 1023.43711 511.718555z" fill="#70B601" p-id="27031"></path><path d="M460.546699 383.788916c9.876168 9.569137 20.878117 24.050772 34.387487 26.762881V501.995902H323.713158l-0.25586 0.051172h-0.409374l-0.25586 0.102344H322.38269l-0.307032 0.051172H321.870971l-0.204687 0.102343H321.359252l-0.153515 0.102344h-0.153516l-0.409375 0.153516h-0.153515l-0.255859 0.102343-0.25586 0.102344H319.824097l-0.204688 0.051172-0.255859 0.102344-0.102344 0.051172-0.051172 0.051171-0.409374 0.102344-0.102344 0.051172-0.153516 0.102344-0.307031 0.102343-0.153515 0.153516h-0.204688l-0.307031 0.204687-0.102344 0.051172-0.204687 0.102344-0.255859 0.153515-0.153516 0.102344h-0.051172l-0.153515 0.102344-0.204688 0.102344-0.153515 0.153515-0.153516 0.051172-0.051172 0.051172-0.307031 0.204687-0.153515 0.102344-0.153516 0.102344a16.374994 16.374994 0 0 0-0.511719 0.409375l-0.204687 0.153515-0.204687 0.153516a17.193743 17.193743 0 0 0-0.511719 0.511718l-0.153515 0.102344-0.051172 0.051172-0.307031 0.307031-0.153516 0.102344-0.153516 0.153515-0.153515 0.204688-0.102344 0.102343-0.051172 0.102344-0.153515 0.153516-0.204688 0.204687-0.051172 0.102344-0.102343 0.102343-0.102344 0.102344-0.153516 0.255859-0.153515 0.153516-0.102344 0.102344-0.255859 0.307031a16.016791 16.016791 0 0 0-0.358203 0.614062l-0.153516 0.153516a17.910149 17.910149 0 0 0-0.358203 0.614062l-0.153515 0.255859-0.102344 0.204688-0.102344 0.102343v0.102344l-0.051172 0.102344-0.204687 0.358203-0.051172 0.102344-0.102344 0.204687-0.153515 0.307031a15.044526 15.044526 0 0 0-0.307031 0.665234l-0.051172 0.204688a19.445305 19.445305 0 0 0-0.307031 0.716406l-0.102344 0.358203-0.051172 0.153515-0.153515 0.409375-0.051172 0.307031-0.102344 0.153516v0.255859l-0.153515 0.307031v0.255859l-0.051172 0.204688-0.102344 0.307031v0.102344l-0.051172 0.102343v0.204688l-0.051172 0.307031-0.051172 0.255859v0.204688l-0.102343 0.307031v0.255859l-0.051172 0.307031V518.831443l-0.102344 0.204687v1.176953l-1.279296 115.392534c-42.779671 2.558593-76.450752 36.69022-76.450752 78.702314 0 20.929289 8.5457 40.988656 23.846084 55.777322 15.300385 14.839838 36.024986 23.129679 57.670681 23.078507 21.645695 0 42.421468-8.238669 57.721853-23.027335 15.300385-14.839838 23.846085-34.899205 23.794913-55.777323 0-34.131628-22.106242-62.992554-53.269901-74.096846l1.12578-101.371446v-0.102344h337.222528l-1.125781 101.883165c-34.796862 6.038279-61.099195 37.560142-61.099195 75.734346 0 20.366398 7.675778 39.914047 21.389835 54.34451 13.765229 14.379291 32.391785 22.464445 51.83709 22.413273 19.445305 0 38.123032-8.033981 51.83709-22.413273 13.765229-14.430463 21.492179-33.978112 21.441007-54.34451 0-34.796862-21.901554-64.015991-52.041777-73.533957l1.330468-122.300734V519.087302l-0.102343-0.153516V518.21738a20.724601 20.724601 0 0 0-0.153516-0.767577v-0.409375l-0.102344-0.358203a20.724601 20.724601 0 0 0-0.153515-0.716406v-0.204688l-0.051172-0.204687-0.102344-0.307031v-0.204688a10.029684 10.029684 0 0 0-0.255859-0.767577l-0.102344-0.25586-0.051172-0.204687-0.102343-0.153516v-0.051172l-0.102344-0.358203-0.102344-0.204687-0.051172-0.204687-0.255859-0.409375v-0.153516l-0.051172-0.051172-0.153515-0.409374-0.051172-0.102344-0.102344-0.204688-0.102344-0.204687-0.153515-0.255859-0.358203-0.614063v-0.051171l-0.102344-0.153516-0.358203-0.614062-0.153515-0.204688-0.153516-0.204687-0.409375-0.614062-0.102344-0.102344-0.102343-0.102344-0.204688-0.307031-0.153515-0.153516-0.153516-0.204687-0.204687-0.204687-0.153516-0.153516-0.153515-0.204687-0.153516-0.153516-0.051172-0.102344h-0.102344l-0.153515-0.204687-0.153516-0.204687-0.153515-0.102344-0.102344-0.102344-0.307031-0.307031-0.56289-0.409375-0.153516-0.153515-0.358203-0.307032-0.153516-0.102343-0.204687-0.102344-0.204687-0.153516-0.614063-0.409374-0.102343-0.102344h-0.051172l-0.25586-0.102344-0.153515-0.153515h-0.102344l-0.102344-0.102344-0.153515-0.102344-0.255859-0.102344-0.102344-0.102343h-0.102344l-0.255859-0.153516-0.204688-0.051172-0.051171-0.051172h-0.102344l-0.255859-0.153515-0.204688-0.102344h-0.204687l-0.255859-0.153515-0.204688-0.102344h-0.204687l-0.307031-0.153516h-0.153516l-0.204687-0.051171-0.25586-0.102344-0.204687-0.102344h-0.204688l-0.358203-0.051172-0.153515-0.102343h-0.153516l-0.307031-0.051172-0.204687-0.051172h-0.153516l-0.051172-0.051172h-0.511718l-0.153516-0.102344H528.298236V409.374844c11.616011-3.121483 25.892959-17.193743 34.592174-25.585928 13.304682-12.792964 25.585928-7.419919 25.585928-25.585928 0-37.867173-37.50897 51.171855-76.757783 51.171856S434.960772 320.335815 434.960772 358.202988c0 18.166009 12.281245 12.792964 25.585927 25.585928z" fill="#FFFFFF" p-id="27032"></path><path d="M511.718555 409.374844q5.014842 0 10.029683-0.511719t9.92734-1.432812q4.96367-1.023437 9.722653-2.456249 4.861326-1.483984 9.466793-3.377342 4.656639-1.944531 9.108591-4.298436 4.40078-2.353905 8.596871-5.168357 4.196092-2.814452 8.085153-5.987108 3.889061-3.172655 7.419919-6.754684 3.58203-3.58203 6.754685-7.41992 3.172655-3.889061 5.987107-8.085153 2.814452-4.196092 5.117186-8.596871 2.405077-4.451951 4.349608-9.108591 1.893359-4.605467 3.377342-9.415621 1.432812-4.810154 2.456249-9.722653 0.972265-4.96367 1.432812-9.978511Q614.062266 312.045975 614.062266 307.031133t-0.511719-10.029684q-0.511719-5.014842-1.432812-9.92734-1.023437-4.96367-2.456249-9.722652-1.483984-4.861326-3.377342-9.466794-1.944531-4.656639-4.298436-9.10859-2.353905-4.40078-5.168358-8.596872-2.814452-4.196092-5.987107-8.085153-3.172655-3.889061-6.754685-7.419919-3.58203-3.58203-7.419919-6.754685-3.889061-3.172655-8.085153-5.987107-4.196092-2.814452-8.596871-5.117185-4.451951-2.405077-9.108591-4.349608-4.605467-1.893359-9.415621-3.377343-4.810154-1.432812-9.722653-2.456249-4.96367-0.972265-9.978512-1.432812Q516.733397 204.687422 511.718555 204.687422t-10.029684 0.511718q-5.014842 0.511719-9.92734 1.432812-4.96367 1.023437-9.722652 2.456249-4.861326 1.483984-9.466794 3.377343-4.656639 1.944531-9.10859 4.298436-4.40078 2.353905-8.596872 5.168357-4.196092 2.814452-8.085153 5.987107-3.889061 3.172655-7.419919 6.754685-3.58203 3.58203-6.754685 7.419919-3.172655 3.889061-5.987107 8.085153-2.814452 4.196092-5.117185 8.596872-2.405077 4.451951-4.349608 9.10859-1.893359 4.605467-3.377343 9.415622-1.432812 4.810154-2.456249 9.722652-0.972265 4.96367-1.432812 9.978512Q409.374844 302.016291 409.374844 307.031133t0.511718 10.029684q0.511719 5.014842 1.432812 9.92734 1.023437 4.96367 2.456249 9.722652 1.483984 4.861326 3.377343 9.466793 1.944531 4.656639 4.298436 9.108591 2.353905 4.40078 5.168357 8.596871 2.814452 4.196092 5.987107 8.085153 3.172655 3.889061 6.754685 7.41992 3.58203 3.58203 7.419919 6.754684 3.889061 3.172655 8.085153 5.987108 4.196092 2.814452 8.596872 5.117185 4.451951 2.405077 9.10859 4.349608 4.605467 1.893359 9.415622 3.377342 4.810154 1.432812 9.722652 2.456249 4.96367 0.972265 9.978512 1.432812Q506.703713 409.374844 511.718555 409.374844z m-40.937485-77.78122v-49.124982a16.374994 16.374994 0 0 1 16.374994-16.374994h49.124981a16.374994 16.374994 0 0 1 16.374994 16.374994v49.124982a16.374994 16.374994 0 0 1-16.374994 16.374993h-49.124981a16.374994 16.374994 0 0 1-16.374994-16.374993z" fill="#FFFFFF" p-id="27033"></path></svg>',
  [NodeType.DATA_ADD]:
    '<svg t="1711094545305" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="17972" width="200" height="200"><path d="M512 512m-512 0a512 512 0 1 0 1024 0 512 512 0 1 0-1024 0Z" fill="#03BFC0" p-id="17973"></path><path d="M512 236.307692c14.572308 0 26.545231 11.933538 26.545231 26.545231v222.601846h222.601846l3.938461 0.315077c12.760615 1.890462 22.606769 12.957538 22.60677 26.230154 0 14.572308-11.933538 26.545231-26.545231 26.545231h-222.641231v222.601846c0 13.272615-9.846154 24.339692-22.606769 26.269538L512 787.692308a26.584615 26.584615 0 0 1-26.545231-26.545231v-222.641231H262.852923a26.624 26.624 0 0 1-26.269538-22.606769L236.307692 512c0-14.572308 11.933538-26.545231 26.545231-26.545231h222.601846V262.852923c0-13.272615 9.846154-24.339692 22.646154-26.269538L512 236.307692z" fill="#FFFFFF" p-id="17974"></path></svg>',
  [NodeType.DATA_UPDATE]:
    '<svg t="1711094568722" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="18288" width="200" height="200"><path d="M512 512m-512 0a512 512 0 1 0 1024 0 512 512 0 1 0-1024 0Z" fill="#03BFC0" p-id="18289"></path><path d="M544.807385 258.638769a23.630769 23.630769 0 1 1 0 47.261539H334.769231a28.868923 28.868923 0 0 0-28.868923 28.868923v354.461538c0 15.950769 12.918154 28.868923 28.868923 28.868923h354.461538a28.868923 28.868923 0 0 0 28.868923-28.868923V472.615385a23.630769 23.630769 0 0 1 47.261539 0v216.615384a76.130462 76.130462 0 0 1-76.130462 76.130462h-354.461538A76.130462 76.130462 0 0 1 258.638769 689.230769v-354.461538c0-42.062769 34.067692-76.130462 76.130462-76.130462h210.038154z m203.18523 0.512a23.630769 23.630769 0 0 1 10.476308 39.778462l-278.488615 278.528a23.630769 23.630769 0 1 1-33.437539-33.437539l277.346462-276.086154 1.102769-2.363076a23.630769 23.630769 0 0 1 23.000615-6.419693z" fill="#FFFFFF" p-id="18290"></path></svg>',
  [NodeType.DATA_GET]:
    '<svg t="1711094582698" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="18446" width="200" height="200"><path d="M512 512m-512 0a512 512 0 1 0 1024 0 512 512 0 1 0-1024 0Z" fill="#03BFC0" p-id="18447"></path><path d="M487.542154 236.307692a251.234462 251.234462 0 0 1 182.272 424.132923l87.827692 87.788308a22.843077 22.843077 0 1 1-32.295384 32.295385l-89.875693-89.915077A251.234462 251.234462 0 1 1 487.581538 236.307692z m0 45.686154a205.587692 205.587692 0 1 0 0 411.096616 205.587692 205.587692 0 0 0 0-411.096616z" fill="#FFFFFF" p-id="18448"></path></svg>',
  [NodeType.DATA_GET_MULTI]:
    '<svg t="1711094593542" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="18604" width="200" height="200"><path d="M512 512m-512 0a512 512 0 1 0 1024 0 512 512 0 1 0-1024 0Z" fill="#03BFC0" p-id="18605"></path><path d="M737.28 605.459692a190.542769 190.542769 0 0 0 59.667692-138.318769c0-105.550769-86.134154-191.448615-191.92123-191.448615-105.944615 0-191.960615 85.897846-191.960616 191.448615 0 105.550769 86.173538 191.448615 191.960616 191.448615a191.803077 191.803077 0 0 0 93.853538-24.576l73.846154 92.553847a23.945846 23.945846 0 0 0 38.084923-29.026462l-73.491692-92.081231z m-279.748923-138.318769a147.377231 147.377231 0 0 1 147.495385-146.983385 147.377231 147.377231 0 0 1 147.456 146.983385 147.377231 147.377231 0 0 1-147.456 146.983385 147.377231 147.377231 0 0 1-147.495385-146.983385zM364.622769 370.884923h-143.753846A23.945846 23.945846 0 0 1 196.923077 346.978462c0-13.115077 10.712615-23.867077 23.985231-23.867077h143.753846c13.233231 0 23.945846 10.752 23.945846 23.867077a23.945846 23.945846 0 0 1-23.985231 23.867076z m-5.750154 236.268308H220.908308A23.945846 23.945846 0 0 1 196.923077 583.286154c0-13.115077 10.712615-23.827692 23.985231-23.827692h137.964307c13.233231 0 23.945846 10.712615 23.945847 23.827692a23.945846 23.945846 0 0 1-23.945847 23.867077z m-28.356923-118.429539H220.947692a23.945846 23.945846 0 1 1 0-47.970461h109.64677a23.945846 23.945846 0 1 1 0 47.970461z m154.387693 241.073231H220.908308a23.945846 23.945846 0 1 1 0-47.931077h263.995077a23.945846 23.945846 0 1 1 0 47.931077z" fill="#FFFFFF" p-id="18606"></path></svg>',
  [NodeType.DATA_DELETE]:
    '<svg t="1711094606198" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="18762" width="200" height="200"><path d="M512 512m-512 0a512 512 0 1 0 1024 0 512 512 0 1 0-1024 0Z" fill="#03BFC0" p-id="18763"></path><path d="M601.403077 236.307692a48.049231 48.049231 0 0 1 47.891692 47.931077v31.035077l115.436308 0.039385c12.209231 0 21.031385 5.316923 22.685538 15.36l0.275693 3.465846a22.843077 22.843077 0 0 1-22.961231 22.843077h-52.499692v338.235077c0 17.329231-6.104615 33.949538-17.132308 47.104l-4.332308 4.726154c-13.784615 13.784615-32.413538 21.504-51.869538 21.504H385.063385a73.334154 73.334154 0 0 1-73.334154-73.334154V357.415385H259.190154a22.843077 22.843077 0 0 1-13.508923-4.332308l-2.756923-2.363077A22.843077 22.843077 0 0 1 236.307692 334.493538c0-12.248615 9.491692-19.180308 22.882462-19.180307h115.830154l0.039384-31.113846a48.049231 48.049231 0 0 1 43.047385-47.655385L422.990769 236.307692h178.412308z m69.435077 121.462154H354.107077V697.107692a27.569231 27.569231 0 0 0 23.748923 27.293539l3.741538 0.236307h261.750154a27.569231 27.569231 0 0 0 27.529846-27.529846V357.769846z m-157.696 84.637539c12.288 0 22.409846 8.822154 22.409846 20.046769v167.305846c0 11.264-10.121846 20.086154-22.409846 20.086154-12.248615 0-22.370462-8.822154-22.370462-20.007385v-167.384615c0-11.224615 10.121846-20.046769 22.370462-20.046769z m-88.497231 2.48123c12.288 0 22.409846 8.861538 22.409846 20.086154v164.785231c0 11.264-10.121846 20.086154-22.409846 20.086154-11.815385 0-21.858462-8.743385-22.370461-20.086154v-164.785231c0-11.224615 10.082462-20.086154 22.370461-20.086154z m176.994462 0.157539c12.288 0 22.409846 8.822154 22.409846 20.086154v164.627692c0 11.264-10.121846 20.086154-22.409846 20.086154-12.248615 0-22.370462-8.822154-22.370462-20.086154v-164.627692c0-11.264 10.121846-20.086154 22.370462-20.086154z m3.032615-167.463385h-184.556308a2.363077 2.363077 0 0 0-2.48123 2.520616l-0.07877 34.697846h189.558154l0.039385-34.697846a2.441846 2.441846 0 0 0-1.181539-2.205539l-1.299692-0.315077z" fill="#FFFFFF" p-id="18764"></path></svg>',
  [NodeType.DATA_GROUPING]:
    '<svg t="1717744262121" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="19933" width="200" height="200"><path d="M512 512m-512 0a512 512 0 1 0 1024 0 512 512 0 1 0-1024 0Z" fill="#03BFC0" p-id="19934"></path><path d="M400.147692 236.307692c15.36 0 29.380923 8.428308 36.627693 21.897846l28.356923 53.011693 281.127384 0.039384c21.307077 0 38.951385 16.187077 41.196308 36.94277l0.236308 4.52923v356.036923c0 22.843077-18.589538 41.432615-41.432616 41.432616H277.740308c-22.843077 0-41.432615-18.589538-41.432616-41.432616V277.740308C236.307692 254.897231 254.897231 236.307692 277.740308 236.307692h122.446769z m-117.720615 215.158154l-0.708923 201.097846v52.224h460.563692V452.135385L282.387692 451.465846z m115.357538-169.747692H281.678769v124.337231h460.563693V356.667077H440.32l-3.544615-2.087385-38.99077-72.861538z" fill="#FFFFFF" p-id="19935"></path></svg>',
};

// 节点 icon code
export const nodeIconCode = {
  [NodeType.START_FLOW]: 'iconkaishi-graph',
  [NodeType.START_EVENT]: 'iconshijianchufajiedian-graph',
  [NodeType.START_TIMER]: 'icondingshichufa-graph',
  [NodeType.START_MANUAL]: 'iconrengongchufa-graph',
  [NodeType.END_FLOW]: 'iconjieshu-graph',
  [NodeType.MANUAL_APPROVE]: 'iconqianhe-graph',
  [NodeType.MANUAL_EXECUTION]: 'iconrengong-graph',
  [NodeType.MANUAL_NOTIFICATION]: 'iconzhihui-graph',
  [NodeType.AUTO_HTTP]: 'iconHTTP-graph',
  [NodeType.AUTO_ESP]: 'iconESP-graph',
  [NodeType.AUTO_CALL_EVENT]: 'icontiaoyongshijianjiedian-graph',
  [NodeType.AUTO_NOTIFY]: 'iconxiaoxitongzhi-graph',
  [NodeType.AUTO_SCRIPT]: 'iconjiaoben-graph',
  [NodeType.MSG_SEND]: 'iconxiaoxitongzhi-graph',
  [NodeType.AUTO_SUB_PROCESS]: 'iconziliucheng-graph',
  [NodeType.AUTO_SUB_PROJECT]: 'iconzixiangmu',
  [NodeType.AUTO_WAIT]: 'icondengdai1',
  [NodeType.AUTO_EVOKE_WAIT]: 'iconhuanqidengdai',
  [NodeType.CONDITION_BRANCH]: 'icontiaojianfenzhi-graph',
  [NodeType.PARALLEL_BRANCH]: 'iconbinghangfenzhi-graph',
  [NodeType.CONDITION_BRANCH_START]: 'iconshulidejiahao-smallest',
  [NodeType.CONDITION_BRANCH_END]: 'iconfenzhijieshu',
  [NodeType.DATA_ADD]: 'iconxinzengshuju',
  [NodeType.DATA_UPDATE]: 'icongengxinshuju',
  [NodeType.DATA_GET]: 'iconhuoqudantiaoshuju',
  [NodeType.DATA_GET_MULTI]: 'iconhuoquduotiaoshuju',
  [NodeType.DATA_DELETE]: 'iconshanchushuju',
  [NodeType.DATA_GROUPING]: 'iconshujufenzu',
  [NodeType.PARALLEL_BRANCH_START]: 'iconshulidejiahao-smallest',
  [NodeType.PARALLEL_BRANCH_END]: 'iconfenzhijieshu',
};

// 工具菜单配置
export const nodesAgs = [
  {
    title: 'dj-基础节点',
    group: 'group1',
    nodes: [
      {
        nodeType: NodeType.START_FLOW,
        nodeName: NodeTypeName[NodeType.START_FLOW],
        iconCode: nodeIconCode[NodeType.START_FLOW],
        isOpen: true,
      },
      {
        nodeType: NodeType.END_FLOW,
        nodeName: NodeTypeName[NodeType.END_FLOW],
        iconCode: nodeIconCode[NodeType.END_FLOW],
        isOpen: true,
      },
    ],
  },
  {
    title: 'dj-任务',
    group: 'group2',
    nodes: [
      {
        nodeType: NodeType.MANUAL_APPROVE,
        nodeName: NodeTypeName[NodeType.MANUAL_APPROVE],
        iconCode: nodeIconCode[NodeType.MANUAL_APPROVE],
        isOpen: true,
      },
      {
        nodeType: NodeType.MANUAL_EXECUTION,
        nodeName: NodeTypeName[NodeType.MANUAL_EXECUTION],
        iconCode: nodeIconCode[NodeType.MANUAL_EXECUTION],
        isOpen: true,
      },
      {
        nodeType: NodeType.MANUAL_NOTIFICATION,
        nodeName: NodeTypeName[NodeType.MANUAL_NOTIFICATION],
        iconCode: nodeIconCode[NodeType.MANUAL_NOTIFICATION],
        isOpen: false,
      },
    ],
  },
  {
    title: 'dj-数据节点',
    group: 'group3',
    nodes: [
      {
        nodeType: NodeType.DATA_ADD,
        nodeName: NodeTypeName[NodeType.DATA_ADD],
        iconCode: nodeIconCode[NodeType.DATA_ADD],
        isOpen: true,
      },
      {
        nodeType: NodeType.DATA_UPDATE,
        nodeName: NodeTypeName[NodeType.DATA_UPDATE],
        iconCode: nodeIconCode[NodeType.DATA_UPDATE],
        isOpen: true,
      },
      {
        nodeType: NodeType.DATA_GET,
        nodeName: NodeTypeName[NodeType.DATA_GET],
        iconCode: nodeIconCode[NodeType.DATA_GET],
        isOpen: true,
      },
      {
        nodeType: NodeType.DATA_GET_MULTI,
        nodeName: NodeTypeName[NodeType.DATA_GET_MULTI],
        iconCode: nodeIconCode[NodeType.DATA_GET_MULTI],
        isOpen: true,
      },
      {
        nodeType: NodeType.DATA_DELETE,
        nodeName: NodeTypeName[NodeType.DATA_DELETE],
        iconCode: nodeIconCode[NodeType.DATA_DELETE],
        isOpen: true,
      },
      // 暂时放开数据分组
      {
        nodeType: NodeType.DATA_GROUPING,
        nodeName: NodeTypeName[NodeType.DATA_GROUPING],
        iconCode: nodeIconCode[NodeType.DATA_GROUPING],
        isOpen: true,
      },
    ],
  },
  {
    title: 'dj-自动节点',
    group: 'group4',
    nodes: [
      {
        nodeType: NodeType.AUTO_HTTP,
        nodeName: NodeTypeName[NodeType.AUTO_HTTP],
        iconCode: nodeIconCode[NodeType.AUTO_HTTP],
        isOpen: true,
      },
      {
        nodeType: NodeType.AUTO_ESP,
        nodeName: NodeTypeName[NodeType.AUTO_ESP],
        iconCode: nodeIconCode[NodeType.AUTO_ESP],
        isOpen: true,
      },
      {
        nodeType: NodeType.AUTO_CALL_EVENT,
        nodeName: NodeTypeName[NodeType.AUTO_CALL_EVENT],
        iconCode: nodeIconCode[NodeType.AUTO_CALL_EVENT],
        isOpen: true,
      },
      {
        nodeType: NodeType.AUTO_NOTIFY,
        nodeName: NodeTypeName[NodeType.AUTO_NOTIFY],
        iconCode: nodeIconCode[NodeType.AUTO_NOTIFY],
        isOpen: true,
      },
      {
        nodeType: NodeType.AUTO_SCRIPT,
        nodeName: NodeTypeName[NodeType.AUTO_SCRIPT],
        iconCode: nodeIconCode[NodeType.AUTO_SCRIPT],
        isOpen: true,
      },
      {
        nodeType: NodeType.AUTO_SUB_PROCESS,
        nodeName: NodeTypeName[NodeType.AUTO_SUB_PROCESS],
        iconCode: nodeIconCode[NodeType.AUTO_SUB_PROCESS],
        isOpen: false,
      },
      {
        nodeType: NodeType.AUTO_SUB_PROJECT,
        nodeName: NodeTypeName[NodeType.AUTO_SUB_PROJECT],
        iconCode: nodeIconCode[NodeType.AUTO_SUB_PROJECT],
        isOpen: true,
      },
      {
        nodeType: NodeType.AUTO_WAIT,
        nodeName: NodeTypeName[NodeType.AUTO_WAIT],
        iconCode: nodeIconCode[NodeType.AUTO_WAIT],
        isOpen: true,
      },
      {
        nodeType: NodeType.AUTO_EVOKE_WAIT,
        nodeName: NodeTypeName[NodeType.AUTO_EVOKE_WAIT],
        iconCode: nodeIconCode[NodeType.AUTO_EVOKE_WAIT],
        isOpen: true,
      },
      {
        nodeType: NodeType.MSG_SEND,
        nodeName: NodeTypeName[NodeType.MSG_SEND],
        iconCode: nodeIconCode[NodeType.MSG_SEND],
        tag: 'new',
        isOpen: true,
      },
    ],
  },
  {
    title: 'dj-分支节点',
    group: 'group5',
    nodes: [
      {
        nodeType: NodeType.CONDITION_BRANCH,
        nodeName: NodeTypeName[NodeType.CONDITION_BRANCH],
        iconCode: nodeIconCode[NodeType.CONDITION_BRANCH],
        isOpen: true,
      },
      {
        nodeType: NodeType.PARALLEL_BRANCH,
        nodeName: NodeTypeName[NodeType.PARALLEL_BRANCH],
        iconCode: nodeIconCode[NodeType.PARALLEL_BRANCH],
        isOpen: true,
      },
    ],
  },
];

// 连接桩样式
const portStyle = {
  circle: {
    r: 4,
    magnet: true,
    stroke: '#6A4CFF',
    strokeWidth: 1,
    fill: '#fff',
    style: {
      visibility: 'hidden',
    },
  },
};

// 连接桩配置
const ports = {
  groups: {
    top: {
      position: 'top',
      attrs: {
        ...portStyle,
      },
    },
    right: {
      position: 'right',
      attrs: {
        ...portStyle,
      },
    },
    bottom: {
      position: 'bottom',
      attrs: {
        ...portStyle,
      },
    },
    left: {
      position: 'left',
      attrs: {
        ...portStyle,
      },
    },
  },
  items: [
    {
      id: createUUID(),
      group: 'top',
    },
    {
      id: createUUID(),
      group: 'right',
    },
    {
      id: createUUID(),
      group: 'bottom',
    },
    {
      id: createUUID(),
      group: 'left',
    },
  ],
};

// 工具节点定制设置
const stencilNodeAttrs = (nodeType) => {
  let size: object = {
    width: 124,
    height: 32,
  };
  let body: object = {};
  let label: object = {};
  let level: object = {};
  let conditionBody: object = {};
  let condition: object = {};
  let icon: object = {};
  let tag: object = {};

  switch (nodeType) {
    case NodeType.START_FLOW:
    case NodeType.START_EVENT:
    case NodeType.START_TIMER:
    case NodeType.START_MANUAL:
    case NodeType.END_FLOW:
      body = {
        rx: 19,
        ry: 19,
      };
      break;
    // 未开放
    case NodeType.MANUAL_NOTIFICATION:
    case NodeType.AUTO_SUB_PROCESS:
    case NodeType.PARALLEL_BRANCH:
      body = {
        fill: '#F9F9F9',
        cursor: 'not-allowed',
      };
      label = {
        cursor: 'not-allowed',
      };
      icon = {
        cursor: 'not-allowed',
      };
      tag = {
        display: 'block',
        cursor: 'not-allowed',
      };
      break;
    default:
    // 默认
  }
  return { size, body, label, level, conditionBody, condition, icon, tag };
};

// 画布节点定制设置
const graphNodeAttrs = (nodeType) => {
  let size: object = commonNodeSize;
  let body: object = {};
  let label: object = {};
  let level: object = {};
  let conditionBody: object = {};
  let condition: object = {};
  let icon: object = {};
  let tag: object = {};

  switch (nodeType) {
    case NodeType.START_FLOW:
    case NodeType.START_EVENT:
    case NodeType.START_TIMER:
    case NodeType.START_MANUAL:
    case NodeType.END_FLOW:
      body = {
        rx: 19,
        ry: 19,
        strokeWidth: 0,
        filter: {
          name: 'dropShadow',
          args: {
            color: '#4943a3',
            dx: 0,
            dy: 0,
            blur: 4,
            opacity: 0.4,
          },
        },
      };
      label = {
        textWrap: {
          width: 104,
          ellipsis: true,
        },
      };
      icon = {
        width: 26,
        height: 26,
      };
      break;
    // case NodeType.CONDITION_BRANCH_START:
    // case NodeType.CONDITION_BRANCH_END:
    //   size = conditionStartNodeSize ?? conditionEndNodeSize;
    //   body = {
    //     rx: 16,
    //     ry: 16,
    //     strokeWidth: 0,
    //     filter: {
    //       name: 'dropShadow',
    //       args: {
    //         color: '#4943a3',
    //         dx: 0,
    //         dy: 0,
    //         blur: 4,
    //         opacity: 0.4,
    //       },
    //     },
    //   };
    //   label = {
    //     textWrap: {
    //       width: 60,
    //       ellipsis: true,
    //     },
    //     x: -20,
    //   };
    //   icon = {
    //     width: 14,
    //     height: 14,
    //     y: 8,
    //   };
    //   break;
    case NodeType.CONDITION_BRANCH:
      size = conditionNodeSize;
      body = {
        strokeWidth: 0,
        filter: {
          name: 'dropShadow',
          args: {
            color: '#4943a3',
            dx: 0,
            dy: 0,
            blur: 4,
            opacity: 0.4,
          },
        },
      };
      label = {
        textWrap: {
          width: 52,
          height: 20,
          ellipsis: true,
        },
        x: -34,
        y: -19,
      };
      icon = {
        width: 26,
        height: 26,
      };
      level = {
        display: 'block',
      };
      conditionBody = {
        display: 'block',
      };
      condition = {
        display: 'block',
      };
      break;
    // case NodeType.PARALLEL_BRANCH_START:
    // case NodeType.PARALLEL_BRANCH_END:
    //   size = parallelMarkNodeSize;
    //   body = {
    //     rx: 16,
    //     ry: 16,
    //     strokeWidth: 0,
    //     filter: {
    //       name: 'dropShadow',
    //       args: {
    //         color: '#4943a3',
    //         dx: 0,
    //         dy: 0,
    //         blur: 4,
    //         opacity: 0.4,
    //       },
    //     },
    //   };
    //   label = {
    //     textWrap: {
    //       width: 60,
    //       ellipsis: true,
    //     },
    //     x: -20,
    //   };
    //   icon = {
    //     width: 14,
    //     height: 14,
    //     y: 8,
    //   };
    //   break;
    case NodeType.PARALLEL_BRANCH:
      size = parallelInstanceNodeSize;
      body = {
        strokeWidth: 0,
        filter: {
          name: 'dropShadow',
          args: {
            color: '#4943a3',
            dx: 0,
            dy: 0,
            blur: 4,
            opacity: 0.4,
          },
        },
      };
      label = {
        textWrap: {
          width: 94,
          height: 20,
          ellipsis: true,
        },
        fontSize: 13,
        textAnchor: 'start',
        x: -34,
        y: -19,
      };
      icon = {
        width: 26,
        height: 26,
      };
      // level = {
      //   display: 'block',
      // };
      conditionBody = {
        display: 'block',
      };
      condition = {
        display: 'block',
      };
      break;
    default:
      body = {
        strokeWidth: 0,
        filter: {
          name: 'dropShadow',
          args: {
            color: '#4943a3',
            dx: 0,
            dy: 0,
            blur: 4,
            opacity: 0.4,
          },
        },
      };
      label = {
        textWrap: {
          width: 104,
          ellipsis: true,
        },
      };
      icon = {
        width: 26,
        height: 26,
      };
  }
  return { size, body, label, level, conditionBody, condition, icon, tag };
};

// 节点通用默认配置
export const commonNode = (data) => {
  const { toolType, nodeName, nodeType } = data || {};
  // 定制化配置
  let nodeAttrs: any;
  if (toolType === 'stencil') {
    nodeAttrs = stencilNodeAttrs(nodeType);
  }
  if (toolType === 'graph') {
    nodeAttrs = graphNodeAttrs(nodeType);
  }
  const { size, body, label, level, conditionBody, condition, icon, tag } = nodeAttrs || {};
  const markup = [
    {
      tagName: 'rect',
      selector: 'body',
    },
    {
      tagName: 'text',
      selector: 'label',
    },
    {
      tagName: 'text',
      selector: 'level',
    },
    {
      tagName: 'rect',
      selector: 'conditionBody',
    },
    {
      tagName: 'text',
      selector: 'condition',
    },
    {
      tagName: 'image',
      selector: 'icon',
    },
    {
      tagName: 'image',
      selector: 'tag',
    },
    NodeType.MSG_SEND === nodeType && {
      tagName: 'image',
      selector: 'newTag',
    },
  ].filter(Boolean);
  const attrs: any = {
    body: {
      strokeWidth: 1,
      stroke: '#E9E9E9',
      fill: '#FFFFFF',
      rx: 6,
      ry: 6,
      cursor: 'move',
      ...(body || {}),
    },
    label: {
      text: nodeName,
      fontSize: 13,
      fill: '#333',
      textWrap: {
        width: 74,
        ellipsis: true,
      },
      x: -34,
      textAnchor: 'start',
      cursor: 'move',
      ...(label || {}),
    },
    level: {
      text: '优先级',
      fontSize: 11,
      fill: '#FD5609',
      textWrap: {
        width: 42,
        ellipsis: true,
      },
      x: 34,
      y: -19,
      textAnchor: 'start',
      cursor: 'move',
      display: 'none',
      ...(level || {}),
    },
    conditionBody: {
      strokeWidth: 1,
      stroke: '#F7F8FE',
      fill: '#F7F8FE',
      width: 140,
      height: 32,
      rx: 4,
      ry: 4,
      x: 10,
      y: 38,
      cursor: 'move',
      display: 'none',
      ...(conditionBody || {}),
    },
    condition: {
      text: '',
      fontSize: 12,
      fill: '#999999',
      textWrap: {
        width: 120,
        ellipsis: true,
      },
      x: -60,
      y: 17,
      textAnchor: 'start',
      cursor: 'move',
      display: 'none',
      ...(condition || {}),
    },
    icon: {
      'xlink:href': imgUrl(nodeIconSvg[nodeType]),
      width: 20,
      height: 20,
      x: 10,
      y: 6,
      cursor: 'move',
      ...icon,
    },
    tag: {
      display: 'none',
      'xlink:href': imgUrl(stayTunedIcon),
      width: 35,
      height: 35,
      x: 89,
      y: -5,
      cursor: 'move',
      ...tag,
    },
  };
  if (NodeType.MSG_SEND === nodeType) {
    attrs.newTag = {
      'xlink:href': imgUrl(newTag),
      width: 35,
      height: 30,
      x: 128,
      y: -7,
      borderRadius: 4,
      cursor: 'move',
    };
  }
  return {
    shape: 'rect',
    id: createUUID(),
    width: size.width,
    height: size.height,
    markup: markup,
    attrs: attrs,
    ports: { ...ports },
  };
};

// #region 初始化 stencil
export const stencil = (
  { graph, basicTitle, manualTitle, dataTitle, automaticTitle, branchTitle },
  subject,
  dndContainer,
) =>
  new Stencil({
    title: '',
    target: graph,
    stencilGraphWidth: 300,
    stencilGraphHeight: 0,
    groups: [
      {
        title: basicTitle,
        name: 'group1',
        collapsable: false,
        layoutOptions: {
          // 每行显示2个图标
          columns: 2,
          columnWidth: 145,
          rowHeight: 40,
          dx: 0,
        },
      },
      {
        title: manualTitle,
        name: 'group2',
        collapsable: false,
        layoutOptions: {
          // 每行显示2个图标
          columns: 2,
          columnWidth: 145,
          rowHeight: 40,
          dx: 0,
        },
      },
      {
        title: dataTitle,
        name: 'group3',
        collapsable: false,
        layoutOptions: {
          // 每行显示2个图标
          columns: 2,
          columnWidth: 145,
          rowHeight: 40,
          dx: 0,
        },
      },
      {
        title: automaticTitle,
        name: 'group4',
        collapsable: false,
        layoutOptions: {
          // 每行显示2个图标
          columns: 2,
          columnWidth: 145,
          rowHeight: 40,
          dx: 0,
        },
      },
      {
        title: branchTitle,
        name: 'group5',
        collapsable: false,
        layoutOptions: {
          // 每行显示2个图标
          columns: 2,
          columnWidth: 145,
          rowHeight: 40,
          dx: 0,
        },
      },
    ],
    dndContainer,
    validateNode(node) {
      const { isOpen } = node?.data || {};
      return !!isOpen;
    },
    getDragNode(node) {
      let { nodeName, nodeType } = node?.data || {};
      const commonNodeParams = {
        toolType: 'graph',
        nodeName,
        nodeType,
      };
      const graphNode = graph.createNode(commonNode(commonNodeParams));
      graphNode.setData({
        ...(node?.data || {}),
        nodeType: nodeType,
        nodeId: node.id,
        nodeName: nodeName,
      });
      return graphNode;
    },
    getDropNode(node) {
      const clonedNode = node.clone();
      subject.next(clonedNode);
      return clonedNode;
    },
  });
