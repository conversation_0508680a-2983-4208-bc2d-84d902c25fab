import { Injectable } from '@angular/core';
import { Node, Edge } from '@antv/x6';
import { Snapline } from '@antv/x6-plugin-snapline';
import { Keyboard } from '@antv/x6-plugin-keyboard';
import { Selection } from '@antv/x6-plugin-selection';
import { Scroller } from '@antv/x6-plugin-scroller';
import { History } from '@antv/x6-plugin-history';
import { NzMessageService } from 'ng-zorro-antd/message';
import { TranslateService } from '@ngx-translate/core';
import { ViewStoreService } from './store.service';
import { commonEdgeAttrs } from '../config/graph';
import { commonNode } from '../config/stencil';
import {
  ICreateNodeParams,
  IAddEdgeBetweenTwoNodes,
  NodeType,
  PannelTabsType,
  ModelNodes,
  VariableType,
  SDNodeType,
} from '../config/typings';
import { cloneDeep } from 'lodash';
import { isJSON } from 'common/utils/core.utils';
import { VariableService } from './variable.service';
import { nodeCanAddToOptions } from 'pages/app-model-driven/utils/operation';
import { ViewGraphService } from './graph.service';

@Injectable()
export class ViewToolsService {
  constructor(
    private athMessageService: NzMessageService,
    private translateService: TranslateService,
    private viewStoreService: ViewStoreService,
    private variableService: VariableService,
  ) {}

  // 画布插件配置
  toolsGraphPlugin(graph) {
    graph
      .use(new Snapline({ enabled: true })) // 对齐线
      .use(new Keyboard({ enabled: true })) // 快捷键
      .use(
        new Selection({
          // 框选
          enabled: true,
          multiple: true,
          rubberband: true,
          rubberEdge: true,
          movable: true,
          showNodeSelectionBox: true,
          modifiers: 'shift',
        }),
      )
      .use(new History({ enabled: true })); // 撤销重做
    // .use(
    //   new Scroller({
    //     // 滚动
    //     enabled: true,
    //     pageVisible: false,
    //     pageBreak: true,
    //     autoResize: false,
    //     pannable: true,
    //     autoResizeOptions: {
    //       border: 50,
    //     },
    //   }),
    // );
  }

  // 初始化工具栏
  toolsInit(graph) {
    this.toolsOriginalSize(graph); // 100% 视图
    graph.cleanHistory(); // 清空历史队列
  }

  // 100% 视图
  toolsOriginalSize(graph) {
    graph.zoom(1, {
      absolute: true,
    });
  }

  // 放大
  toolsEnlarge(graph) {
    graph.zoom(0.2, {
      maxScale: 2,
    });
  }

  // 缩小
  toolsReduce(graph) {
    graph.zoom(-0.2, {
      minScale: 0.5,
    });
  }

  // 撤销
  toolsUndo(graph) {
    graph.undo();
  }

  // 重做
  toolsRedo(graph) {
    graph.redo();
  }

  /**
   * 下面用于处理画布的逻辑
   * ==================================================
   */

  // 根据数据渲染画布
  toolsGraphFromJSON(graph, cellList: (Node.Metadata | Edge.Metadata)[]) {
    graph.fromJSON(cellList);
    graph.centerContent();
  }

  // 获取画布中心坐标
  toolsGraphCenter(graph, graphWrapper) {
    const { origin } = graph.getGraphArea();
    const offset = graph.coord.getClientOffset();
    const viewX = graphWrapper.clientWidth / 2;
    const viewY = graphWrapper.clientHeight / 2;
    const scale = graph.scale();
    const x = origin.x - offset.x / scale.sx + viewX / scale.sx;
    const y = origin.y - offset.y / scale.sy + viewY / scale.sy;

    return { x, y };
  }

  // 获取节点连接桩 group 集合（入线）
  toolsNodeIncomingPortGroups(graph, node) {
    const _groups = [];
    const incomingEdges = graph.getIncomingEdges(node);
    incomingEdges?.forEach((edge) => {
      const targetPortId = edge.getTargetPortId();
      const targetPortMetaData = node.getPort(targetPortId);
      _groups.push(targetPortMetaData.group);
    });
    const groups = [...new Set(_groups)];

    return groups;
  }

  // 获取节点连接桩 group 集合（入线和出线）
  toolsNodePortGroups(graph, node) {
    const _groups = [];
    const outEdges = graph.getOutgoingEdges(node);
    outEdges?.forEach((edge) => {
      const sourcePortId = edge.getSourcePortId();
      const sourcePortMetaData = node.getPort(sourcePortId);
      _groups.push(sourcePortMetaData.group);
    });
    const incomingEdges = graph.getIncomingEdges(node);
    incomingEdges?.forEach((edge) => {
      const targetPortId = edge.getTargetPortId();
      const targetPortMetaData = node.getPort(targetPortId);
      _groups.push(targetPortMetaData.group);
    });
    const groups = [...new Set(_groups)];

    return groups;
  }

  // 通过节点获取节点id某个连接桩id和节点id（边创建格式： { cell: string, port: string }）
  toolsNodeAndPortIdByNodeId(graph, group, nodeId) {
    const node = graph.getCellById(nodeId);
    const port = node.getPortsByGroup(group)[0];

    return {
      cell: node.id,
      port: port.id,
    };
  }

  // 获取单向节点的来源节点连接桩和目标节点连接桩（只有一个入边和一个出边）
  toolsOneWayNodePort(graph, node) {
    const incomingEdges = graph.getIncomingEdges(node);
    const outgoingEdges = graph.getOutgoingEdges(node);
    const sourcePort: any = incomingEdges?.[0]?.source;
    const targetPort: any = outgoingEdges?.[0]?.target;

    return { sourcePort, targetPort };
  }

  // 通过边获取来源和目标连接桩元信息
  toolsPortMetaDataByEdge(edge) {
    const sourcePortId = edge.getSourcePortId();
    const targetPortId = edge.getTargetPortId();
    const sourceNode = edge.getSourceNode();
    const targetNode = edge.getTargetNode();
    const sourcePortMetaData = sourceNode.getPort(sourcePortId);
    const targetPortMetaData = targetNode.getPort(targetPortId);

    return { sourcePortMetaData, targetPortMetaData };
  }

  // 通过边 ID 获取来源和目标节点坐标
  toolsNodePositionByEdgeId(graph, edgeId) {
    const edge: any = graph.getCellById(edgeId);
    const sourceNode: any = edge.getSourceCell();
    const targetNode: any = edge.getTargetCell();
    const sourcePosition = sourceNode.position();
    const targetPosition = targetNode.position();

    return { sourcePosition, targetPosition };
  }

  // 通过边 ID 获取来源和目标节点大小
  toolsNodeSizeByEdgeId(graph, edgeId) {
    const edge: any = graph.getCellById(edgeId);
    const sourceNode: any = edge.getSourceCell();
    const targetNode: any = edge.getTargetCell();
    const sourceSize = sourceNode.size();
    const targetSize = targetNode.size();
    return { sourceSize, targetSize };
  }

  // 通过边 ID 获取边、来源和目标节点
  toolsEdgeAndNodeByEdgeId(graph, edgeId) {
    const edge: any = graph.getCellById(edgeId);
    const sourceNode: any = edge.getSourceCell();
    const targetNode: any = edge.getTargetCell();

    return { edge, sourceNode, targetNode };
  }

  // 画布上节点间添加边
  toolsAddEdge(graph, source: { cell: string; port: string }, target: { cell: string; port: string }) {
    const edge = graph.addEdge({
      source,
      target,
      ...commonEdgeAttrs,
    });
    graph.trigger('edge:connected', { edge });

    return edge;
  }

  /**
   * 两个节点之间连边
   * @param graph 画布
   * @param sourceNode 来源节点
   * @param targetNode 目标节点
   * @param sourceDirection 来源方向
   * @param targetDirection 目标方向
   */
  toolsAddEdgeBetweenTwoNodes(graph, data: IAddEdgeBetweenTwoNodes) {
    const { sourceNode, targetNode, sourceDirection, targetDirection } = data;
    if (!sourceNode || !targetNode) return;
    const port = sourceNode.getPortsByGroup(sourceDirection)[0];
    const sourcePort: any = { cell: sourceNode.id, port: port.id };
    const targetPortGroup = targetNode.getPortsByGroup(targetDirection)[0];
    const targetPort: any = { cell: targetNode.id, port: targetPortGroup.id };
    this.toolsAddEdge(graph, sourcePort, targetPort);
  }

  // 边添加禁止删除按钮工具
  toolsEdgeDisabledRemoveButton(edge) {
    // 显示连线禁止删除按钮
    edge.addTools([
      {
        name: 'edge-disabled-button-remove',
        args: {
          distance: 0.66,
        },
      },
    ]);
  }

  // 节点添加编辑工具
  toolsNodeEditor(graph, node) {
    const allNodes = graph.getNodes();
    allNodes.forEach((i) => {
      i.removeTool('node-text-editor');
    });

    node.addTools({
      name: 'node-text-editor',
      args: {
        attrs: {
          fontSize: 14,
          fontFamily: 'Arial, helvetica, sans-serif',
          color: '#000',
          backgroundColor: '#fff',
        },
        getText({ cell }) {
          return cell.attr('label/text');
        },
        setText: ({ cell, value }) => {
          // 更新面板节点名称
          this.viewStoreService.setState((state) => {
            const { propertiesObj } = state;
            propertiesObj[cell.id].name = value;
            propertiesObj[cell.id].lang.name = {
              zh_CN: value,
              zh_TW: value,
              en_US: value,
            };
          });

          if (!value) {
            this.athMessageService.error(this.translateService.instant('dj-名称必填！'));
            cell.attr('label/text', null);
            return;
          }
          cell.attr('label/text', value);
        },
      },
    });
  }

  // 创建节点
  toolsCreateNode(graph, data: ICreateNodeParams) {
    const node = graph.createNode(commonNode({ toolType: 'graph', ...data }));
    node.setData({ ...(data || {}), nodeId: node.id });
    this.toolsNodeStyle(node, 'none');

    return node;
  }

  // 通知节点变量更新
  private noticeNodeVariableChange(type: 'add' | 'delete' | 'update', node: any, id: string) {
    const data = node.data;
    if (nodeCanAddToOptions(data.nodeType)) {
      this.variableService.handleChangeVariableData({
        type,
        item: {
          _nodeId: data.nodeId,
          nodeId: id,
          varName: data.nodeName,
          name: data.nodeName,
          dataType: 'Node',
        },
      });
    }
  }

  // 画布上新增某个节点
  toolsAddNode(graph, node, copyNodeId?: string) {
    const { nodeId, nodeType, isVerificationPassed } = node?.data || {};
    graph.addNode(node);

    // 默认节点可编辑
    // this.toolsNodeEditor(graph, node);

    const defaultConfig: any = this.viewStoreService.initPropertiesByType(nodeType, this.translateService);

    if (node.data.conditionStartId && node.data.conditionEndId) {
      // 条件分支右侧面板数据中增加 conditionStartId 标记
      defaultConfig['conditionStartId'] = node.data.conditionStartId;
    } else if (node.data.parallelStartId && node.data.parallelEndId) {
      // 并行分支右侧面板数据中增加 parallelStartId 标记
      defaultConfig['parallelStartId'] = node.data.parallelStartId;
    }
    this.viewStoreService.setState((state) => {
      const { propertiesObj, originalFlowData } = state;
      const { processId } = originalFlowData || {};

      if (copyNodeId) {
        const copyPropertiesObj = cloneDeep(propertiesObj[copyNodeId]);
        // 复制节点，采用和下面新增一样的逻辑
        if ([NodeType.AUTO_WAIT, NodeType.AUTO_SUB_PROJECT, NodeType.AUTO_EVOKE_WAIT].includes(defaultConfig._nodeType)) {
          propertiesObj[nodeId] = {
            ...copyPropertiesObj,
            id: `${defaultConfig._nodeType}_${nodeId}`,
            _isValidPassed: isVerificationPassed,
            _nodeId: nodeId,
            _nodeType: nodeType,
          };
        } else {
          propertiesObj[nodeId] = {
            ...copyPropertiesObj,
            id: `${defaultConfig.type}_${nodeId}`,
            _isValidPassed: isVerificationPassed,
            _nodeId: nodeId,
            _nodeType: nodeType,
          };
        }
      } else {
        if ([NodeType.AUTO_WAIT, NodeType.AUTO_SUB_PROJECT, NodeType.AUTO_EVOKE_WAIT].includes(defaultConfig._nodeType)) {
          propertiesObj[nodeId] = {
            ...defaultConfig,
            id: `${defaultConfig._nodeType}_${nodeId}`,
            _isValidPassed: isVerificationPassed,
            _nodeId: nodeId,
            _nodeType: nodeType,
          };
        } else {
          propertiesObj[nodeId] = {
            ...defaultConfig,
            id: `${defaultConfig.type}_${nodeId}`,
            _isValidPassed: isVerificationPassed,
            _nodeId: nodeId,
            _nodeType: nodeType,
          };
        }
        // 全局存在
        if (processId && propertiesObj?.[processId]) {
          // 全局设置绑定
          if (
            propertiesObj[processId].hasOwnProperty('bindForm') &&
            [NodeType.MANUAL_APPROVE, NodeType.MANUAL_EXECUTION].includes(nodeType)
          ) {
            propertiesObj[nodeId].bindForm = propertiesObj[processId].bindForm;
          }
        }
      }
    });
    this.noticeNodeVariableChange('add', node, `${defaultConfig.type}_${nodeId}`);
  }

  // 画布上删除某个节点
  toolsRemoveNode(graph, node) {
    const { nodeId } = node?.data || {};
    graph.removeCell(node);

    this.viewStoreService.setState((state) => {
      const { propertiesObj } = state;
      if (propertiesObj?.[nodeId]) {
        const id = propertiesObj?.[nodeId].id;
        delete propertiesObj[nodeId];
      }
    });
    this.noticeNodeVariableChange('delete', node, undefined);
    this.handleRemoveNode(graph);
    this.handleDeleteEndOutput(node)
  }

  // 画布上删除多个节点
  toolsRemoveMoreNodes(graph, nodes) {
    graph.removeCells(nodes);

    nodes.forEach((node) => {
      const { nodeId } = node?.data || {};
      this.viewStoreService.setState((state) => {
        const { propertiesObj } = state;
        if (propertiesObj?.[nodeId]) {
          delete propertiesObj[nodeId];
        }
      });
      this.noticeNodeVariableChange('delete', node, undefined);
      this.handleRemoveNode(graph);
    });
  }

  // 撤销画布，节点属性同步
  toolsHistoryUndoUpdateProperties(cmds) {
    // 判断是否有 add 节点被撤销，如果有，则删除对应的属性配置信息
    // 判断是否有 remove 节点被撤销，如果有，则添加对应的属性配置信息
    cmds.forEach((item) => {
      const { event, data } = item;
      if (event === 'cell:added') {
        const { nodeId } = data?.props?.data || {};
        this.viewStoreService.setState((state) => {
          const { propertiesObj } = state;
          if (propertiesObj?.[nodeId]) {
            delete propertiesObj[nodeId];
          }
        });
      }
      if (event === 'cell:removed') {
        const { nodeId, nodeType } = data?.props?.data || {};
        this.viewStoreService.setState((state) => {
          const { propertiesObj } = state;
          propertiesObj[nodeId] = {
            nodeType,
          };
        });
      }
    });
  }

  // 重做画布，节点属性同步
  toolsHistoryRedoUpdateProperties(cmds) {
    // 判断是否有 add 节点被重做，如果有，需要添加对应的属性配置信息
    // 判断是否有 remove 节点被重做，如果有，需要删除对应的属性配置信息
    cmds.forEach((item) => {
      const { event, data } = item;
      if (event === 'cell:added') {
        const { nodeId, nodeType } = data?.props?.data || {};
        this.viewStoreService.setState((state) => {
          const { propertiesObj } = state;
          propertiesObj[nodeId] = {
            nodeType,
          };
        });
      }
      if (event === 'cell:removed') {
        const { nodeId } = data?.props?.data || {};
        this.viewStoreService.setState((state) => {
          const { propertiesObj } = state;
          if (propertiesObj?.[nodeId]) {
            delete propertiesObj[nodeId];
          }
        });
      }
    });
  }

  /**
   * 节点样式控制
   * @param node 节点
   * @param type 类型，none|highlight
   */
  toolsNodeStyle(node, type?) {
    const { isSelected, isVerificationPassed } = node?.data || {};
    let isPassed = isVerificationPassed;

    if (isJSON(isVerificationPassed)) {
      isPassed = !Object.values(isVerificationPassed).includes(false);
    }

    if (isSelected && !isPassed) {
      // 警告加粗
      node.setAttrByPath('body/stroke', '#EA3D46');
      node.setAttrByPath('body/strokeWidth', 2);
      return;
    }

    if (isSelected && isPassed) {
      // 选中加粗
      node.setAttrByPath('body/stroke', '#6A4CFF');
      node.setAttrByPath('body/strokeWidth', 2);
      return;
    }

    if (!isSelected && !isPassed) {
      // 警告不加粗
      node.setAttrByPath('body/stroke', '#EA3D46');
      node.setAttrByPath('body/strokeWidth', 1);
      return;
    }

    if (type === 'none') {
      // 无边框
      node.setAttrByPath('body/stroke', '#E9E9E9');
      node.setAttrByPath('body/strokeWidth', 0);
      return;
    }

    if (type === 'highlight') {
      // 高亮边框
      node.setAttrByPath('body/stroke', '#6A4CFF');
      node.setAttrByPath('body/strokeWidth', 1);
      return;
    }
  }

  /**
   * 边样式控制
   * @param edge 边
   * @param isHightlight 是否高亮
   */
  toolsEdgeStyle(edge, isHightlight) {
    if (isHightlight) {
      edge.setAttrByPath('line/stroke', '#6A4CFF');
      edge.setAttrByPath('line/strokeWidth', 2);
      edge.setAttrByPath('line/strokeDasharray', 0);
    } else {
      edge.setAttrByPath('line/stroke', '#A2B1C3');
      edge.setAttrByPath('line/strokeWidth', 1);
      edge.setAttrByPath('line/strokeDasharray', 0);
    }
  }

  // 清除所有节点的高亮和工具
  toolsClearAllNodesTools(graph) {
    const allNodes = graph.getNodes();
    allNodes?.forEach((node) => {
      // 设置节点未选中状态
      node.setData({ ...(node?.data || {}), isSelected: false });
      // 设置节点样式
      this.toolsNodeStyle(node, 'none');
      // 链接桩不可见
      const ports = node.getPorts();
      ports.forEach((port) => {
        node.setPortProp(port.id, 'attrs/circle/style/visibility', 'hidden');
      });
      // 设置复制按钮不可见
      node.removeTool('copy-node-button');
      // 设置删除按钮不可见
      node.removeTool('delete-node-button');
      // 设置气泡不可见
      node.removeTool('node-tooltip');
      // 移除节点周边 + 图标
      node.removeTool('node-add-node-button');
    });
  }

  // 清除所有边的高亮和工具
  toolsClearAllEdgesTools(graph) {
    const allEdges = graph.getEdges();
    allEdges?.forEach((edge) => {
      this.toolsClearEdgeTools(edge);
    });
  }

  // 清除边的高亮和工具
  toolsClearEdgeTools(edge) {
    // 移除删除边的按钮
    edge.removeTool('button-remove');
    edge.removeTool('edge-disabled-button-remove');
    edge.removeTool('edge-add-node-button');
    // 边恢复默认样式
    this.toolsEdgeStyle(edge, false);
  }

  /**
   * 删除节点
   * @param graph
   */
  handleRemoveNode(graph) {
    const {
      propertiesObj,
      originalFlowData: { allModelList, code },
    } = this.viewStoreService.state;
    const allNodesId =
      graph
        .getNodes()
        .filter((e) => ModelNodes.includes(e?.data?.nodeType))
        ?.map((j) => {
          const { nodeId } = j?.data || {};
          return nodeId;
        }) || [];

    const allModelNodes = [];
    const map = new Map();
    [code, ...allNodesId]?.forEach((k) => {
      const { modelCode, serviceCode } = propertiesObj?.[k]?.bindForm || {};
      if (modelCode && serviceCode && !map.has(modelCode + serviceCode)) {
        map.set(modelCode + serviceCode, modelCode + serviceCode);
        allModelNodes.push({
          modelCode,
          serviceCode,
          varName: allModelList?.find((e) => e.code === modelCode)?.name || '',
          dataType: VariableType.MODEL,
          uniqueKey: `${modelCode}&${serviceCode}`,
        });
      }
    });
    this.viewStoreService.setState((state) => {
      state.propertiesObj[code].modelVariables = allModelNodes;
    });
    this.variableService.variableRefresh$.next(true);
  }
  /**
   * @description: 删除节点时检查结束节点输出，如果输出内容包含所删除节点则同时删除该输出内容，防止将不存在的节点保存到输出变量
   * @param {*} node 删除节点
   * @return {*}
   */
  handleDeleteEndOutput(node) {
    const delNodeData = node.getData();
    const nodeDatas = Object.values(this.viewStoreService.state?.propertiesObj) || []
    const endNode: any = nodeDatas?.find((item: any) => (item.type === SDNodeType.END_NODE || item._nodeType === SDNodeType.END_NODE));
    const endMapping = endNode.endOutput?.mapping || []
    if (endMapping.some(item => item.valueCode.includes(delNodeData.nodeId) && item.valueType === VariableType.NODE)) {
      const newMapping = endMapping.filter(item => !item.valueCode.includes(delNodeData.nodeId))
      // 更新结束节点的输出内容
      this.viewStoreService.setState((state) => {
        const { propertiesObj } = state;
        propertiesObj[endNode._nodeId].endOutput.mapping = newMapping;
      });
    }
  }
}
