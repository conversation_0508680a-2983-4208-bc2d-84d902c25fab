import { Injectable } from '@angular/core';
import { BehaviorSubject, Subject } from 'rxjs';
import { ModelNodes, VariableType } from '../config/typings';
import { ViewStoreService } from './store.service';

@Injectable()
export class VariableService {
  variableRefresh$ = new BehaviorSubject<any>(null);
  constructor(private viewStoreService: ViewStoreService) {}

  /**
   * 全局变量数据更新
   * @param data
   */
  handleChangeVariableData(data: { type: 'add' | 'delete' | 'update'; item: any }) {
    const { item, type } = data;
    const {
      originalFlowData: { code },
    } = this.viewStoreService.state;

    this.viewStoreService.setState((state) => {
      const { dataType, varName } = item;
      const { propertiesObj } = state;
      switch (dataType) {
        case VariableType.DTD: // DTD变量
          const dtdVariable = propertiesObj?.[code]?.dtdVariable;
          if (type === 'add') {
            dtdVariable.push(item);
          } else {
            const cIndex = dtdVariable?.findIndex((node) => node.varName === varName);
            if (cIndex > -1) {
              if (type === 'update') {
                dtdVariable[cIndex] = item;
              } else {
                // delete
                dtdVariable.splice(cIndex, 1);
              }
            }
          }
          state.propertiesObj = {
            ...propertiesObj,
          };
          break;
        case VariableType.NODE: // 节点变量
          const nodeVariables = propertiesObj?.[code]?.nodeVariables || [];
          if (type === 'add') {
            nodeVariables.push(item);
          } else {
            const cIndex = nodeVariables.findIndex((node) => node._nodeId === item._nodeId);
            if (cIndex > -1) {
              if (type === 'update') nodeVariables[cIndex] = item;
              else nodeVariables.splice(cIndex, 1);
            }
          }
          state.propertiesObj = {
            ...propertiesObj,
          };
          break;
        case VariableType.Mechanism: // 机制变量
          const mechanismVariables = propertiesObj?.[code]?.mechanismVariables;
          if (type === 'add') {
            mechanismVariables.push(item);
          } else {
            const bcIndex = mechanismVariables?.findIndex((node) => node.varName === varName);
            if (bcIndex > -1) {
              if (type === 'update') {
                mechanismVariables[bcIndex] = item;
              } else {
                // delete
                mechanismVariables.splice(bcIndex, 1);
              }
            }
          }
          state.propertiesObj = {
            ...propertiesObj,
          };
          break;
        case VariableType.BUSINESS_OBJECT: // 业务对象变量
          const businessObjectVariables = propertiesObj?.[code]?.businessObjectVariables;
          if (type === 'add') {
            businessObjectVariables.push(item);
          } else {
            const bcIndex = businessObjectVariables?.findIndex((node) => node.varName === varName);
            if (bcIndex > -1) {
              if (type === 'update') {
                businessObjectVariables[bcIndex] = item;
              } else {
                // delete
                businessObjectVariables.splice(bcIndex, 1);
              }
            }
          }
          state.propertiesObj = {
            ...propertiesObj,
          };
          break;
        case VariableType.STRING: // 自定义变量
        case VariableType.INTEGER:
        case VariableType.DECIMAL:
        case VariableType.BOOLEAN:
        case VariableType.DATETIME:
          const customVariables = propertiesObj?.[code]?.customVariables;
          if (type === 'add') {
            customVariables.push(item);
          } else {
            const cIndex = customVariables?.findIndex((node) => node.varName === varName);
            if (cIndex > -1) {
              if (type === 'update') {
                customVariables[cIndex] = item;
              } else {
                // delete
                customVariables.splice(cIndex, 1);
              }
            }
          }
          state.propertiesObj = {
            ...propertiesObj,
          };
          break;
        default: // 系统变量,暂时不做处理
          break;
      }
    });
    this.variableRefresh$.next(true);
  }

  /**
   * 校验新增的唯一性
   * @param data
   */
  handleCheckAddVariable(data) {
    const {
      originalFlowData: { code },
      propertiesObj,
    } = this.viewStoreService.state;
    const customVariables = propertiesObj?.[code]?.customVariables || [];
    const systemVariable = propertiesObj?.[code]?.systemVariable || [];
    const businessObjectVariables = propertiesObj?.[code]?.businessObjectVariables || [];
    const mechanismVariables = propertiesObj?.[code]?.mechanismVariables || [];
    const dtdVariable = propertiesObj?.[code]?.dtdVariable || [];
    const allVariables = [
      ...customVariables,
      ...systemVariable,
      ...businessObjectVariables,
      ...mechanismVariables,
      ...dtdVariable,
    ];
    return !allVariables.some((item) => item.varName === data?.varName);
  }
}
