import { Inject, Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';
import { SystemConfigService, DW_APP_AUTH_TOKEN } from 'common/service/system-config.service';
import { AppService } from 'pages/apps/app.service';

@Injectable({
  providedIn: 'root',
})
export class ViewApiService {
  serviceUrl: string;
  private httpHeaders: HttpHeaders;
  private eocUrl: string;
  appCode: any;

  empList: any[] = []; // 人员列表
  dutyList: any[] = []; // 职责列表
  deptList: any[] = []; // 部门列表
  levelList: any[] = []; // 人员层级列表
  deptLevelList: any[] = []; // 部门层级列表
  headers: any = {};
  tenantHeaders: any = {};

  get combineHeaders() {
    return {
      ...this.headers,
      ...this.tenantHeaders,
    };
  }

  constructor(
    private http: HttpClient,
    @Inject(DW_APP_AUTH_TOKEN) private dwAppAuthToken: string,
    private configService: SystemConfigService,
    private appService: AppService,
  ) {
    this.configService.get('eocUrl').subscribe((url: string): void => {
      this.eocUrl = url;
    });
    this.configService.get('adesignerUrl').subscribe((url) => {
      this.serviceUrl = url;
    });
    this.httpHeaders = new HttpHeaders({
      Authorization: this.dwAppAuthToken,
    });
    this.appCode = this.appService?.selectedApp?.code;
    // 缓存人员的信息
    this.loadEmp();
    this.loadDuty();
    this.loadDept();
    this.loadLevel();
    this.loadDeptLevel();
  }

  // 查询当前业务对象下的所有界面
  queryPageDesignByBusinessCode(businessCode): Observable<any> {
    const url = `${this.serviceUrl}/athena-designer/businessDir/queryPageDesign`;
    return this.http.post(url, { businessCode });
  }

  // 新建|保存流程
  postUpsertProcess(params: any): Observable<any> {
    const param = { ...params, application: this.appCode };
    const url = `${this.serviceUrl}/athena-designer/process/upsertProcess`;
    return this.http.post(url, param, { headers: this.headers });
  }

  // 新建|保存流程（租户级）
  postUpsertProcessTenant(params: any): Observable<any> {
    const url = `${this.serviceUrl}/athena-designer/process/tenant/updateTenantProcess`;
    return this.http.post(
      url,
      { ...params, application: this.appCode },
      { headers: { ...this.tenantHeaders, ...this.headers } },
    );
  }

  // 查询流程详情
  getFindProcessById(params: any): Observable<any> {
    const url = `${this.serviceUrl}/athena-designer/process/findProcessById`;
    return this.http.get(url, { params, headers: this.headers });
  }

  // 查询流程详情（租户级）
  getFindProcessByIdTenant(params: any): Observable<any> {
    const url = `${this.serviceUrl}/athena-designer/process/tenant/findProcessById`;
    return this.http.get(url, { params, headers: { ...this.tenantHeaders, ...this.headers } });
  }

  // 获取员工信息
  getEmp(): Observable<any> {
    const url = `${this.eocUrl}/api/eoc/v2/emp/has/user`;
    return this.http.get(url, { headers: this.httpHeaders });
  }

  // 获取职能信息
  getDuty(): Observable<any> {
    const url = `${this.eocUrl}/api/eoc/v2/duty/list?pageSize=99999`;
    return this.http.get(url);
  }

  // 获取部门信息
  getDept(): Observable<any> {
    const url = `${this.serviceUrl}/athena-designer/eoc/getDepartmentList`;
    return this.http.get(url);
  }

  // 获取人员层级信息
  getLevel(): Observable<any> {
    const url = `${this.eocUrl}/api/eoc/v2/emp/level`;
    return this.http.get(url);
  }

  // 获取部门层级信息
  getDeptLevel(): Observable<any> {
    const url = `${this.eocUrl}/api/eoc/v2/dept/level`;
    return this.http.get(url);
  }

  // 加载人员
  loadEmp(): void {
    this.getEmp().subscribe((res) => {
      const empList = res.data || [];
      this.empList = empList;
    });
  }

  // 加载职能
  loadDuty(): void {
    this.getDuty().subscribe((res) => {
      this.dutyList = res.data.list;
    });
  }

  // 加载部门
  loadDept(): void {
    this.getDept().subscribe((res) => {
      this.deptList = res.data;
    });
  }
  // 加载人员层级
  loadLevel(): void {
    this.getLevel().subscribe((res) => {
      this.levelList = res.data;
    });
  }

  // 加载部门层级
  loadDeptLevel(): void {
    this.getDeptLevel().subscribe((res) => {
      this.deptLevelList = res.data;
    });
  }

  // 查询业务对象列表
  postQueryBusinessObject(payload?: any): Observable<any> {
    const url = `${this.serviceUrl}/athena-designer/businessDir/queryList`;
    return this.http.post(url, { ...payload, application: this.appCode });
  }

  // 查询事件列表
  postEventListQuery(payload?: any): Observable<any> {
    const url = `${this.serviceUrl}/athena-designer/event/eventListQuery`;
    return this.http.post(url, { ...payload, application: this.appCode });
  }

  getApiProvider(apiName) {
    const url = `${this.serviceUrl}/athena-designer/action/getApiProvider`;
    return this.http.get(url, { params: { apiName } });
  }

  /**
   * 查询字段列表(数据映射面板->UI模式->当前节点下拉列表)
   * @param params
   * @returns
   */
  queryFieldsByCode(params): Observable<any> {
    return this.http.get(`${this.serviceUrl}/athena-designer/pageDesignModel/queryFieldsByCode`, { params });
  }

  /**
   * 数据节点-数据映射UI模式下-右侧业务对象tree
   * @param params
   * @returns
   */
  queryFieldsGroupList(params): Observable<any> {
    return this.http.post(`${this.serviceUrl}/athena-designer/pageDesignModel/queryFieldsGroupList`, params);
  }

  /** 获取当前解决方案下的模型的列表 */
  getModelDesignList() {
    const url = `${this.serviceUrl}/athena-designer/modelDriver/modelAssign/queryList`;
    return this.http.get(url, { params: { application: this.appService?.selectedApp?.code, queryType: 'list' } });
  }

  queryQueryplanList(params: { application: string; businessCode: string }): Observable<any> {
    return this.http.post(`${this.serviceUrl}/athena-designer/dataView/query/queryplan/list`, params);
  }

  /** 获取当前解决方案下的模型列表 */
  getBusinessDirList(): Observable<any> {
    const url = `${this.serviceUrl}/athena-designer/businessDir/queryList`;
    return this.http.post(url, { application: this.appService?.selectedApp?.code });
  }

  batchQueryModelDetail(params: any): Observable<any> {
    return this.http.post(`${this.serviceUrl}/athena-designer/modelAssign/batchQuery/modelDetails`, params);
  }

  /**
   * 查询流程版本列表
   */
  loadProcessVersionList(params: any): Observable<any> {
    return this.http.get(`${this.serviceUrl}/athena-designer/processVersion/queryList`, {
      params,
      headers: this.tenantHeaders,
    });
  }

  /**
   * 新增流程版本
   * @param params
   * @returns
   */
  createProcessVersion(params: any): Observable<any> {
    return this.http.post(`${this.serviceUrl}/athena-designer/processVersion/create`, params, {
      headers: this.tenantHeaders,
    });
  }

  /**
   * 更新流程版本
   * @param params
   * @returns
   */
  updateProcessRemark(params: any): Observable<any> {
    return this.http.post(`${this.serviceUrl}/athena-designer/processVersion/updateRemark`, params, {
      headers: this.tenantHeaders,
    });
  }

  /**
   * 删除流程版本
   * @param params
   * @returns
   */
  removeProcessVersion(params: any): Observable<any> {
    return this.http.post(`${this.serviceUrl}/athena-designer/processVersion/remove`, params, {
      headers: this.tenantHeaders,
    });
  }

  /**
   * 生效流程版本
   * @param params
   * @returns
   */
  effectProcessVersion(params: any): Observable<any> {
    return this.http.post(`${this.serviceUrl}/athena-designer/processVersion/effect`, params, {
      headers: this.tenantHeaders,
    });
  }

  /* 查询当前解决方案下的基础资料
   * @returns
   */
  loadBasicByAppCode(): Observable<any> {
    const url = `${this.serviceUrl}/athena-designer/activityConfigs/getActivityListByPatternAndApplication`;
    return this.http.get(url, { params: { pattern: 'DATA_ENTRY', application: this.appService?.selectedApp?.code } });
  }

  /**
   * 获取模型驱动数据
   * @param formId
   * @returns
   */
  fetchModelDrivenData(params: { code: string; serviceCode: string }): Observable<any> {
    const url = `${this.serviceUrl}/athena-designer/modelDriver/queryModelByCode`;
    return this.http.get(url, { params });
  }

  /**
   * 批量查询模型的详情
   * @param params
   * @returns
   */
  queryModelByCodes(params: { modelCode: string; serviceCode: string }[]): Observable<any> {
    const url = `${this.serviceUrl}/athena-designer/modelDriver/queryModelByCodes`;
    return this.http.post(url, params);
  }

  /**
   * 查询所有开启的场景
   */
  queryAllOpenAimScenes(): Observable<any> {
    const url = `${this.serviceUrl}/athena-designer/aimScene/queryAllOpenAimScenes`;
    return this.http.get(url, { params: { application: this.appService?.selectedApp?.code } });
  }

  /**
   * 流程场景中选择事件获取数据结构
   */
  queryEventBodyExplain(sceneId: string): Observable<any> {
    const url = `${this.serviceUrl}/athena-designer/aimScene/queryEventBodyExplain`;
    return this.http.get(url, { params: { sceneId } });
  }

  /** 获取父级前序大T */
  queryBeforeNodes(taskCode): Observable<any> {
    const url = `${this.serviceUrl}/athena-designer/process/queryBeforeNodes`;
    return this.http.post(url, { application: this.appService?.selectedApp?.code , taskCode});
  }

  /**
   * 等待节点中选择业务主键
   */
  queryFieldsByDataType(params: any): Observable<any> {
    const url = `${this.serviceUrl}/athena-designer/pageDesignModel/queryFieldsByDataType`;
    return this.http.get(url, { params });
  }
}
