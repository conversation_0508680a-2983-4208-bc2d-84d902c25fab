/**
 * 从editor项目复制而来，一律维护editor，维护完成同步过来
 */
/**
 * 菜单code 改用 平台的 pageCode
 */
export enum PageCode {
  /**
   * 单档多栏的 界面设计
   */
  BASIC_DATA = 'basic-data',
  /**
   * 浏览界面
   */
  BROWSE_PAGE = 'browse-page',
  /**
   * 编辑界面
   */
  EDIT_PAGE = 'edit-page',
  /**
   * 子页面
   */
  SUB_PAGE = 'sub-page',
  /**
   * TODO: 自定义任务卡？？？确定之后修改
   */
  TASK_CARD = 'task-card',
}

/**
 * 业务类型定义
 */
export enum Pattern {
  /**
   * 基础资料
   */
  DATA_ENTRY = 'DATA_ENTRY',
  /**
   * 报表
   */
  STATEMENT = 'STATEMENT',
  /**
   * TODO: 任务相关，只是定义，不保熟
   */
  BUSINESS = 'BUSINESS',
  CUSTOM = 'CUSTOM',
}

/**
 * 基础资料类型定义
 */
export enum Category {
  /**
   * 模型驱动单档多栏
   */
  'SIGN-DOCUMENT' = 'SIGN-DOCUMENT',
  /**
   * 模型驱动单档多栏树
   */
  'TREEDATA-SINGLE-DOCUMENT' = 'TREEDATA-SINGLE-DOCUMENT',
  /**
   * 模型驱动单档
   */
  'DOUBLE-DOCUMENT-FORM' = 'DOUBLE-DOCUMENT-FORM',
  /**
   * 模型驱动单档树
   */
  'TREEDATA-DOUBLE-DOCUMENT-FORM' = 'TREEDATA-DOUBLE-DOCUMENT-FORM',
  /**
   * 模型驱动双档
   */
  'DOUBLE-DOCUMENT' = 'DOUBLE-DOCUMENT',
  /**
   * 模型驱动双档树
   */
  'TREEDATA-DOUBLE-DOCUMENT' = 'TREEDATA-DOUBLE-DOCUMENT',
  /**
   * 模型驱动多档
   */
  'DOUBLE-DOCUMENT-MULTI' = 'DOUBLE-DOCUMENT-MULTI',
  /**
   * 模型驱动多档树
   */
  'TREEDATA-DOUBLE-DOCUMENT-MULTI' = 'TREEDATA-DOUBLE-DOCUMENT-MULTI',
}

/**
 * 按钮类型定义
 */
export enum ButtonType {
  /**
   * 按钮基类
   */
  BUTTON = 'BUTTON',
  BUTTON_DECOUPLE = 'BUTTON_DECOUPLE',
  /**
   * 子页面按钮
   */
  BUTTON_SUB_PAGE = 'BUTTON_SUB_PAGE',
  /**
   * 子页面-保存并关闭, 特殊，转换时需转成 BUTTON_DATA_SAVE
   */
  // BUTTON_SUB_PAGE_SAVE = 'BUTTON_SUB_PAGE_SAVE',
  /**
   * 打印按钮
   */
  BUTTON_PRINT = 'BUTTON_PRINT',
  /**
   * 提交按钮：删除 (原submitAction)
   */
  BUTTON_DATA_DELETE = 'BUTTON_DATA_DELETE',
  /**
   * 提交按钮：删除到回收站 (原submitAction)
   */
  BUTTON_DELETE_TO_RECYCLE = 'BUTTON_DELETE_TO_RECYCLE',
  /**
   * 提交按钮：彻底删除 (原submitAction, 暂是没有使用场景)
   */
  BUTTON_RECYCLE_DELETE = 'BUTTON_RECYCLE_DELETE',
  /**
   * 提交按钮：删除全部 (submitAction类型，原submitAction也没有, 暂是没有使用场景, 只是定义)
   */
  BUTTON_DATA_DELETEALL = 'BUTTON_DATA_DELETEALL',
  /**
   * 提交按钮：新建保存 (原submitAction)
   */
  BUTTON_DATA_SAVE = 'BUTTON_DATA_SAVE',
  /**
   * 提交按钮：组合式保存（新建保存+更新保存） (原submitAction)
   */
  BUTTON_DATA_COMBINE_SAVE = 'BUTTON_DATA_COMBINE_SAVE',
  /**
   * 保存按钮（新）
   */
  BUTTON_SUBMIT_ACTION_DECOUPLE = 'BUTTON_SUBMIT_ACTION_DECOUPLE',
  /**
   * 组合式保存按钮（新）
   */
  BUTTON_COMBINE_SAVE_DECOUPLE = 'BUTTON_COMBINE_SAVE_DECOUPLE',

  /**
   * 新增按钮（新）
   */
  BUTTON_OPENPAGE_ADD_DECOUPLE = 'BUTTON_OPENPAGE_ADD_DECOUPLE',

  /**
   * 删除按钮（新）
   */
  BUTTON_DATA_DELETE_DECOUPLE = 'BUTTON_DATA_DELETE_DECOUPLE',
  /**
   * 新增行
   */
  BUTTON_ADD_ITEM_DECOUPLE = 'BUTTON_ADD_ITEM_DECOUPLE',

  /**
   * 维护
   */
  BUTTON_EDIT_ITEM_DECOUPLE = 'BUTTON_EDIT_ITEM_DECOUPLE',

  /**
   * 详情
   */
  BUTTON_DETAIL_ITEM_DECOUPLE = 'BUTTON_DETAIL_ITEM_DECOUPLE',

  /**
   * 删除行
   */
  BUTTON_DELETE_ITEM_DECOUPLE = 'BUTTON_DELETE_ITEM_DECOUPLE',

  /**
   * 提交按钮：更新保存 (原submitAction)
   */
  BUTTON_DATA_UPDATE = 'BUTTON_DATA_UPDATE',
  /**
   * 提交按钮：失效 (原submitAction)
   */
  BUTTON_DATA_INVALID = 'BUTTON_DATA_INVALID',
  /**
   * 提交按钮：生效 (原submitAction)
   */
  BUTTON_DATA_VALID = 'BUTTON_DATA_VALID',
  /**
   * 提交按钮：取消生效 2025-05-14新增
   */
  BUTTON_DATA_CANCEL_VALID = 'BUTTON_DATA_CANCEL_VALID',
  /**
   * 提交按钮：重新生效 2025-05-14新增
   */
  BUTTON_DATA_RESET_VALID = 'BUTTON_DATA_RESET_VALID',
  /**
   * 提交按钮：还原 (原submitAction, 暂时没有使用场景)
   */
  BUTTON_DATA_RECOVER = 'BUTTON_DATA_RECOVER',
  /**
   * 提交按钮：送审 (原submitAction)
   */
  BUTTON_WORKFLOW_INVOKE = 'BUTTON_WORKFLOW_INVOKE',
  /**
   * 提交按钮：撤审 (原submitAction)
   */
  BUTTON_WORKFLOW_ABORT = 'BUTTON_WORKFLOW_ABORT',
  /**
   * TODO: 2024-12-13 以下按钮本期运行时没有定义，后续有类型定义之后需要修改
   */
  /**
   * 提交按钮：同意
   */
  BUTTON_AGREE = 'BUTTON_AGREE',
  /**
   * 提交按钮：不同意
   */
  BUTTON_DISAGREE = 'BUTTON_DISAGREE',
  /**
   * 提交按钮：退回
   */
  BUTTON_ACT_RETURN = 'BUTTON_ACT_RETURN',
  /**
   * 提交按钮：加签
   */
  BUTTON_ACT_ADD = 'BUTTON_ACT_ADD',
  /**
   * 提交按钮：提交
   */
  BUTTON_WORKFLOW_SUBMIT = 'BUTTON_WORKFLOW_SUBMIT',
  /**
   * 一般任务：终止
   */
  BUTTON_DATA_TERMINATE = 'BUTTON_DATA_TERMINATE',
  /**
   * 报表查询按钮
   */
  BUTTON_REPORT_QUERY = 'BUTTON_REPORT_QUERY',
  /**
   * 签核任务：退回重办
   */
  BUTTON_TASK_REEXECUTE = 'BUTTON_TASK_REEXECUTE',
  /**
   * 签核任务：终止任务
   */
  BUTTON_TASK_TERMINATE = 'BUTTON_TASK_TERMINATE',
  /**
   * 签核任务：退回重签
   */
  BUTTON_TASK_REAPPROVE = 'BUTTON_TASK_REAPPROVE',
  /**
   * 签核任务：加签
   */
  BUTTON_TASK_ADD = 'BUTTON_TASK_ADD',
  /**
   * 签核任务：同意
   */
  BUTTON_TASK_AGREE = 'BUTTON_TASK_AGREE',
  /**
   * 原operations -- start
   * WARN: 原operations的打印复用BUTTON_PRINT
   */
  /**
   * 前端导出
   */
  BUTTON_FRONT_EXPORT = 'BUTTON_FRONT_EXPORT',
  /**
   * 数据导出
   */
  BUTTON_BACKEND_EXPORT = 'BUTTON_BACKEND_EXPORT',
  /**
   * 文件导入
   */
  BUTTON_UPLOAD_FILE = 'BUTTON_UPLOAD_FILE',
  /**
   * 下载模板
   */
  BUTTON_DOWNLOAD_TEMPLATE = 'BUTTON_DOWNLOAD_TEMPLATE',
  /**
   * 图纸下载
   */
  BUTTON_DRAWINGS_DOWNLOAD = 'BUTTON_DRAWINGS_DOWNLOAD',
  /**
   * 批量指定交期
   */
  BUTTON_BATCH_SET_DATE = 'BUTTON_BATCH_SET_DATE',
  /**
   * 自动更换
   */
  BUTTON_OPERATE_SCRIPT = 'BUTTON_OPERATE_SCRIPT',
  /**
   * 手动拆行
   */
  BUTTON_SPLIT_ROW = 'BUTTON_SPLIT_ROW',
  /**
   * 自动拆分
   */
  BUTTON_AUTO_SPLIT_ROW = 'BUTTON_AUTO_SPLIT_ROW',
  /**
   * 新增行
   */
  BUTTON_ADD_ITEM = 'BUTTON_ADD_ITEM',
  /**
   * 删除行
   */
  BUTTON_DELETE_ITEM = 'BUTTON_DELETE_ITEM',
  /**
   * 批量删除
   */
  BUTTON_BATCH_DELETE_ITEM = 'BUTTON_BATCH_DELETE_ITEM',
  /**
   * 复制行
   */
  BUTTON_COPY_ITEM = 'BUTTON_COPY_ITEM',
  /**
   * 双档的新增（表格外）
   */
  BUTTON_OPENPAGE_ADD = 'BUTTON_OPENPAGE_ADD',
  /**
   * 双档的维护（行内）
   */
  BUTTON_OPENPAGE_EDIT = 'BUTTON_OPENPAGE_EDIT',
  /**
   * 双档的复制（行内）
   */
  BUTTON_OPENPAGE_COPY = 'BUTTON_OPENPAGE_COPY',
  /**
   * 原operations -- end
   */
  /**
   * 原整单操作 -- start
   * WARN: 原整单操作中的新增&复制复用operation的双档新增、复制
   */
  /**
   * 编辑
   */
  BUTTON_TOOLBAR_EDIT = 'BUTTON_TOOLBAR_EDIT',
  /**
   * 上一笔
   */
  BUTTON_TOOLBAR_PREVIOUS = 'BUTTON_TOOLBAR_PREVIOUS',
  /**
   * 下一笔
   */
  BUTTON_TOOLBAR_NEXT = 'BUTTON_TOOLBAR_NEXT',
  /**
   * 原整单操作 -- end
   */
  /**
   * 2025-03-20 多模式导入
   */
  BUTTON_IMPORT = 'BUTTON_IMPORT',
}

/**
 * 按钮分类定义
 */
export enum ButtonCategory {
  /**
   * 通用按钮 -> 按钮基类
   * WARN: 2025-03-11 operations&toolbar转动态按钮需求：common不再独立为一个栏目，回归功能按钮栏目
   */
  // COMMON = 'COMMON',
  /**
   * 功能按钮,目前包括
   * 1. 按钮基类 -> 挪到COMMON中
   * WARN: 不再独立为一个栏目，回归功能按钮栏目
   * 2. 子页面按钮
   * 3. 子页面-保存并关闭 -> 挪到SUBMIT_BUTTON中
   * 4. 打印按钮
   */
  BUSINESS_BUTTON = 'BUSINESS_BUTTON',

  /**
   * 提交按钮,目前包括
   * 1. 提交按钮：删除
   * 2. 提交按钮：删除到回收站
   * 3. 提交按钮：彻底删除
   * 4. 提交按钮：删除全部
   * 5. 提交按钮：新建保存
   * 6. 提交按钮：组合式保存
   * 7. 提交按钮：更新保存
   * 8. 提交按钮：失效
   * 9. 提交按钮：生效
   * 10. 提交按钮：还原
   * 11. 提交按钮：送审
   * 12. 提交按钮：撤审
   */
  SUBMIT_BUTTON = 'SUBMIT_BUTTON',

  /**
   * 新功能按钮包括：
   * 新增
   * 保存
   * 删除
   */
  FUNCTION_BUTTON = 'FUNCTION_BUTTON',
}

/**
 * 按钮的业务类型
 */
export enum ButtonActionType {
  /**
   * 组合保存
   */
  COMBINE = 'basic-data-combine-save',
  /**
   * 更新
   */
  UPDATE = 'basic-data-update',
  /**
   * 保存
   */
  SAVE = 'basic-data-save',
  /**
   * 删除
   */
  DELETE = 'basic-data-delete',
  /**
   * 还原
   */
  RECOVER = 'basic-data-recover',
  /**
   * 失效
   */
  INVALID = 'invalid',
  /**
   * 生效
   */
  VALID = 'valid',
  /**
   * 取消生效
   * TODO: actionType未确定
   */
  CANCEL_VALID = 'cancelvalid',
  /**
   * 重新生效
   */
  RESET_VALID = 'revalid',
}

/**
 * 按钮调用API类型
 */
export enum ButtonApiType {
  /**
   * API的组合调用
   */
  COMBINE = 'COMBINE',
  /**
   * 调彻底删除API
   */
  RECYCLE = 'RECYCLE',
  /**
   * 调企业服务池的API
   */
  ESP = 'ESP',
  /**
   * 调数据驱动引擎的API
   */
  SD = 'SD',
  /**
   * 调封装好的API
   */
  UIBOT = 'UIBOT',
  /**
   * 调知识图谱的API
   */
  TM = 'TM',
  /**
   * 调任务引擎的API
   */
  TASK_ENGINE = 'TaskEngine',
  /**
   * 调任务引擎PTM的API
   */
  PTM = 'PTM',
  /**
   * 手动发起项目
   */
  MANUALPROJECT_NEW = 'MANUALPROJECT_NEW',
  /**
   * 流程
   */
  WORKFLOW = 'workflow',
}

/**
 * actionType === TaskEngine时，相应的服务名类型
 */
export enum ButtonTaskEngineService {
  /**
   * 加签
   */
  ADD_TASK = 'add-task',
  /**
   * 同意
   */
  AGREE = 'agree',
  /**
   * 不同意
   */
  DISAGREE = 'disagree',
  /**
   * 退回重签
   */
  RE_APPROVE = 'reapprove',
  /**
   * 转派
   */
  RE_ASSIGN = 'reassign',
  /**
   * 退回重办
   */
  RE_EXECUTE = 'reexecute',
  /**
   * 发起项目
   */
  START_NEW_PROJECT = 'start-new-project',
  /**
   * 终止流程
   */
  TERMINATE_TASK = 'terminate-task',
  /**
   * 提交数据到流程
   */
  SUBMIT_DATA = 'submit-data',
}

/**
 * actionType === 'UIBOT'时, 相应的服务类型
 */
export enum ButtonUIBotService {
  /**
   * 提交数据到ForecastingIntelligence
   */
  COMMIT_DATA_TO_FI = 'commit-data-to-fi',
  /**
   * 数据转派
   */
  DATA_REASSIGNMENT_ACTION = 'data-reassignment-action',
  /**
   * 从流程变量中的bpm集合中删除提交的数据
   */
  DELETE_ACTIVITY_BPM_VARIABLE_VALUE = 'delete-activity-bpm-variable-value',
  /**
   * 过滤选择数据
   */
  FILTER_SELECTED_DATA_ACTION = 'filter-selected-data-action',
  /**
   * 发起流程
   */
  START_NEW_PROCESS = 'start-new-process',
  /**
   * 终止任务的数据
   */
  TERMINATE_DATA = 'terminate-data',
  /**
   * 撤回
   */
  UIBOT_TASK_WITHDRAW = 'uibot_task_withdraw',
  /**
   * 从流程变量中的query集合中删除提交的数据
   */
  UPDATE_ACTIVITY_QUERY_VARIABLE_VALUE = 'update-activity-query-variable-value',
  /**
   * 更新任务签核状态
   */
  UPDATE_SOLVE_TASK_APPROVAL_STATE = 'update-solve-task-approval-state',
  /**
   * 更新数据状态
   */
  UPDATE_TABLE_FIELD = 'update-table-field',
  /**
   * 添加所有需要追踪的数据id到任务中心
   */
  UPDATE_TASK_TRACE_STATE = 'update-task-trace-state',
  /**
   * 任务撤回
   */
  UIBOT_RETRIVE = 'uibot_retrive',
}

/**
 * actionType === 'workflow'时, 相应的服务类型
 */
export enum ButtonWorkflowService {
  /**
   * 送审
   */
  WORKFLOW_INVOKE = 'workflow-invoke',
  /**
   * 撤审
   */
  WORKFLOW_ABORT = 'workflow-abort',
}

/**
 * 参数的类型定义
 */
export enum ActionParamType {
  /**
   * 当前行动态值
   */
  ACTIVE_ROW = 'ACTIVE_ROW',
  /**
   * 静态值
   */
  ACTIVE_ROW_CONSTANT = 'ACTIVE_ROW_CONSTANT',
  /**
   * 提交的值，或上一个action返回的值
   */
  GET_ACTION_RESPONSE = 'GET_ACTION_RESPONSE',
  /**
   * 静态值，空数组可以为[]
   */
  CONSTANT = 'CONSTANT',
  /**
   * 取系统参数，value可以为当前登录id
   */
  SYSTEM = 'SYSTEM',
  /**
   * 任务引擎推送来的变量
   */
  PROCESS_VARIABLE = 'PROCESS_VARIABLE',
}

/**
 * 参数转换器定义
 */
export enum ActionParamConverterType {
  /**
   * 空
   */
  EMPTY = '',
  StringToBooleanConverter = 'stringToBooleanConverter',
  StringToNumberConverter = 'stringToNumberConverter',
}

/**
 * 按钮condition触发时机类型
 */
export enum ButtonConditionTriggerType {
  /**
   * 初始化
   */
  INIT = 'init',
  /**
   * 数据变动时
   */
  DATA_CHANGED = 'dataChanged',
  /**
   * 提交后
   */
  IN_SUBMIT = 'inSubmit',
}

/**
 * 按钮position属性的可能值, 都是css的position的值就不注释了
 */
export enum PositionType {
  STATIC = 'static',
  RELATIVE = 'relative',
  ABSOLUTE = 'absolute',
  FIXED = 'fixed',
  STICKY = 'sticky',
  INITIAL = 'initial',
  INHERIT = 'inherit',
}

/**
 * 子页面按钮打开抽屉时的位置，意思很明确，不注释了
 */
export enum PlacementType {
  LEFT = 'left',
  RIGHT = 'right',
  TOP = 'top',
  BOTTOM = 'bottom',
}

/**
 * 打开子页面的方式
 */
export enum SubpageOpenType {
  /**
   * 弹窗打开
   */
  MODAL = 'modal',
  /**
   * 抽屉打开
   */
  DRAWER = 'drawer',
}

/**
 * 按钮中图标位置
 */
export enum ButtonIconPositionType {
  /**
   * 图标在文字前
   */
  BEFORE = 'before',
  /**
   * 图标在文字后
   */
  AFTER = 'after',
}

/**
 * 按钮align类型
 */
export enum ButtonAlignType {
  LEFT = 'left',
  CENTER = 'center',
  RIGHT = 'right',
}

/**
 * 按钮样式模式, 对应antd的按钮type
 */
export enum ButtonStyleMode {
  PRIMARY = 'primary',
  DEFAULT = 'default',
  DASHED = 'dashed',
  TEXT = 'text',
  LINK = 'link',
}

/**
 * 按钮大小，对应antd的按钮size
 */
export enum ButtonSizeType {
  LARGE = 'large',
  SMALL = 'small',
  DEFAULT = 'default',
}

/**
 * 按钮作用模式
 */
export enum ButtonAttachMode {
  /**
   * 作用单行
   */
  ROW = 'row',
  /**
   * 作用整个表格/表单
   */
  ALL = 'all',
}

/**
 * 按钮形状
 */
export enum ButtonShapeType {
  CIRCLE = 'circle',
  ROUND = 'round',
}

/**
 * operation类型
 * FIXME: 类型并不完整，本期只做两个，后续全部转换时再补足和修改
 * NOTE: 已按最新文档更新
 */
export enum OperationType {
  /**
   * 前端导出
   */
  FRONT_EXPORT = 'table-export',
  /**
   * 数据导出
   */
  DATA_EXPORT = 'data_export',
  /**
   * 文件上传、导入
   */
  UPLOAD_FILE = 'upload_file',
  /**
   * 下载模板
   */
  DOWNLOAD_TEMPLATE = 'download_template',
  /**
   * 图纸下载
   */
  DRAWINGS_DOWNLOAD = 'drawings-download',
  /**
   * 批量指定交期
   */
  BATCH_SET_DATE = 'batch-set-date',
  /**
   * 自动更换
   */
  OPERATE_SCRIPT = 'script',
  /**
   * 手动拆行
   */
  SPLIT_ROW = 'split-row',
  /**
   * 自动拆分
   */
  AUTO_SPLIT_ROW = 'auto-split-row',
  /**
   * 新增行
   */
  ADD_ROW = 'add-row',
  /**
   * 删除行
   */
  DELETE_ROW = 'delete-row',
  /**
   * 批量删除
   */
  BATCH_DELETE = 'batch-delete',
  /**
   * 复制行
   */
  COPY = 'copy',
  /**
   * 双档新增，表格外
   */
  OPENPAGE = 'openpage',
  /**
   * 双档的维护，行内
   */
  EDIT = 'edit',
  /**
   * 双档的复制，行内
   */
  OPENPAGE_COPY = 'openpage_copy',
}
