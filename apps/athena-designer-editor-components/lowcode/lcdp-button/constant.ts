import {
  ButtonType,
  Button<PERSON>ate<PERSON>y,
  ButtonStyleMode,
  ButtonSizeType,
  ButtonApiType,
  ButtonActionType,
  ActionParamType,
  ButtonConditionTriggerType,
  SubpageOpenType,
} from './enum';

import type {
  IBusinessButtonInfo,
  IOperationConfigInfo,
  ISubmitButtonInfo,
} from '../../../athena-designer-editor/src/plugins/plugin-ath-setter/components/Button/buttonCoreTypes';

export const ButtonApiTypeList = [...Object.entries(ButtonApiType)].map(([label, value]) => {
  return {
    label,
    value,
  };
});

/**
 * 全量提交按钮类型集合
 */
export const SubmitButtonTypeSet: Set<ButtonType> = new Set([
  ButtonType.BUTTON_DATA_DELETE,
  ButtonType.BUTTON_DELETE_TO_RECYCLE,
  ButtonType.BUTTON_RECYCLE_DELETE,
  ButtonType.BUTTON_DATA_DELETEALL,
  ButtonType.BUTTON_DATA_SAVE,
  ButtonType.BUTTON_DATA_COMBINE_SAVE,
  ButtonType.BUTTON_DATA_UPDATE,
  ButtonType.BUTTON_DATA_INVALID,
  ButtonType.BUTTON_DATA_VALID,
  ButtonType.BUTTON_DATA_RESET_VALID,
  ButtonType.BUTTON_DATA_CANCEL_VALID,
  ButtonType.BUTTON_DATA_RECOVER,
  ButtonType.BUTTON_WORKFLOW_INVOKE,
  ButtonType.BUTTON_WORKFLOW_ABORT,
  ButtonType.BUTTON_AGREE,
  ButtonType.BUTTON_DISAGREE,
  ButtonType.BUTTON_ACT_RETURN,
  ButtonType.BUTTON_ACT_ADD,
  ButtonType.BUTTON_WORKFLOW_SUBMIT,
  ButtonType.BUTTON_DATA_TERMINATE,
  ButtonType.BUTTON_REPORT_QUERY,
  ButtonType.BUTTON_TASK_REEXECUTE,
  ButtonType.BUTTON_TASK_TERMINATE,
  ButtonType.BUTTON_TASK_REAPPROVE,
  ButtonType.BUTTON_TASK_ADD,
  ButtonType.BUTTON_TASK_AGREE,
  // ButtonType.BUTTON_SUB_PAGE_SAVE,
]);

/**
 * 功能按钮类型集合
 */
export const FunctionButtonTypeSet: Set<ButtonType> = new Set([
  ButtonType.BUTTON_SUBMIT_ACTION_DECOUPLE,
  ButtonType.BUTTON_COMBINE_SAVE_DECOUPLE,
  ButtonType.BUTTON_OPENPAGE_ADD_DECOUPLE,
  ButtonType.BUTTON_DATA_DELETE_DECOUPLE
])

/**
 * 通用按钮, 目前只有一个，但是统一写法
 */
export const CommonButtonTypeSet: Set<ButtonType> = new Set([ButtonType.BUTTON_DECOUPLE]);

/**
 * 单档多栏 mode===row时支持的特殊operation类型集合
 */
export const SingleDocumentOperationRowButtonTypeSet: Set<ButtonType> = new Set([
  /**
   * 单档多栏复制
   */
  ButtonType.BUTTON_COPY_ITEM,
]);

/**
 * 单双多档 mode===row时支持的特殊operation类型集合
 */
export const NotSingleDocumentOperationRowButtonTypeSet: Set<ButtonType> = new Set([
  /**
   * 单双多档维护
   */
  ButtonType.BUTTON_OPENPAGE_EDIT,
  /**
   * 单双多档复制
   */
  ButtonType.BUTTON_OPENPAGE_COPY,
]);

/**
 * 单档多栏 mode===all时支持的特殊operation类型集合
 */
export const SingleDocumentOperationAllButtonTypeSet: Set<ButtonType> = new Set([
  /**
   * 单档多栏新增行
   */
  ButtonType.BUTTON_ADD_ITEM,
]);

/**
 * 单双多档 mode===all时支持的特殊operation类型集合
 */
export const NotSingleDocumentOperationAllButtonTypeSet: Set<ButtonType> = new Set([
  /**
   * 单双多档新增行
   */
  ButtonType.BUTTON_OPENPAGE_ADD,
]);

/**
 * mode===row场景支持的按钮基础集合
 */
export const OperationRowBaseButtonTypeSet: Set<ButtonType> = new Set([
  ButtonType.BUTTON_PRINT,
  ButtonType.BUTTON_DELETE_ITEM,
]);

/**
 * mode===all场景支持的按钮基础集合
 */
export const OperationAllBaseButtonTypeSet: Set<ButtonType> = new Set([
  ButtonType.BUTTON_PRINT,
  ButtonType.BUTTON_BATCH_DELETE_ITEM,
  ButtonType.BUTTON_UPLOAD_FILE,
  ButtonType.BUTTON_DOWNLOAD_TEMPLATE,
  ButtonType.BUTTON_DRAWINGS_DOWNLOAD,
  ButtonType.BUTTON_FRONT_EXPORT,
  ButtonType.BUTTON_BACKEND_EXPORT,
  ButtonType.BUTTON_BATCH_SET_DATE,
  ButtonType.BUTTON_OPERATE_SCRIPT,
  ButtonType.BUTTON_SPLIT_ROW,
  ButtonType.BUTTON_AUTO_SPLIT_ROW,
  ButtonType.BUTTON_IMPORT,
]);

/**
 * 行operation集合定义
 * WARN: 原OperationButtonTypeSet拆分成OperationRowButtonTypeSet和OperationAllButtonTypeSet。为了定义更明确
 */
export const OperationRowButtonTypeSet: Set<ButtonType> = new Set([
  ...OperationRowBaseButtonTypeSet,
  ...SingleDocumentOperationRowButtonTypeSet,
  ...NotSingleDocumentOperationRowButtonTypeSet,
]);

/**
 * 整单operation集合定义
 */
export const OperationAllButtonTypeSet: Set<ButtonType> = new Set([
  ...OperationAllBaseButtonTypeSet,
  ...SingleDocumentOperationAllButtonTypeSet,
  ...NotSingleDocumentOperationAllButtonTypeSet,
]);

/**
 * operation按钮全量集合
 */
export const OperationButtonTypeSet: Set<ButtonType> = new Set([
  ...OperationRowButtonTypeSet,
  ...OperationAllButtonTypeSet,
]);

/**
 * 全量功能按钮集合
 */
export const BusinessButtonTypeSet: Set<ButtonType> = new Set([
  ButtonType.BUTTON,
  /**
   * 2025-02-12 PO要求暂时隐藏打印按钮
   * 2025-03-11 新需求开始转换operation&toolabr为动态按钮，operation中的打印沟通后确认复用之前的动态打印按钮
   */
  ButtonType.BUTTON_PRINT,
  ButtonType.BUTTON_SUB_PAGE,
  ...OperationButtonTypeSet,
]);

/**
 * 按钮分类对应的全量可用按钮类型Map定义
 */
export const ButtonTypeMap: Map<ButtonCategory, Set<ButtonType>> = new Map([
  /**
   * 2025-03-11 common按钮回到功能按钮集合中,不再作为一个独立的类目
   */
  // [ButtonCategory.COMMON, CommonButtonTypeSet],
  [ButtonCategory.BUSINESS_BUTTON, BusinessButtonTypeSet],
  [ButtonCategory.SUBMIT_BUTTON, SubmitButtonTypeSet],
  [ButtonCategory.FUNCTION_BUTTON, FunctionButtonTypeSet]
]);

/**
 * BUTTON
 * 功能按钮默认使用的schema
 */
export const DefaultBusinessButtonSchema: IBusinessButtonInfo = {
  id: '',
  type: ButtonType.BUTTON,
  title: '通用',
  lang: {
    title: {
      zh_CN: '通用',
      zh_TW: '通用',
      en_US: 'Universal',
    },
  },
  styleMode: ButtonStyleMode.DEFAULT,
  size: ButtonSizeType.LARGE,
  disabled: false,
  ghost: false,
  danger: false,
  block: false,
  debounce: false,
  debounceTime: 300,
};

/**
 * 子页面按钮
 */
export const DefaultSubPageButtonSchema: IBusinessButtonInfo = {
  ...DefaultBusinessButtonSchema,
  type: ButtonType.BUTTON_SUB_PAGE,
  title: '打开子页面',
  lang: {
    title: {
      zh_CN: '打开子页面',
      zh_TW: '開啟子頁面',
      en_US: 'Open Subpage',
    },
  },
  subpageConfig: {
    target: SubpageOpenType.MODAL,
    optional: {
      width: '600px',
      height: 'auto',
    },
  },
};

/**
 * 子页面保存并关闭按钮
 */
// export const DefaultSubPageSaveButtonSchema: ISubmitButtonInfo = {
//   ...DefaultBusinessButtonSchema,
//   type: ButtonType.BUTTON_SUB_PAGE_SAVE,
//   title: '子页面保存并关闭',
//   lang: {
//     title: {
//       zh_CN: '子页面保存并关闭',
//       zh_TW: '子页面保存并关闭',
//       en_US: 'Subpage save and close',
//     },
//   },
//   styleMode: ButtonStyleMode.PRIMARY,
//   action: {
//     type: ButtonApiType.ESP,
//     actionType: ButtonActionType.SAVE,
//     actionId: 'esp_dpbas.xc.xmbb.update',
//     actionParams: [
//       {
//         type: ActionParamType.GET_ACTION_RESPONSE,
//       },
//     ],
//     trackCode: 'SUBMIT',
//     submitType: {
//       isBatch: false,
//       schema: '',
//     },
//   },
// };

/**
 * 打印按钮
 * WARN: 打印按钮既有动态按钮也有operation，但是结构更像action，所以使用Submit的定义
 * WARN: 打印的attachMode比较特殊，它在行，整表，表格外都可以使用，所以需要在运行时手动处理这个值，这边就不加默认值了
 */
export const DefaultPrintTemplateButtonSchema: ISubmitButtonInfo & IOperationConfigInfo = {
  ...DefaultBusinessButtonSchema,
  type: ButtonType.BUTTON_PRINT,
  styleMode: ButtonStyleMode.PRIMARY,
  title: '打印',
  description: '打印',
  lang: {
    title: {
      zh_CN: '打印',
      zh_TW: '列印',
      en_US: 'Print',
    },
    description: {
      zh_CN: '打印',
      zh_TW: '列印',
      en_US: 'Print',
    },
  },
};

/**
 * 一般提交按钮schema, 需要手动覆盖type, title, lang, 以及其它需要变化的属性
 */
export const DefaultBaseSubmitButtonSchema: ISubmitButtonInfo = {
  ...DefaultBusinessButtonSchema,
  action: {
    submitType: {
      /**
       * 所以提交按钮的这个字段由设置器初始化时获取当前fieldTree的根节点data_name赛到这里
       */
      schema: '',
      isBatch: false,
    },
    trackCode: 'SUBMIT',
  },
  condition: {
    relateValidators: false,
    unchangedForbidClick: false,
  },
};

/**
 * BUTTON_DATA_COMBINE_SAVE
 * 组合保存按钮
 */
export const DefaultCombineSaveButtonSchema: ISubmitButtonInfo = {
  ...DefaultBaseSubmitButtonSchema,
  type: ButtonType.BUTTON_DATA_COMBINE_SAVE,
  styleMode: ButtonStyleMode.PRIMARY,
  title: '保存',
  lang: {
    title: {
      zh_CN: '保存',
      zh_TW: '存檔',
      en_US: 'Save',
    },
  },
  action: {
    type: ButtonApiType.COMBINE,
    actionType: ButtonActionType.COMBINE,
    combineAttachActions: [
      {
        type: ButtonApiType.ESP,
        actionType: ButtonActionType.SAVE,
        title: '新建',
        lang: {
          title: {
            zh_CN: '新建',
            zh_TW: '新建',
            en_US: 'Create',
          },
        },
      },
      {
        type: ButtonApiType.ESP,
        actionType: ButtonActionType.UPDATE,
        title: '保存',
        lang: {
          title: {
            zh_CN: '保存',
            zh_TW: '存檔',
            en_US: 'Save',
          },
        },
      },
    ],
  },
};

/**
 * BUTTON_DATA_DELETE
 * 删除按钮
 */
export const DefaultDataDeleteButtonSchema: ISubmitButtonInfo = {
  ...DefaultBaseSubmitButtonSchema,
  title: '删除',
  type: ButtonType.BUTTON_DATA_DELETE,
  lang: {
    title: {
      zh_CN: '删除',
      zh_TW: '删除',
      en_US: 'Delete',
    },
  },
  action: {
    type: ButtonApiType.ESP,
    actionType: ButtonActionType.DELETE,
  },
};

/**
 * BUTTON_DATA_INVALID
 * 失效
 */
export const DefaultInvalidButtonSchema: ISubmitButtonInfo = {
  ...DefaultBaseSubmitButtonSchema,
  title: '失效',
  type: ButtonType.BUTTON_DATA_INVALID,
  lang: {
    title: {
      zh_TW: '失效',
      en_US: 'Invalid',
      zh_CN: '失效',
    },
  },
  action: {
    ...DefaultBaseSubmitButtonSchema.action,
    type: ButtonApiType.ESP,
    actionType: ButtonActionType.INVALID,
  },
};

/**
 * BUTTON_DATA_VALID
 * 生效
 */
export const DefaultValidButtonSchema: ISubmitButtonInfo = {
  ...DefaultBaseSubmitButtonSchema,
  title: '生效',
  type: ButtonType.BUTTON_DATA_VALID,
  lang: {
    title: {
      zh_TW: '生效',
      en_US: 'Valid',
      zh_CN: '生效',
    },
  },
  action: {
    ...DefaultBaseSubmitButtonSchema.action,
    type: ButtonApiType.ESP,
    actionType: ButtonActionType.VALID,
  },
};

/**
 * BUTTON_DATA_CANCEL_VALID
 * 取消生效
 */
export const DefaultCancelValidButtonSchema: ISubmitButtonInfo = {
  ...DefaultBaseSubmitButtonSchema,
  title: '取消生效',
  type: ButtonType.BUTTON_DATA_CANCEL_VALID,
  lang: {
    title: {
      zh_TW: '取消生效',
      en_US: 'Cancel Valid',
      zh_CN: '取消生效',
    },
  },
  action: {
    ...DefaultBaseSubmitButtonSchema.action,
    type: ButtonApiType.ESP,
    actionType: ButtonActionType.CANCEL_VALID,
  },
};

/**
 * BUTTON_DATA_RESET_VALID
 * 重新生效
 */
export const DefaultResetValidButtonSchema: ISubmitButtonInfo = {
  ...DefaultBaseSubmitButtonSchema,
  title: '重新生效',
  type: ButtonType.BUTTON_DATA_RESET_VALID,
  lang: {
    title: {
      zh_TW: '重新生效',
      en_US: 'Revalidate',
      zh_CN: '重新生效',
    },
  },
  action: {
    ...DefaultBaseSubmitButtonSchema.action,
    type: ButtonApiType.ESP,
    actionType: ButtonActionType.RESET_VALID,
  },
};

/**
 * BUTTON_DELETE_TO_RECYCLE
 * 删除到回收站
 */
export const DefaultDeleteToRecycleButtonSchema: ISubmitButtonInfo = {
  ...DefaultBaseSubmitButtonSchema,
  title: '删除到回收站',
  type: ButtonType.BUTTON_DELETE_TO_RECYCLE,
  lang: {
    title: {
      zh_TW: '删除到回收站',
      zh_CN: '删除到回收站',
      en_US: 'Delete to recycle',
    },
  },
  action: {
    type: ButtonApiType.ESP,
    actionType: ButtonActionType.DELETE,
    dispatchBPM: false,
    needProxyToken: false,
    combineAttachActions: [
      {
        type: ButtonApiType.RECYCLE,
        actionId: 'recycle.save',
        title: '删除回收站数据',
        lang: {
          title: {
            zh_TW: '刪除回收站數據',
            en_US: 'Delete Recycle Bin data',
            zh_CN: '删除回收站数据',
          },
        },
      },
    ],
  },
};

/**
 * BUTTON_DATA_RECOVER
 * 还原按钮
 */
export const DefaultRecoverButtonSchema: ISubmitButtonInfo = {
  ...DefaultBaseSubmitButtonSchema,
  title: '还原',
  type: ButtonType.BUTTON_DATA_RECOVER,
  lang: {
    title: {
      zh_TW: '還原',
      en_US: 'Recycle',
      zh_CN: '还原',
    },
  },
  styleMode: ButtonStyleMode.PRIMARY,
  action: {
    type: ButtonApiType.ESP,
    actionType: ButtonActionType.RECOVER,
    dispatchBPM: false,
    needProxyToken: false,
    extendParas: {
      applyToRecycle: false,
    },
    combineAttachActions: [
      {
        type: ButtonApiType.RECYCLE,
        actionId: 'recycle.delete',
        title: '删除回收站数据',
        lang: {
          title: {
            zh_TW: '刪除回收站數據',
            en_US: 'Delete Recycle Bin data',
            zh_CN: '删除回收站数据',
          },
        },
      },
    ],
  },
};

/**
 * BUTTON_RECYCLE_DELETE
 * 彻底删除按钮
 */
export const DefaultDeleteCompleteButtonSchema: ISubmitButtonInfo = {
  ...DefaultBaseSubmitButtonSchema,
  title: '彻底删除',
  type: ButtonType.BUTTON_RECYCLE_DELETE,
  lang: {
    title: {
      zh_TW: '徹底刪除',
      en_US: 'Delete completely',
      zh_CN: '彻底删除',
    },
  },
  styleMode: ButtonStyleMode.PRIMARY,
  action: {
    type: ButtonApiType.RECYCLE,
    actionType: ButtonActionType.DELETE,
    actionId: 'recycle.delete',
    dispatchBPM: false,
    needProxyToken: false,
    extendParas: {
      applyToRecycle: true,
    },
  },
};

/**
 * BUTTON_WORKFLOW_ABORT
 * 撤审按钮
 */
export const DefaultAbortButtonSchema: ISubmitButtonInfo = {
  ...DefaultBaseSubmitButtonSchema,
  title: '撤审',
  type: ButtonType.BUTTON_WORKFLOW_ABORT,
  lang: {
    title: {
      zh_TW: '撤审',
      en_US: 'processabort',
      zh_CN: '撤审',
    },
  },
  condition: {
    script: "data?.manage_status=='I'",
    trigger: [ButtonConditionTriggerType.INIT, ButtonConditionTriggerType.DATA_CHANGED],
  },
  hiddenConfig: {
    script: "data?.manage_status!='I' && data?.manage_status!='Y'",
    trigger: [ButtonConditionTriggerType.INIT, ButtonConditionTriggerType.DATA_CHANGED],
  },
  action: {
    type: ButtonApiType.WORKFLOW,
    actionId: 'athena.workflow.process.abort',
    serviceName: 'workflow-abort',
    submitType: {
      schema: '',
      isBatch: false,
      submitAll: true,
    },
    combineAttachActions: [
      {
        title: '业务数据更新',
        type: ButtonApiType.ESP,
        lang: {
          title: {
            zh_CN: '业务数据更新',
            zh_TW: '業務數據更新',
            en_US: 'Business data update',
          },
        },
        paras: {
          API入参name: [
            {
              manage_status: 'N',
            },
          ],
        },
        actionParams: [
          {
            name: '#@tableName@.serial_number',
            type: ActionParamType.GET_ACTION_RESPONSE,
            value: 'serialNumber',
          },
        ],
      },
    ],
    extendParas: {
      ignoreDataChange: true,
    },
  },
};

/**
 * BUTTON_WORKFLOW_INVOKE
 * 送审按钮
 */
export const DefaultInvokeButtonSchema: ISubmitButtonInfo = {
  ...DefaultBaseSubmitButtonSchema,
  title: '送审',
  type: ButtonType.BUTTON_WORKFLOW_INVOKE,
  lang: {
    title: {
      zh_CN: '送审',
      zh_TW: '送审',
      en_US: 'processinvoke',
    },
  },
  hiddenConfig: {
    script: "data?.manage_status!='N'",
    trigger: [ButtonConditionTriggerType.INIT, ButtonConditionTriggerType.DATA_CHANGED],
  },
  action: {
    type: ButtonApiType.WORKFLOW,
    actionId: 'athena.workflow.process.invoke',
    serviceName: 'workflow-invoke',
    submitType: {
      schema: '',
      isBatch: false,
      submitAll: true,
    },
    combineAttachActions: [
      {
        type: ButtonApiType.ESP,
        title: '送审后更新流程序号',
        lang: {
          title: {
            zh_CN: '送审后更新流程序号',
            zh_TW: '送審後更新流程程式編號',
            en_US: 'Update the flow program number after submission for review',
          },
        },
        paras: {
          API入参name: [
            {
              manage_status: 'I',
            },
          ],
        },
        actionParams: [
          {
            name: '#@tableName@.serial_number',
            type: ActionParamType.GET_ACTION_RESPONSE,
            value: '#@tableName@.serialNumber',
          },
        ],
      },
    ],
    extendParas: {
      ignoreDataChange: true,
    },
    actionParams: [
      {
        type: ActionParamType.GET_ACTION_RESPONSE,
      },
    ],
  },
};

/**
 * BUTTON_AGREE
 * 同意按钮, 任务卡，人工签核;
 */
export const DefaultAgreeButtonSchema: ISubmitButtonInfo = {
  ...DefaultBaseSubmitButtonSchema,
  title: '同意',
  type: ButtonType.BUTTON_AGREE,
  lang: {
    title: {
      zh_TW: '同意',
      en_US: 'Agree',
      zh_CN: '同意',
    },
  },
  styleMode: ButtonStyleMode.PRIMARY,
  action: {
    type: ButtonApiType.ESP,
    submitType: {
      schema: '',
      isBatch: false,
      submitAll: true,
    },
    actionParams: [
      {
        type: ActionParamType.GET_ACTION_RESPONSE,
      },
    ],
    combineAttachActions: [
      {
        type: ButtonApiType.WORKFLOW,
        serviceName: 'workflow-act-agree',
        actionId: 'athena.workflow.process.activity.work.agree',
        title: '同意',
        lang: {
          title: {
            zh_TW: '同意',
            en_US: 'Agree',
            zh_CN: '同意',
          },
        },
        actionParams: [
          {
            type: ActionParamType.GET_ACTION_RESPONSE,
          },
        ],
      },
    ],
  },
};

/**
 * BUTTON_AGREE
 * 同意按钮，签核任务
 */
export const DefaultTaskDgreeButtonSchema: ISubmitButtonInfo = {
  ...DefaultBaseSubmitButtonSchema,
  title: '同意',
  type: ButtonType.BUTTON_TASK_AGREE,
  styleMode: ButtonStyleMode.PRIMARY,
  lang: {
    title: {
      zh_TW: '同意',
      en_US: 'Agree',
      zh_CN: '同意',
    },
  },
  action: {
    type: ButtonApiType.ESP,
    submitType: {
      schema: '',
      isBatch: false,
      submitAll: true,
    },
    combineAttachActions: [
      {
        type: ButtonApiType.TASK_ENGINE,
        serviceName: 'agree',
        actionId: 'agree',
        title: '同意',
        lang: {
          title: {
            zh_TW: '同意',
            en_US: 'Agree',
            zh_CN: '同意',
          },
        },
      },
    ],
  },
};

/**
 * BUTTON_DISAGREE
 * 不同意按钮, 任务卡，人工签核
 */
export const DefaultDisAgreeButtonSchema: ISubmitButtonInfo = {
  ...DefaultBaseSubmitButtonSchema,
  title: '不同意',
  type: ButtonType.BUTTON_DISAGREE,
  lang: {
    title: {
      zh_TW: '不同意',
      en_US: 'disagree',
      zh_CN: '不同意',
    },
  },
  action: {
    type: ButtonApiType.ESP,
    submitType: {
      schema: '',
      isBatch: false,
      submitAll: true,
    },
    actionParams: [
      {
        type: ActionParamType.GET_ACTION_RESPONSE,
      },
    ],
    combineAttachActions: [
      {
        type: ButtonApiType.WORKFLOW,
        title: '不同意',
        lang: {
          title: {
            zh_TW: '不同意',
            en_US: 'disagree',
            zh_CN: '不同意',
          },
        },
        actionId: 'athena.workflow.process.activity.work.disagree',
        serviceName: 'workflow-act-disagree',
        actionParams: [
          {
            type: ActionParamType.GET_ACTION_RESPONSE,
          },
        ],
      },
    ],
  },
};

/**
 * BUTTON_ACT_RETURN
 * 退回按钮, 任务卡，人工签核
 */
export const DefaultActReturnButtonSchema: ISubmitButtonInfo = {
  ...DefaultBaseSubmitButtonSchema,
  title: '退回',
  type: ButtonType.BUTTON_ACT_RETURN,
  lang: {
    title: {
      zh_TW: '退回',
      en_US: 'return',
      zh_CN: '退回',
    },
  },
  action: {
    type: ButtonApiType.WORKFLOW,
    actionId: 'athena.workflow.process.activity.return',
    serviceName: 'workflow-act-return',
    terminateProcess: false,
    actionParams: [
      {
        type: ActionParamType.GET_ACTION_RESPONSE,
      },
    ],
  },
};

/**
 * BUTTON_TASK_REEXECUTE
 * 签核任务：退回重办
 */
export const DefaultTaskReexecuteButtonSchema: ISubmitButtonInfo = {
  ...DefaultBaseSubmitButtonSchema,
  title: '退回重办',
  type: ButtonType.BUTTON_TASK_REEXECUTE,
  lang: {
    title: {
      zh_TW: '退回重辦',
      en_US: 'reexecute',
      zh_CN: '退回重办',
    },
  },
  action: {
    type: ButtonApiType.ESP,
    submitType: {
      schema: '',
      isBatch: false,
      submitAll: true,
    },
    dispatch: true,
    combineAttachActions: [
      {
        title: '退回',
        type: ButtonApiType.TASK_ENGINE,
        actionId: 'reexecute',
        serviceName: 'reexecute',
        lang: {
          title: {
            zh_TW: '退回',
            en_US: 'reexecute',
            zh_CN: '退回',
          },
        },
      },
    ],
  },
};

/**
 * BUTTON_ACT_ADD
 * 加签, 任务卡，人工签核
 */
export const DefaultActAddButtonSchema: ISubmitButtonInfo = {
  ...DefaultBaseSubmitButtonSchema,
  title: '加签',
  type: ButtonType.BUTTON_ACT_ADD,
  lang: {
    title: {
      zh_TW: '加簽',
      en_US: 'add',
      zh_CN: '加签',
    },
  },
  action: {
    type: ButtonApiType.WORKFLOW,
    actionId: 'athena.workflow.process.activity.add',
    serviceName: 'workflow-act-add',
    terminateProcess: false,
    actionParams: [
      {
        type: ActionParamType.GET_ACTION_RESPONSE,
      },
    ],
  },
};

/**
 * BUTTON_DATA_UPDATE
 * 更新保存按钮, 任务卡，人工关卡
 */
export const DefaultUpdateButtonSchema: ISubmitButtonInfo = {
  ...DefaultBaseSubmitButtonSchema,
  title: '保存',
  type: ButtonType.BUTTON_DATA_UPDATE,
  lang: {
    title: {
      zh_TW: '存檔',
      en_US: 'Save',
      zh_CN: '保存',
    },
  },
  styleMode: ButtonStyleMode.PRIMARY,
  action: {
    type: ButtonApiType.ESP,
    actionId: 'esp_dpbas.item.data.update',
    serviceName: 'dpbas.item.data.update',
    actionParams: [
      {
        type: ActionParamType.GET_ACTION_RESPONSE,
      },
    ],
  },
};

/**
 * BUTTON_WORKFLOW_SUBMIT
 * 提交按钮, 任务卡，人工关卡
 */
export const DefaulttSubmitButtonSchema: ISubmitButtonInfo = {
  ...DefaultBaseSubmitButtonSchema,
  title: '提交',
  type: ButtonType.BUTTON_WORKFLOW_SUBMIT,
  lang: {
    title: {
      zh_TW: '提交',
      en_US: 'submit',
      zh_CN: '提交',
    },
  },
  action: {
    type: ButtonApiType.WORKFLOW,
    actionId: 'athena.workflow.process.activity.work.submit',
    serviceName: 'workflow-act-submit',
    terminateProcess: false,
    actionParams: [
      {
        type: ActionParamType.GET_ACTION_RESPONSE,
      },
    ],
  },
};

/**
 * BUTTON_DATA_TERMINATE
 * 一般任务：终止按钮
 */
export const DefaultDataTerminateButtonSchema: ISubmitButtonInfo = {
  ...DefaultBaseSubmitButtonSchema,
  title: '终止',
  type: ButtonType.BUTTON_DATA_TERMINATE,
  lang: {
    title: {
      zh_TW: '終止',
      en_US: 'Termination',
      zh_CN: '终止',
    },
  },
  confirm: {
    enable: true,
    content: '是否确认终止',
    lang: {
      content: {
        zh_TW: '是否確認終止',
        en_US: 'Is termination confirmed',
        zh_CN: '是否确认终止',
      },
    },
  },
  action: {
    type: ButtonApiType.ESP,
    dispatch: true,
    combineAttachActions: [
      {
        title: '终止任务的数据',
        type: ButtonApiType.UIBOT,
        actionId: 'terminate-data',
        lang: {
          title: {
            zh_TW: '終止任務的數據',
            en_US: 'terminate data',
            zh_CN: '终止任务的数据',
          },
        },
      },
    ],
  },
};

/**
 * BUTTON_REPORT_QUERY
 * 报表查询按钮
 */
export const DefaultReportQueryButtonSchema: ISubmitButtonInfo = {
  ...DefaultBaseSubmitButtonSchema,
  title: '查询',
  type: ButtonType.BUTTON_REPORT_QUERY,
  styleMode: ButtonStyleMode.PRIMARY,
  lang: {
    title: {
      zh_TW: '查詢',
      en_US: 'Search',
      zh_CN: '查询',
    },
  },
  action: {
    type: ButtonApiType.ESP,
    /**
     * 同schema逻辑, 需要运行时挂载
     */
    actionId: '',
    /**
     * 同schema逻辑, 需要运行时挂载
     */
    serviceName: '',
  },
};

/**
 * BUTTON_TASK_TERMINATE
 * 签核任务：终止任务
 */
export const DefaultTaskTerminateButtonSchema: ISubmitButtonInfo = {
  ...DefaultBaseSubmitButtonSchema,
  title: '终止任务',
  type: ButtonType.BUTTON_TASK_TERMINATE,
  lang: {
    title: {
      zh_TW: '終止任务',
      en_US: 'terminate task',
      zh_CN: '终止任务',
    },
  },
  confirm: {
    enable: true,
    title: '提示',
    content: '是否确定终止？',
    lang: {
      title: {
        zh_TW: '提示',
        en_US: 'prompt',
        zh_CN: '提示',
      },
      content: {
        zh_TW: '是否確定終止？',
        zh_CN: '是否确定终止？',
        en_US: 'Confirm terminate?',
      },
    },
  },
  action: {
    type: ButtonApiType.ESP,
    submitType: {
      schema: '',
      isBatch: false,
      submitAll: true,
    },
    combineAttachActions: [
      {
        type: ButtonApiType.TASK_ENGINE,
        title: '终止任务',
        actionId: 'terminate-task',
        serviceName: 'terminate-task',
        lang: {
          title: {
            zh_TW: '終止任务',
            en_US: 'terminate task',
            zh_CN: '终止任务',
          },
        },
      },
    ],
  },
};

/**
 * BUTTON_TASK_REAPPROVE
 * 签核任务：退回重签
 */
export const DefaultTaskReapproveButtonSchema: ISubmitButtonInfo = {
  ...DefaultBaseSubmitButtonSchema,
  type: ButtonType.BUTTON_TASK_REAPPROVE,
  title: '退回重签',
  lang: {
    title: {
      zh_TW: '退回重簽',
      en_US: 'ReApprove',
      zh_CN: '退回重签',
    },
  },
  action: {
    type: ButtonApiType.TASK_ENGINE,
    actionId: 'athena_bpm_reapprove',
    serviceName: 'reapprove',
  },
};

/**
 * BUTTON_TASK_ADD
 * 签核任务：加签
 */
export const DefaultTaskAddButtonSchema: ISubmitButtonInfo = {
  ...DefaultBaseSubmitButtonSchema,
  title: '加签',
  type: ButtonType.BUTTON_TASK_ADD,
  lang: {
    title: {
      zh_TW: '加簽',
      en_US: 'AddApprove',
      zh_CN: '加签',
    },
  },
  action: {
    type: ButtonApiType.TASK_ENGINE,
    actionId: 'athena_bpm_add_task',
    serviceName: 'add-task',
  },
};

/**
 * 允许配置子action的按钮类型配置集合
 */
export const SupportSubActionsButtonTypeSet: Set<ButtonType> = new Set([
  ButtonType.BUTTON_DATA_COMBINE_SAVE,
  ButtonType.BUTTON_DELETE_TO_RECYCLE,
  ButtonType.BUTTON_DATA_RECOVER,
  ButtonType.BUTTON_WORKFLOW_ABORT,
  ButtonType.BUTTON_WORKFLOW_INVOKE,
  ButtonType.BUTTON_AGREE,
  ButtonType.BUTTON_TASK_AGREE,
  ButtonType.BUTTON_DISAGREE,
  ButtonType.BUTTON_TASK_REEXECUTE,
  ButtonType.BUTTON_DATA_TERMINATE,
  ButtonType.BUTTON_TASK_TERMINATE,
  ButtonType.BUTTON_REPORT_QUERY,
]);

/**
 * 动态按钮类型匹配的默认权限类型
 */
export const DynamicButtonTypeToPermissionTypeMap: Map<string, string> = new Map([
  /**
   * 提交按钮
   */
  [ButtonType.BUTTON_DATA_DELETE, 'basic-data-delete'],
  [ButtonType.BUTTON_DELETE_TO_RECYCLE, 'basic-data-delete-to-recycle'],
  [ButtonType.BUTTON_RECYCLE_DELETE, 'basic-data-recyle-delete'],
  [ButtonType.BUTTON_DATA_DELETEALL, 'basic-data-deleteAll'],
  [ButtonType.BUTTON_DATA_SAVE, 'basic-data-save'],
  [ButtonType.BUTTON_DATA_COMBINE_SAVE, 'basic-data-combine-save'],
  [ButtonType.BUTTON_DATA_UPDATE, 'basic-data-update'],
  [ButtonType.BUTTON_DATA_INVALID, 'invalid'],
  [ButtonType.BUTTON_DATA_VALID, 'valid'],
  [ButtonType.BUTTON_DATA_CANCEL_VALID, 'cancelvalid'],
  [ButtonType.BUTTON_DATA_RESET_VALID, 'revalid'],
  [ButtonType.BUTTON_DATA_RECOVER, 'basic-data-recover'],
  [ButtonType.BUTTON_WORKFLOW_INVOKE, 'workflow-invoke'],
  [ButtonType.BUTTON_WORKFLOW_ABORT, 'workflow-abort'],
  /**
   * operations
   */
  [ButtonType.BUTTON_PRINT, 'print'],
  [ButtonType.BUTTON_FRONT_EXPORT, 'table-export'],
  [ButtonType.BUTTON_BACKEND_EXPORT, 'data_export'],
  [ButtonType.BUTTON_DOWNLOAD_TEMPLATE, 'download_template'],
  [ButtonType.BUTTON_UPLOAD_FILE, 'upload_file'],
  [ButtonType.BUTTON_DRAWINGS_DOWNLOAD, 'drawings-download'],
  [ButtonType.BUTTON_BATCH_SET_DATE, 'batch-set-date'],
  [ButtonType.BUTTON_OPERATE_SCRIPT, 'script'],
  [ButtonType.BUTTON_SPLIT_ROW, 'split-row'],
  [ButtonType.BUTTON_AUTO_SPLIT_ROW, 'auto-split-row'],
  [ButtonType.BUTTON_ADD_ITEM, 'add-row'],
  [ButtonType.BUTTON_DELETE_ITEM, 'delete-row'],
  [ButtonType.BUTTON_BATCH_DELETE_ITEM, 'batch-delete'],
  [ButtonType.BUTTON_COPY_ITEM, 'copy'],
  [ButtonType.BUTTON_OPENPAGE_ADD, 'add'],
  [ButtonType.BUTTON_OPENPAGE_EDIT, 'edit'],
  [ButtonType.BUTTON_OPENPAGE_COPY, 'openpage_copy'],
  /**
   * toolbar
   */
  [ButtonType.BUTTON_TOOLBAR_EDIT, 'toolbar_edit'],
  [ButtonType.BUTTON_TOOLBAR_PREVIOUS, 'toolbar_previous'],
  [ButtonType.BUTTON_TOOLBAR_NEXT, 'toolbar_next'],
]);
