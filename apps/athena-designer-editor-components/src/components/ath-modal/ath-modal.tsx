import React, { createElement, useMemo, useState } from 'react';
import './index.scss';
import { getI18n } from '../../tools';
import { AthenaComponentType } from '../../../lowcode/common/common.config';
import {
  InfoCircleOutlined,
  WarningOutlined,
  CheckCircleTwoTone,
  CloseCircleOutlined,
  CloseOutlined,
} from '@ant-design/icons';
import { Tooltip } from 'antd';

export interface AthModalProps {
  children: any;
  dslInfo: any;
  _leaf: any;
}

const AthModal: React.FC<AthModalProps> = (props: AthModalProps) => {
  const { dslInfo, _leaf, children } = props;
  const { t, language } = getI18n(_leaf);
  // 分类 children
  const childInfo = useMemo(() => {
    const { slots, columnDefs } = (children || []).reduce(
      (acc, cur) => {
        const type = cur.props?.dslInfo?.type;
        if (
          type === AthenaComponentType.DYNAMIC_OPERATION ||
          type === AthenaComponentType.ICON ||
          type === AthenaComponentType.FOOTER_BUTTON_GROUP
        ) {
          acc.slots.push(cur);
        } else {
          acc.columnDefs.push(cur);
        }
        return acc;
      },
      { slots: [], columnDefs: [] },
    );

    const wrapperChildren = React.Children.map(columnDefs, (child, index) => {
      return React.createElement('div', { key: index, className: 'wrapped-child' }, child);
    });
    return { slots, wrapperChildren };
  }, [children]);
  const title = dslInfo?.lang?.title?.[language] ?? dslInfo?.title;
  const modalType = dslInfo?.modalType;
  const fontIcon = dslInfo?.fontIcon ?? false;
  const mask = dslInfo?.mask ?? true;
  // const maskClosable = dslInfo?.maskClosable ?? false;
  const isClose = dslInfo?.isClose ?? true;
  const isFooter = dslInfo?.isFooter ?? true;

  // const [visible, setVisible] = useState(true);
  // const handleMaskClick = () => {
  //   if (maskClosable) setVisible(false);
  // };
  // if (!visible) return null;
  const size = dslInfo?.size || 'medium';
  const sizeWidthMap = {
    small: 480,
    medium: 720,
    large: 960,
    xlarge: 1200,
  };
  const sizeHeightMap = {
    small: 240,
    medium: 360,
    large: 480,
    xlarge: 600,
  };
  const modalWidth = sizeWidthMap[size] || 720;
  const modalHeight = sizeHeightMap[size] || 360;
  const padding = dslInfo?.padding || '24px';

  const renderTitleIcon = () => {
    if (modalType !== 'customDsl') {
      if (modalType === 'confirm') {
        return <InfoCircleOutlined style={{ color: '#1677ff' }} />;
      }
      if (modalType === 'warning') {
        return <WarningOutlined style={{ color: '#ffc107' }} />;
      }
      if (modalType === 'success') {
        return <CheckCircleTwoTone style={{ color: '#4CAF50' }} />;
      }
      if (modalType === 'failure') {
        return <CloseCircleOutlined style={{ color: '#f44336' }} />;
      }
      return null;
    } else if (fontIcon) {
      // return <InfoCircleOutlined style={{ color: '#1677ff' }} />;
      return <div className="tool-bar">{[childInfo.slots[1]]}</div>;
    }
    return null;
  };

  return (
    <div className="ath-modal">
      {mask && <div className="ath-modal-mask" />}

      <div className="ath-modal-content" style={{ width: modalWidth, height: modalHeight }}>
        {/* 头部 */}
        {title && (
          <div className="title-wrapper">
            <div className="title-bar">
              {renderTitleIcon()}
              <Tooltip title={title}>
                <div style={{ marginLeft: renderTitleIcon() ? 8 : 0 }} className="titleFlex">
                  {title}
                </div>
              </Tooltip>
            </div>
            <div className="iconFlex">{isClose && <CloseOutlined />}</div>
          </div>
        )}
        {/* 内容区 */}
        <div className="content" style={{ padding }}>
          {dslInfo?.modalContent ? (
            <div className="feedback-content">
              {dslInfo?.lang?.modalContent?.[language] || dslInfo?.modalContent}
            </div>
          ) : !childInfo.wrapperChildren?.length ? (
            <div className="empty-form-list-container-placeholder lc-container-placeholder">
              {t('dj-拖拽组件或模板到这里')}
            </div>
          ) : (
            childInfo.wrapperChildren
          )}
        </div>
        {/* 底部栏 */}
        {isFooter && <div className="modal-footer">{childInfo.slots[0]}</div>}
      </div>
    </div>
  );
};

AthModal.displayName = 'AthModal';
export default AthModal;
