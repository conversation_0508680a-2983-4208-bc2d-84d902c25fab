import React, { useState } from 'react';
import './CustomOptions.less';
import { cloneDeep, isEmpty } from 'lodash';
import { DropDownVocabulary } from '@/components/DropDownVocabulary';
import { t } from 'i18next';
import { Popconfirm, Select } from 'antd';
import Icon from '@/components/Icon';
import { OptionModal } from './OptionModal';

interface CustomOptionsProps {
  dataConnectorId: string;
  dataConnectors: any[];
  options: any[];
  dataType: string;
  isPrivatization: boolean;
  change: (data: any) => void;
}

const CustomOptions = (props: CustomOptionsProps) => {
  // 选项编辑
  const [optionIndex, setOptionIndex] = useState<number>(-1);
  const [optionData, setOptionData] = useState<any>({});
  const [optionVisible, setOptionVisible] = useState<boolean>(false);

  /**
   * 选择枚举key
   * @param data
   */
  const handleChangeDataConnectorId = (e): void => {
    handleChange([], e);
  };

  /**
   * 编辑选项
   * @param index
   * @param option
   */
  const handleEdit = (index: number, option: any) => {
    setOptionIndex(index);
    setOptionData(option);
    setOptionVisible(true);
  };

  /**
   * 删除选项
   * @param index
   * @param option
   */
  const handleDelete = (index: number, option: any) => {
    const newOptions = cloneDeep(props.options);
    newOptions.splice(index, 1);
    handleChange(newOptions, null);
  };

  /**
   * 添加选项
   */
  const handleAdd = (): void => {
    setOptionIndex(-1);
    setOptionData({});
    setOptionVisible(true);
  };

  /**
   * 取消编辑选项
   */
  const handleOptionCancel = (): void => {
    setOptionIndex(-1);
    setOptionData({});
    setOptionVisible(false);
  };

  /**
   * 确认编辑选择
   */
  const handleOptionOk = (newData: any): void => {
    const newOptions = cloneDeep(props.options);
    if (optionIndex > -1) {
      newOptions[optionIndex] = newData;
    } else {
      newOptions.push(newData);
    }
    handleOptionCancel();
    handleChange(newOptions, null);
  };

  /**
   * 触发数据变更事件
   * @param options
   * @param dataConnectorId
   */
  const handleChange = (options: any, dataConnectorId: string): void => {
    props.change({ options, dataConnectorId });
  };

  const [draggableList, setDraggableList] = useState([]);
  const [draggedItem, setDraggedItem] = useState(null);

  const handleDragStart = (e, index) => {
    const items = props.options ?? [];
    setDraggableList(items);
    e.dataTransfer.setData('text/plain', JSON.stringify({ key: 'setting', value: items[index] }));
    setDraggedItem(items[index]);
    e.dataTransfer.effectAllowed = 'move';
    e.target.classList.add('dragging');
  };

  const handleDragEnd = (e) => {
    e.target.classList.remove('dragging');
    console.log('onDragOver', JSON.parse(e.dataTransfer.getData('text/plain') || '{}'));

    handleChange(draggableList, null);
    setDraggedItem(null);
    setDraggableList([]);
  };

  const handleDragOver = (e, index) => {
    e.preventDefault();
    console.log('onDragEnd', JSON.parse(e.dataTransfer.getData('text/plain') || '{}'));
    if (draggedItem === null) return;

    const draggedOverItem = draggableList[index];
    if (draggedItem === draggedOverItem) return;

    const newItems = [...draggableList];
    const draggedItemIndex = draggableList.indexOf(draggedItem);
    newItems.splice(draggedItemIndex, 1);
    newItems.splice(index, 0, draggedItem);
    setDraggableList(newItems);
  };

  return (
    <>
      <div className="select-content">
        <div className="setter-title">
          <span>{t('dj-配置选项')}</span>
          <span onClick={() => handleAdd()}>+&nbsp;{t('dj-添加')}</span>
        </div>
        <div className="select-input">
          <Select
            value={props.dataConnectorId}
            onChange={(e) => handleChangeDataConnectorId(e)}
            options={[
              {
                label: (
                  <span style={{ color: 'rgba(51, 51, 51, 0.45)', fontSize: '10px' }}>
                    {t('dj-数据请求')}
                  </span>
                ),
                title: t('dj-数据请求'),
                options: (props.dataConnectors ?? []).map(({ name }, index) => ({
                  key: index,
                  value: name,
                  label: name,
                })),
              },
            ]}
          />
        </div>
      </div>
      <div className="drop-list">
        {(props.options ?? []).map((option, i) => {
          return (
            <div
              className="drop-box"
              draggable
              onDragStart={(e) => handleDragStart(e, i)}
              onDragEnd={(e) => handleDragEnd(e)}
              onDragOver={(e) => handleDragOver(e, i)}
            >
              <div className="back-fields-info">{option?.lang?.title?.[t('dj-LANG')]}</div>
              <div className="drop-operate">
                <Icon
                  className="iconfont"
                  type="iconbianji1"
                  onClick={() => handleEdit(i, option)}
                />
                <Popconfirm
                  className="confirm-delete"
                  placement="top"
                  title={t('dj-确认删除吗？')}
                  description={null}
                  okText={t('dj-删除')}
                  cancelText={t('dj-取消')}
                  onConfirm={() => handleDelete(i, option)}
                >
                  <Icon className="iconfont" type="iconshanchu3" />
                </Popconfirm>
                <a>
                  <Icon className="iconfont button-move" type="icontuozhuai1" />
                </a>
              </div>
            </div>
          );
        })}
      </div>
      <OptionModal
        visible={optionVisible}
        data={optionData}
        index={optionIndex}
        onChange={handleOptionOk}
        onCancel={handleOptionCancel}
      />
    </>
  );
};

export default CustomOptions;
