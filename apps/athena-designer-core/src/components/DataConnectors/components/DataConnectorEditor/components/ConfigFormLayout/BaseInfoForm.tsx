import React, { useRef } from 'react';
import { Form, Input, Select, Button, Divider,Tooltip,message } from 'antd';
import {BaseInfoFormProps} from '../../../../types';
import { EnterOutlined} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import VariableSelect from '@/components/DataVariablesSelect'
import { insertTextAtCursor } from '@/components/DataConnectors/utils/cursorUtils';
const methodOptions = [
    { value: 'GET', label: 'GET' },
    { value: 'POST', label: 'POST' },
    { value: 'PUT', label: 'PUT' },
    { value: 'DELETE', label: 'DELETE' },
    { value: 'PATCH', label: 'PATCH' }
]
const BaseInfoForm: React.FC<BaseInfoFormProps> = ({ data, onChange,onRequestApi }) => {
    const { t } = useTranslation();
    const inputRef = useRef<any>(null);
    return (
        <>
        <Form className='base-info-form'>
            <Form.Item label={t('dj-接口名称')} required>
                <Input allowClear value={data.name} onChange={e => onChange({ ...data, name: e.target.value })} />
            </Form.Item>
            <Form.Item label={t('dj-请求接口')} required>
                <div className='base-info-form-flex'>
                    <Select className='select'
                        options={methodOptions}
                        value={data.method}
                        onChange={method =>  onChange({ ...data, method}) }></Select>
                    <Input className='input'
                        ref={inputRef}
                        value={data.path}
                        suffix={
                            <Tooltip title="添加变量">
                                <VariableSelect onSelect={(varName) => {
                                    console.log('todo添加变量--varName', varName)
                                    const newValue = insertTextAtCursor(data.path || '', varName, inputRef);
                                    onChange({...data, path: newValue})
                                    
                                }} />
                              
                            </Tooltip>
                        }
                        onChange={e => onChange({ ...data, path: e.target.value })} />
                </div>
            </Form.Item>
            <Button className='base-info-form-btn' onClick={onRequestApi} type="primary" style={{ marginLeft: 8 }}>{t('dj-立即请求')}</Button>
            
        </Form>
        <Divider style={{ margin: '8px 0' }} />

        </>

    );
};

export default BaseInfoForm;