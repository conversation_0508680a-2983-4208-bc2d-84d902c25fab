
.base-info-form {
    width: 70%;
    position: relative;
    .base-info-form-flex {
        display: flex;
        align-items: center;
        .select {
            width: 150px;
            height: 28px;
        }
        .input {
            flex: 1;
            margin-left: 8px;
            height: 28px;
        }
    }
}
.base-info-form-btn {
    position: absolute;
    right: -100px;
    bottom: 2px;
}
// 请求参数style
.params-box {
    .params-box-title {
        font-size: 14px;
        font-weight: 600;
        color: #1D1C33;
    }
    .ant-tabs-nav {
        margin: 0;
    }

    .params-box-tab {
        border: 1px solid #fcfcfc;
        margin: 16px 0;
        padding: 12px 0;
        border-radius: 4px;
        background: #FFFFFF;
        box-shadow: 0px 1px 10px 0px rgba(69, 67, 143, 0.05), 0px 4px 5px 0px rgba(60, 59, 112, 0.08), 0px 2px 4px -1px rgba(44, 44, 77, 0.12);
        // 请求参数tab模块公共样式
        .tab-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding-left: 16px;
            // border-bottom: 1px solid #F0F0F5;
            padding-bottom: 12px;
            // margin-bottom: 4px;

            .header-right {
                color: #605CE5;
                display: flex;
                align-items: center;
                .btn {
                    font-size: 14px;
                    display: flex;
                    align-items: center;
                    padding: 0;
                    margin-right: 16px;
                    .ant-btn-icon {
                        margin-right: 4px;
                        font-size: 16px;
                    }
                    
                }
            }

            .header-left {
                .title {
                    font-size: 13px;
                    font-weight: 500;
                    color: #333;
                }
            }
        }
    }

}

// 编辑表格style
.table-box {
    .ant-table {
        table {
            border: 0;
            background-color: #fff !important;
          }
          .ant-table-thead {
                border-radius: 4px 0px 0px 4px;
                box-sizing: border-box;
                border-width: 1px 0px 1px 1px;
                border-style: solid;
                border-color: #E6E6EB;
            .ant-table-cell {
                box-sizing: border-box;
                border-width: 1px 0px;
                border-style: solid;
                border-color: #E6E6EB;
                &:first-child {
                    border-radius: 4px 0px 0px 4px;
                    border-left-width:1px ;
                }
                &:last-child {
                    border-radius: 0px 4px 4px 0px;
                    border-right-width:1px ;
                }
                
            }
        }
        .ant-table-thead > tr > th {
            background-color: #F7F7FA !important;
            color: #8C8B99;
            font-weight: 600;
            font-size: 14px;
          }
          .ant-table-tbody {
            .ant-table-cell {
                // border-bottom: 0;
            }
          }
          .ant-table-tbody .ant-table-row > td {
            background-color: #fff !important;
          }
    } 
    .drag-visible {
        &::before {
            width: 0 !important;
        }
    }
    .drag-input-item {
        border-radius: 4px;
        .ant-select-selector {
            border-radius: 4px;
            height: 28px;
        }
    }
    .ant-select {
        height: 28px;
    }
    .move-icon {
        color: #1D1C33;
        font-size: 16px;
        svg {
            cursor: move !important;
        }
    }
}
// .table-box-border {
//     table {
//         border: 0;
//         background-color: #fff !important;
//       }
//       .ant-table-thead > tr > th {
//         background-color: #F7F7FA !important;
//         color: #8C8B99;
//         font-weight: 600;
//         font-size: 14px;
//       }
// }