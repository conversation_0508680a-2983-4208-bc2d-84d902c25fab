import React, { useMemo, useEffect } from 'react';
import { useState } from 'react';
import DataSearch from './components/DataSearch';
import DataConnectorsList from './components/DataConnectorsList/index'
import DataConnectorEditor from './components/DataConnectorEditor/index'
import { DataConnectorsProps } from './types'
import { v4 as uuidv4 } from 'uuid';
import { externalToInternal, internalToExternal } from './utils/index';
import './index.less'
import { cloneDeep, isEmpty } from 'lodash';
import { useDynamicWorkDesignContext } from '@components/DynamicWorkDesign/context'
import { hotkeyUtils } from '@/common/utils/hotkeyUtils';



const DataConnectors: React.FC<DataConnectorsProps> = ({
  dataConnectors,
  onChange,
}) => {
  // 编辑器弹窗
  const [drawVisible, setDrawVisible] = useState(false);
  // 当前编辑项
  const [dataConnectorsItem, setDataConnectorsItem] = useState<any>(null);
  // 转化数据给子组件使用
  const dataConnectorsList = useMemo(
    () => {
      const newList = externalToInternal(dataConnectors)
      console.log('dataConnectors---externalToInternal', newList)
      return newList
    },
    [dataConnectors]
  );
  const { dispatch } = useDynamicWorkDesignContext();

  // 生成数据源名称的 Map
  const dataConnectorNameMap = useMemo(() => {
    const map = new Map();
    dataConnectorsList.forEach(item => {
      map.set(item.name, item.id);
    });
    return map;
  }, [dataConnectorsList]);

  useEffect(() => {
    if (drawVisible) {
      hotkeyUtils.enable();
    } else {
      hotkeyUtils.disable();
    }
  }, [drawVisible]);
  
  // 组件卸载时也要禁用快捷键
  useEffect(() => {
    return () => {
      hotkeyUtils.disable();
    };
  }, []);
  
  // 转化数据给父组件
  const handleChange = (newList) => {
    const dataConnectors = internalToExternal(newList)
    console.log('dataConnectors-internalToExternal', dataConnectors)
    onChange?.(dataConnectors);
  }

  // 删除数据源
  const handleDelete = (id) => {
    const newList = dataConnectorsList.filter(item => item.id !== id);
    handleChange(newList)
  };
  // 新增数据源
  const handleAdd = () => {
    setDataConnectorsItem(null); // 新增时无item
    setDrawVisible(true);
  };
  // 编辑数据源
  const handleEdit = (item) => {
    setDataConnectorsItem(item);
    setDrawVisible(true);
  };
  // 复制数据源
  const handleCopy = (data) => {
    const newData = cloneDeep(data);
    newData.id = uuidv4()
    newData.name = `${newData.name}_copy`
    if (dataConnectorNameMap.has(newData.name)) {
      newData.name = `${newData.name}_copy`
    }
    handleChange([...dataConnectorsList, newData])
  }

  //  保存数据源（新增或编辑）
  const handleSave = (item) => {
    const isEdit = !!item.id;
    const updatedItem = { ...item, id: item.id || uuidv4() }; // 新增时自动生成 id
    const updatedList = isEdit
      ? dataConnectorsList.map(d => d.id === updatedItem.id ? updatedItem : d)
      : [...dataConnectorsList, updatedItem];
    handleChange(updatedList);
    setDrawVisible(false);
  };

 // 导入数据源
  const handleImport = () => {
    dispatch('importDataConnectors', {}, (dataConnectorsItem:any) => {
      console.log('导入结果---:', dataConnectorsItem);
      if (!isEmpty(dataConnectorsItem)) {
        const newItems = externalToInternal([dataConnectorsItem])
        setDataConnectorsItem(newItems[0])
        setDrawVisible(true)
      }
    });
  };



  return <div className='data-connectors'>
    {/* 搜索数据源 */}
    <DataSearch
      onImport={handleImport}
      onInputChange={(val) => { /* 处理输入 */ }}
      onAdd={handleAdd} />

    {/* 左侧数据源列表 */}
    <DataConnectorsList
      dataList={dataConnectorsList}
      onDelete={handleDelete}
      onCopy={handleCopy}
      onEdit={handleEdit} />

    {/* 数据源编辑/新增 */}
    <DataConnectorEditor
      dataConnectorNameMap={dataConnectorNameMap}
      visible={drawVisible}
      currentItem={dataConnectorsItem}
      onClose={() => setDrawVisible(false)}
      onSave={handleSave}
    />
  </div>;
};

export { DataConnectors }; 