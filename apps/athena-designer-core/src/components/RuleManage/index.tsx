import React, { createContext, forwardRef, useEffect, useImperativeHandle } from 'react';
import './index.less';
import RuleManageRuleList from './components/RuleManageRuleList';
import RuleManageCollectionModal from './components/RuleManageCollectionModal';
import RuleManageContentModal from './components/RuleManageContentModal';
import RuleManageExtendEditorModal from './components/RuleManageExtendEditorModal';
import RuleManageExtendInfoModal from './components/RuleManageExtendInfoModal';
import { Spin } from 'antd';
import { useRuleManageStore, useRuleManageRuleListStore } from './store';
import { ExternalAddSelectInfo, Rule, RuleManageOriginData } from './config/ruleManage.type';
import { ToTemplateManager, OfflineRuleListChange } from './context';
import useCommonRuleOperation from './hooks/useCommonRuleOperation';

/**
 * 规则管理组件
 * 全局事件：（具有父子等层级关系的组件间调用定义为context，不具有父子等层级关系的组件间调用定义为全局事件）
 * ruleManageReloadList // 触发列表组件刷新
 * ruleManageReloadTemplate // 触发模板列表的刷新
 *
 */

export interface RuleManageProps extends RuleManageOriginData {
  ruleListChange: (ruleList: Rule[]) => void; // 对外暴露组件最新的规则数据
  toTemplateManager: () => void; // 跳转到模板管理
}

const RuleManage = forwardRef((props: RuleManageProps, ref) => {
  const {
    ruleListChange,
    toTemplateManager,
    domainType,
    code,
    applicationCode,
    isMobile,
    isOffline = false,
    offlineRuleList,
    defaultDataSourceName,
    fieldTreeMap,
    hideShortcut,
    hideAuth,
    hideRules,
  } = props;
  const { isLoading, setRuleManageOriginData } = useRuleManageStore((state) => ({
    isLoading: state.isLoading,
    setRuleManageOriginData: state.setRuleManageOriginData,
  }));

  const { ruleList, setExternalAddSelectInfo } = useRuleManageRuleListStore((state) => ({
    ruleList: state.ruleList,
    setExternalAddSelectInfo: state.setExternalAddSelectInfo,
  }));

  const { addRule, editRule, deleteRule } = useCommonRuleOperation(ruleListChange);

  useEffect(() => {
    if (isOffline) return;
    ruleListChange(ruleList);
  }, [ruleList, isOffline]);

  useEffect(() => {
    setRuleManageOriginData({
      domainType,
      code,
      applicationCode,
      isMobile: isMobile ?? false,
      isOffline: isOffline ?? false,
      offlineRuleList: offlineRuleList ?? [],
      defaultDataSourceName: defaultDataSourceName ?? '',
      fieldTreeMap: fieldTreeMap ?? null,
      hideShortcut,
      hideAuth,
      hideRules
    });
  }, [
    domainType,
    code,
    applicationCode,
    isMobile,
    isOffline,
    offlineRuleList,
    defaultDataSourceName,
    fieldTreeMap,
    hideShortcut,
    hideAuth
  ]);

  useImperativeHandle(ref, () => {
    return {
      addRule, // 通过规则基础值打开新增规则面板，至少有规则type
      editRule, // 编辑规则数据
      deleteRule, // 删除规则
      openAddSelect: (externalAddSelectInfo: ExternalAddSelectInfo) => {
        // 打开新增规则选择面板，传null则关闭
        setExternalAddSelectInfo(externalAddSelectInfo);
      },
    };
  }, [addRule, editRule, deleteRule, setExternalAddSelectInfo]);

  const handleToTemplateManager = () => {
    toTemplateManager();
  };

  const handleOfflineRuleListChange = (ruleList: Rule[]) => {
    ruleListChange(ruleList);
  };

  return (
    <div className="rule-manage">
      <OfflineRuleListChange.Provider value={handleOfflineRuleListChange}>
        <Spin spinning={isLoading} className="rule-manage-loading"></Spin>
        <ToTemplateManager.Provider value={() => handleToTemplateManager()}>
          {/* 规则管理渲染的列表，也包含搜索和操作栏 */}
          <RuleManageRuleList />
        </ToTemplateManager.Provider>
        {/* 这里考虑到可能会存在没有list，直接打开规则编辑的情况，所以几个Modal挂载在父层级，由store控制数据 */}
        <RuleManageCollectionModal />
        <RuleManageContentModal />
        <RuleManageExtendEditorModal />
        <RuleManageExtendInfoModal />
      </OfflineRuleListChange.Provider>
    </div>
  );
});

export default RuleManage;
