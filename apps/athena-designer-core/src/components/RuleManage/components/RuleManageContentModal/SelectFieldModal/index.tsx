import React, { useEffect, useMemo, useState } from 'react';
import i18next, { t } from 'i18next';
import { Config<PERSON><PERSON>ider, Button, Flex, Modal, Tree, Select } from 'antd';
import './index.less';
import { AthTreeDataNode } from './type';
import { isEqual } from 'lodash';
import { findSelectedNodes, getAllKeys } from './util';

const fieldNames = { title: 'title', key: 'fullPath', children: 'children' };

const AthTreeNode: React.FC<AthTreeDataNode> = (props: AthTreeDataNode) => {
  const { fullPath, data_name, description } = props;
  return (
    <div className="ath-field-panel-tree-node" data-full-path={fullPath}>
      <span className="title">{data_name}</span>
      <span className="description">{description?.[i18next.language] ?? ''}</span>
    </div>
  );
};

export interface SelectFieldModalProps {
  isOpen: boolean;
  treeData?: any[];
  title?: string;
  initCheckedKeys?: string[];
  isMultiple?: boolean;
  fieldTreeMap?: { [key: string]: any };
  dataSourceName?: string;
  onCancel: () => void;
  onSubmit: (node: AthTreeDataNode[]) => void;
}
const SelectFieldModal: React.FC<SelectFieldModalProps> = (props: SelectFieldModalProps) => {
  const [currentTreeData, setCurrentTreeData] = useState<AthTreeDataNode[]>([]);
  const [currentDataSourceName, setCurrentDataSourceName] = useState<string>('');
  const [expandedKeys, setExpandedKeys] = useState([]); //展开的节点
  const {
    dataSourceName,
    isOpen,
    onCancel,
    onSubmit,
    fieldTreeMap,
    treeData = [],
    title,
    initCheckedKeys,
    isMultiple = false,
  } = props;

  useEffect(() => {
    if (isOpen) {
      if (fieldTreeMap?.size > 0 && treeData.length === 0) {
        let treeDatas = [];
        for (const [, datas] of fieldTreeMap.entries()) {
          treeDatas = treeDatas.concat([...(datas ?? [])]);
        }
        if (!isEqual(currentTreeData, treeDatas)) {
          setCurrentTreeData(treeDatas);
        }
      } else if (treeData?.length > 0) {
        if (!isEqual(currentTreeData, treeData)) {
          setCurrentTreeData(treeData);
        }
      } else {
        setCurrentTreeData([]);
      }
    }
  }, [fieldTreeMap, treeData, isOpen]);

  useEffect(() => {
    setCurrentDataSourceName(dataSourceName);
  }, [dataSourceName]);

  // const [checkedNodes, setCheckedNodes] = useState<AthTreeDataNode[]>();
  const [checkedKeys, setCheckedKeys] = useState<string[]>([]);

  const checkedNodes = useMemo(() => {
    return findSelectedNodes(checkedKeys, currentTreeData);
  }, [currentTreeData, checkedKeys]);

  useEffect(() => {
    const allKeys = getAllKeys(currentTreeData);
    setExpandedKeys(allKeys);
  }, [currentTreeData]);

  useEffect(() => {
    !!initCheckedKeys && setCheckedKeys(initCheckedKeys);
  }, [initCheckedKeys]);

  const handleCheck = (checkedKeys, e) => {
    setCheckedKeys(checkedKeys.checked);
    // setCheckedNodes(e.checkedNodes);
  };

  const handleSelect = (selectedKeys, e) => {
    setCheckedKeys(selectedKeys);
    // setCheckedNodes(e.selectedNodes);
  };

  const handleModalOk = () => {
    onSubmit(checkedNodes);
  };

  const handlecCancel = () => {
    onCancel();
  };

  const handleSelectChange = (value: string) => {
    setCurrentDataSourceName(value);
    setCurrentTreeData(fieldTreeMap?.get(value));
  };

  return (
    <Modal
      open={isOpen}
      title={t(title ?? 'dj-添加字段')}
      footer={null}
      onCancel={handlecCancel}
      destroyOnClose={true}
      maskClosable={false}
      width={520}
      className="select-field-modal"
    >
      <div className="select-field-modal-content">
        {(currentTreeData?.length > 0 && fieldTreeMap?.size > 0) && (
          <Select
            value={currentDataSourceName}
            className="ath-field-modal-select"
            size="small"
            onChange={handleSelectChange}
            options={currentTreeData.map((i) => ({ label: i.name, value: i.name }))}
          />
        )}
        <Tree
          defaultExpandAll={true}
          expandedKeys={expandedKeys}
          onExpand={(expandedKeysValue: any) => {
            setExpandedKeys(expandedKeysValue);
          }}
          treeData={currentTreeData}
          fieldNames={fieldNames}
          titleRender={AthTreeNode}
          checkStrictly={true}
          checkedKeys={checkedKeys}
          checkable={isMultiple}
          selectable={!isMultiple}
          selectedKeys={checkedKeys}
          onCheck={handleCheck}
          onSelect={handleSelect}
        />
      </div>

      <Flex className="select-field-modal-content-footer" gap="large" wrap="wrap" justify="center">
        <Button onClick={handlecCancel}>{t('dj-取消')}</Button>
        <Button type="primary" onClick={handleModalOk}>
          {t('dj-确定')}
        </Button>
      </Flex>
    </Modal>
  );
};

export default SelectFieldModal;
