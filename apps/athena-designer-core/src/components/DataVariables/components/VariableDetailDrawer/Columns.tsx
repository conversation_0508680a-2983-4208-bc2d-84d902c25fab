// columns.tsx
import React from "react";
import { Input, Switch, Button, Tooltip } from "antd";
import Icon from '@components/Icon';
import type { VariableItem } from "../../types";
import { t } from 'i18next';

const typeOptions = [
  { label: "string", value: "string" },
  { label: "number", value: "number" },
  { label: "boolean", value: "boolean" },
];
export function getColumns({
  handleChange,
  handleDelete,
  errorMessages,
  showOperation
}) {
  return [
    {
      title: `${t('dj-名称')}`,
      width: 260,
      dataIndex: "name",
      render: (_: any, record: VariableItem) => (
        <Input disabled={record.disabled} className={`variable-input ${record.disabled ? 'variable-readonly' : ''}`}
          value={record.name}
          placeholder={record.disabled?'-': t('dj-请输入')}
          status={errorMessages[record.id] ? "error" : undefined}
          onChange={e => handleChange(record.id, "name", e.target.value)}
        />
      ),
    },
    // {
    //   title: "类型",
    //   dataIndex: "type",
    //   render: (_: any, record: VariableItem) => (
    //     <Select disabled={record.disabled}
    //       value={record.type}
    //       options={typeOptions}
    //       onChange={val => handleChange(record.id, "type", val)}
    //     />
    //   ),
    // },
    {
      title: `${t("dj-默认值")}`,
      dataIndex: "defaultValue",
      render: (_: any, record: VariableItem) => (
        <Tooltip overlayStyle={{ 
          minWidth:  record?.defaultValue?.length > 200 ? '800px' : '100px' 
        }}  overlayClassName="variable-default-value-tooltip" title={record.defaultValue}>
          <Input disabled={record.disabled} className={`variable-input ${record.disabled ? 'variable-readonly' : ''}`}
          value={record.defaultValue} placeholder={record.disabled? '-': t('dj-请输入')}
          onChange={e => handleChange(record.id, "defaultValue", e.target.value)}
        />
        </Tooltip>
               
      ),
    },
    {
      title: `${t('dj-描述')}`,
      dataIndex: "description",
      render: (_: any, record: VariableItem) => (
        <Input disabled={record.disabled} className={`variable-input ${record.disabled ? 'variable-readonly' : ''}`}
          value={record.description} placeholder={record.disabled? '-': t('dj-请输入')}
          onChange={e => handleChange(record.id, "description", e.target.value)}
        />
      ),
    },
    {
      title: `${t('dj-操作')}`,
      dataIndex: "operation",
      hidden: !showOperation,
      width: 100,
      render: (_: any, record: VariableItem) => (
        <div className="variable-operation">
          {/* todo 本期变量系统变量只读 只拿传入的disabled */}
          {/* <Switch disabled={record.disabled} style={{ width: 16, height: 16, marginRight: 8 }}
            checked={!record.disabled}
            onChange={checked => handleChange(record.id, "disabled", !checked)}
          /> */}
          <Button disabled={record.disabled} type="link" icon={<Icon type='icondelete3' className="variable-switch"
            style={{ color: "#605CE5", fontSize: 14, cursor: 'pointer' }}
          />} onClick={() => handleDelete(record.id)}>
          </Button>


        </div>
      ),
    },
  ];
}