import React, { useState, useEffect } from "react";
import { Drawer, Table, Input, Select, Switch, Button, message, Space } from "antd";
import  { VariableItem, VariableTreeNode,VariableType } from "../../types";
import { PlusCircleOutlined } from '@ant-design/icons'
import './index.less'
import { v4 as uuidv4 } from 'uuid';
import { getColumns } from './Columns';
import { t } from 'i18next';

interface Props {
  visible: boolean;
  variable: VariableTreeNode | null;
  onClose: () => void;
  onSave: (item: VariableItem[]) => void;
}
const tipsConfig = {
   user: 'dj-用于存储用户变量说明',
   system: 'dj-用于存储系统变量说明'
}



const VariableDetailDrawer: React.FC<Props> = ({ visible, variable, onClose, onSave }) => {
  // 1. 本地 state
  const [localVariable, setLocalVariable] = useState<VariableItem[]>([]);
   // 新增错误信息状态
  const [errorMessages, setErrorMessages] = useState<Record<string, string>>({});

  // 2. variable 变化时同步
  useEffect(() => {
    setLocalVariable(variable?.children || []);
  }, [variable]);

  // 3. 编辑操作
  const handleChange = (id: string, key: string, value: any) => {
    if (!localVariable) return;
    const newData = localVariable.map(item =>
      item.id === id ? { ...item, [key]: value } : item
    )
    setLocalVariable(newData);
    setErrorMessages(prev => ({ ...prev, [id]: '' }));
  };

  // 4. 删除操作
  const handleDelete = (id: string) => {
    if (!localVariable) return;
    const newData = localVariable.filter(item => item.id !== id)
    setLocalVariable(newData);
    setErrorMessages(prev => {
      const { [id]: _, ...rest } = prev;
      return rest;
    });
  };

  const handleSave = () => {
    const newErrorMessages: Record<string, string> = {};
    let hasError = false;

    // 变量必填
    localVariable.forEach(item => {
      if (!item.name || item.name.trim() === "") {
        newErrorMessages[item.id] = 'required';
        hasError = true;
      }
    });

    // 2. 系统变量必须 sys_ 开头
    if (variable?.type === VariableType.System) {
      localVariable.forEach(item => {
        if (!item.name.startsWith('sys_')) {
          newErrorMessages[item.id] = 'sys_prefix';
          hasError = true;
        }
      });
    }
  
    // 3. 格式校验 变量暂时无格式校验 先注释
    // const nameReg = /^(?!.*__)[a-zA-Z_][a-zA-Z0-9_]*[a-zA-Z0-9]$/;
    // localVariable.forEach(item => {
    //   if (!nameReg.test(item.name)) {
    //     newErrorMessages[item.id] = 'invalid_format';
    //     hasError = true;
    //   }
    // });

    // 4. 唯一性校验
    const nameSet = new Set();
    localVariable.forEach(item => {
      if (nameSet.has(item.name)) {
        newErrorMessages[item.id] = 'duplicate_name';
        hasError = true;
      }
      nameSet.add(item.name);
    });

    setErrorMessages(newErrorMessages);

    if (hasError) {
      if (Object.values(newErrorMessages).includes('required')) {
        message.warning(t('dj-变量名称为必填项'));
      } else if (Object.values(newErrorMessages).includes('sys_prefix')) {
        message.warning(t('dj-系统变量名称tips'));
      } else if (Object.values(newErrorMessages).includes('invalid_format')) {
        message.warning(t('dj-系统变量规则'));
      } else if (Object.values(newErrorMessages).includes('duplicate_name')) {
        message.warning(t('dj-变量名称不能重复！'));
      }
      return;
    }

    onSave(localVariable);
  };

  const handleAdd = () => {

    const newItem = {
      id: uuidv4(),
      disabled: false,
      scope: variable.type
    }
    setLocalVariable([...localVariable, newItem]);
  }

  const columns = getColumns({ handleChange, handleDelete, errorMessages, showOperation: variable?.type === VariableType.User });



  return (
    <Drawer className="variable-drawer-box"
      title={t(variable?.name)}
      closeIcon={null}
      open={visible}
      onClose={onClose}
      width={'70%'}
      extra={
        <Space>
          <Button onClick={onClose}>{t('dj-取消')}</Button>
          {
            variable?.type === VariableType.User && (
              <Button type="primary" onClick={handleSave}>{t('dj-保存')}</Button>
            )
          }
        </Space>
      }
    >
      <div className="variable-tips">{t(tipsConfig[variable?.type])}
      </div>
      <Table className="variable-table"
        dataSource={localVariable}
        columns={columns}
        rowKey="id"
        pagination={false}
        locale={{
          emptyText: <div style={{ paddingTop: '16px', textAlign: 'center' }}>{t('dj-暂无数据')}</div>
        }}
      />
      {
        variable?.type === VariableType.User && (
          <div onClick={handleAdd} className="variable-bottom-btn"><PlusCircleOutlined /> {t('dj-添加变量')}</div>
        )
      }
    </Drawer>
  );
};

export default VariableDetailDrawer;