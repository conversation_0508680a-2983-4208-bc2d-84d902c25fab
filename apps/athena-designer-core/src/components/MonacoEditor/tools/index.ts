import { SupportedEditorType } from '@components/MonacoEditor/enum';
import { Monaco } from '@monaco-editor/react';
import HooksData from '@/assets/config/hooks.json';
import { last } from 'lodash';

import {
  defaultEventSourceField,
  component_code,
  data_code,
} from '@components/MonacoEditor/constant';
import HooksConfig from '@/assets/config/hooks.json';
import { capitalize } from 'lodash';
import i18next from 'i18next';

import type {
  HooksParamsEnum,
  IHookParams,
  IGetHooksParams,
  IInitCodeCommentParams,
  IFieldData,
  IHooksEventSourceField,
  IBuildTipParams,
  IHookConfig,
  IFieldTreeNodeInfo,
} from '@components/MonacoEditor/types/hooks';
import type { IComponentInfo, IFieldTreeNode } from '@components/MonacoEditor/types/form';
import type { ILangInfo } from '@/components/ActionModal/api';
import { envParams } from '@/env';

const SubmitButtonTypeSet: Set<string> = new Set([
  'BUTTON_DATA_DELETE',
  'BUTTON_DELETE_TO_RECYCLE',
  'BUTTON_RECYCLE_DELETE',
  'BUTTON_DATA_DELETEALL',
  'BUTTON_DATA_SAVE',
  'BUTTON_DATA_COMBINE_SA',
  'BUTTON_DATA_UPDATE',
  'BUTTON_DATA_INVALID',
  'BUTTON_DATA_VALID',
  'BUTTON_DATA_RECOVER',
  'BUTTON_WORKFLOW_INVOKE',
  'BUTTON_WORKFLOW_ABORT',
  'BUTTON_AGREE',
  'BUTTON_DISAGREE',
  'BUTTON_ACT_RETURN',
  'BUTTON_ACT_ADD',
  'BUTTON_WORKFLOW_SUBMIT',
  'BUTTON_DATA_TERMINATE',
  'BUTTON_REPORT_QUERY',
  'BUTTON_TASK_REEXECUTE',
  'BUTTON_TASK_TERMINATE',
  'BUTTON_TASK_REAPPROVE',
  'BUTTON_TASK_ADD',
  'BUTTON_TASK_AGREE',
]);
const BusinessBaseButtonTypeSet: Set<string> = new Set([
  'BUTTON',
  'BUTTON_PRINT',
  'BUTTON_SUB_PAGE',
]);
const ToolbarButtonTypeSet: Set<string> = new Set([
  'BUTTON_OPENPAGE_ADD',
  'BUTTON_COPY_ITEM',
  'BUTTON_TOOLBAR_EDIT',
  'BUTTON_TOOLBAR_NEXT',
  'BUTTON_TOOLBAR_PREVIOUS',
]);
const OperationButtonTypeSet: Set<string> = new Set([
  'BUTTON_PRINT',
  'BUTTON_DELETE_ITEM',
  'BUTTON_COPY_ITEM',
  'BUTTON_OPENPAGE_EDIT',
  'BUTTON_OPENPAGE_COPY',
  'BUTTON_BATCH_DELETE_ITEM',
  'BUTTON_UPLOAD_FILE',
  'BUTTON_DOWNLOAD_TEMPLATE',
  'BUTTON_DRAWINGS_DOWNLOAD',
  'BUTTON_FRONT_EXPORT',
  'BUTTON_BACKEND_EXPORT',
  'BUTTON_BATCH_SET_DATE',
  'BUTTON_OPERATE_SCRIPT',
  'BUTTON_SPLIT_ROW',
  'BUTTON_AUTO_SPLIT_ROW',
  'BUTTON_ADD_ITEM',
  'BUTTON_OPENPAGE_ADD',
]);

// 组件解耦后的按钮
const newButtonTypeSet: Set<string> = new Set([
  'BUTTON_SUBMIT_ACTION_DECOUPLE',
  'BUTTON_DECOUPLE',
  'BUTTON_ADD_ITEM_DECOUPLE',
  'BUTTON_EDIT_ITEM_DECOUPLE',
  'BUTTON_DETAIL_ITEM_DECOUPLE',
  'BUTTON_DELETE_ITEM_DECOUPLE',
  'BUTTON_OPENPAGE_ADD_DECOUPLE',
  'BUTTON_COMBINE_SAVE_DECOUPLE',
  'BUTTON_DATA_DELETE_DECOUPLE'
])

const AllButtonSet: Set<string> = new Set([
  ...SubmitButtonTypeSet,
  ...BusinessBaseButtonTypeSet,
  ...ToolbarButtonTypeSet,
  ...OperationButtonTypeSet,
]);

const {
  ComponentCommonHooksMobile,
  ComponentCommonHooks,
  ComponentCustomHooksMobile,
  ComponentCustomHooks,
  DataCommonHooks,
  SubmitActionsHooks,
  SubpageButtonHooks,
  // OperationsToolbarHooks,
} = HooksConfig;

/**
 * 判断是否是null或者是undefined
 * @param ele 要判断的值
 * @return boolean
 */
export function isNone(ele: unknown): boolean {
  return ele === undefined || ele === null;
}

export function setJavascript(monaco: Monaco) {
  monaco.languages.typescript.javascriptDefaults.setCompilerOptions({
    target: monaco.languages.typescript.ScriptTarget.ES2019,
    allowNonTsExtensions: true,
    lib: [],
  });
  if (monaco.languages.typescript.javascriptDefaults.getExtraLibs()) {
    monaco.languages.typescript.javascriptDefaults.setExtraLibs([]);
  }
}

/**
 * 目前只有javascript需要去设置
 */
export function doDefaultSet(type: SupportedEditorType, monaco: Monaco) {
  switch (type) {
    case SupportedEditorType.JAVASCRIPT:
      setJavascript(monaco);
      break;
    default:
      break;
  }
}

/**
 * 返回通用控件勾子名称和勾子参数的Map
 * @param {boolean} isMobile 是否移动端
 * @returns Map<string, HooksParamsEnum[]>
 */
export function getComponentHooksParamsMap(isMobile?: boolean): Map<string, HooksParamsEnum[]> {
  const hookParams = isMobile ? ComponentCommonHooksMobile : ComponentCommonHooks;
  return new Map(hookParams.map((hook) => [hook.name, hook.params]));
}

/**
 * 返回私有控件勾子名称和勾子参数的Map
 * @param {string} componentType 'BUTTON'
 * @returns Map<string, HooksParamsEnum[]>
 */
export function getCustomComponentHooksParamsMap(
  componentType: string,
  isMobile?: boolean
): Map<string, HooksParamsEnum[]> {
  const isButton = AllButtonSet.has(componentType);
  const actualType = isButton ? 'BUTTON' : componentType;
  let hookParams: IHookParams[] = isMobile
    ? ComponentCustomHooksMobile[actualType]
    : ComponentCustomHooks[actualType];
  if (isButton) {
    hookParams = hookParams
      .concat(SubmitActionsHooks as IHookParams[])
      .concat(SubpageButtonHooks as IHookParams[]);
  }
  return new Map((hookParams ?? []).map((hook) => [hook.name, hook.params]));
}

/**
 * 返回通用字段勾子名称和勾子参数的Map
 * @returns Map<string, HooksParamsEnum[]>
 */
export function getDataHooksParamsMap(): Map<string, HooksParamsEnum[]> {
  return new Map(DataCommonHooks.map((hook) => [hook.name, hook.params as HooksParamsEnum[]]));
}

// export function getToolbarHooksParamsMap(): Map<string, HooksParamsEnum[]> {
//   return new Map(
//     OperationsToolbarHooks.map((hook) => [hook.name, hook.params as HooksParamsEnum[]])
//   );
// }

/**
 * 返回提交按钮勾子名称和勾子参数的Map
 * @returns Map<string, HooksParamsEnum[]>
 */
export function getActionHooksParamsMap(): Map<string, HooksParamsEnum[]> {
  return new Map(SubmitActionsHooks.map((hook) => [hook.name, hook.params as HooksParamsEnum[]]));
}

/**
 * 根据关联类型求需要添加的附加源文件类型列表
 * @param {IGetHooksParams} params 入参
 * @returns 返回需要添加的附加源文件类型列表
 */
export function getHooksParams(params: IGetHooksParams): HooksParamsEnum[] {
  const { type, componentType, customHooks, eventSourceHook, isMobile } = params;
  const componentCommonMap = getComponentHooksParamsMap(isMobile);
  const customParams: HooksParamsEnum[] =
    customHooks.find((hook) => hook.name === eventSourceHook)?.params || [];
  if (type === 'data') {
    return getDataHooksParamsMap().get(eventSourceHook) ?? [];
  } else {
    if (componentCommonMap.has(eventSourceHook)) {
      return componentCommonMap.get(eventSourceHook) ?? [];
    } else if (customParams.length > 0) {
      return customParams ?? [];
    } else {
      const componentMap = getCustomComponentHooksParamsMap(componentType, isMobile);
      return componentMap.get(eventSourceHook) ?? [];
    }
  }
}

/**
 * 初始化默认代码
 * @param {IGetHooksParams} params
 */
export function initSimpleCode(params: IInitCodeCommentParams): string {
  const {
    eventSourceType,
    eventSourceField,
    customHooks = [],
    eventSourceHook,
    eventSource,
  } = params;
  const { path, schema } = eventSourceField ?? defaultEventSourceField;
  const defaultValue = customHooks.find((hook) => hook.name == eventSourceHook)?.defaultValue ?? '';
  switch (eventSourceType) {
    case 'component':
      return (
        (component_code[eventSourceHook] || defaultValue)?.replaceAll(
          "'path'",
          `'${path}.${schema}'`
        ) ?? ''
      )?.replaceAll("'id'", `'${eventSource}'`);
    case 'data':
      return (data_code[eventSourceHook] ?? '')?.replaceAll("'path'", `'${path}.${schema}'`) ?? '';
    case 'submitAction':
    case 'toolbar':
    case 'operation':
      return '';
    default:
      break;
  }
}

/**
 * 初始化注释文本
 * @param {IGetHooksParams} params
 * @param {string} code 手动指定的代码内容
 */
export function initComments(params: IInitCodeCommentParams, code?: string): string {
  const { hooksParams = [] } = params;
  if (hooksParams.length === 0) return '';
  const codecommentList = hooksParams.map(
    (param: HooksParamsEnum) => ` * @param ${param} ${HooksData?.ParamDescription[param]}`
  );
  codecommentList.unshift('/**');
  codecommentList.push(' */');
  return `${codecommentList.join('\n')}`;
  // return `${codecommentList.join('\n')}\n\n${code ?? initSimpleCode(params)}`;
}

/**
 * 获取父级数据
 * @param {IFieldData} data
 * @return {IFieldData[]}
 */
export function getParentData(
  data: IFieldData | IFieldData[],
  eventSourceField: IHooksEventSourceField
): IFieldData[] {
  let returnVal: IFieldData[] = [];
  if (Object.prototype.toString.call(data) === '[object Array]') {
    // (data as IFieldData[]).forEach((d) => {
    //   returnVal = getParentData(d, eventSourceField);
    // });
    return [];
  } else {
    const objectData = data as IFieldData;
    const { schema, path = '' } = eventSourceField;
    if (schema === objectData.data_name && !objectData.fullPath && !path) {
      returnVal = [objectData];
    } else if (
      path === objectData.fullPath &&
      objectData.field?.some((s) => s.data_name === schema)
    ) {
      returnVal = objectData.field;
    } else if (path && schema) {
      for (const d of objectData.field) {
        if (d.field?.length > 0) {
          returnVal = getParentData(d, eventSourceField);
          if (returnVal?.length > 0) break;
        }
      }
    }
  }
  return returnVal || [];
}

/**
 * 构建数据
 */
export function buildTips(params: IBuildTipParams) {
  const { type, eventSourceType, eventSourceField, buildTip = [], fieldDatas = [] } = params;
  const buildTipTemp = [];
  buildTipTemp.push(`interface ${type} {`);
  fieldDatas.forEach((s) => {
    const { data_name, fullPath, data_type, description, field = [] } = s;
    if (
      ['data', 'submitAction', 'toolbar', 'operation'].includes(eventSourceType) ||
      (eventSourceType === 'component' &&
        eventSourceField.selectedfields.findIndex(
          (field) =>
            data_name === field.value &&
            fullPath === (field.path ? `${field.path}.${field.value}` : field.value)
        ) > -1)
    ) {
      if (data_type !== 'object') {
        buildTipTemp.push('/**');
        buildTipTemp.push(` * ${description?.[i18next.language]}`);
        buildTipTemp.push(' */');
        buildTipTemp.push(`${data_name}: ${data_type};`);
      } else if (field.length > 0) {
        buildTipTemp.push('/**');
        buildTipTemp.push(` * ${description?.[i18next.language]}`);
        buildTipTemp.push(' */');
        buildTipTemp.push(`${data_name}: ${capitalize(data_name)};`);
        buildTips({
          type: capitalize(data_name),
          fieldDatas: field,
          buildTip,
          eventSourceField,
          eventSourceType,
        });
      }
    }
  });
  buildTipTemp.push('}');
  buildTipTemp.forEach((tip) => buildTip.push(tip)); // 需要保留buildTip的引用，所以不能给它直接赋值
  // buildTip = [...buildTip, ...buildTipTemp];
  return buildTip;
}

/**
 * 缓存hook配置
 */
let cacheHooksConfig: IHookConfig;
/**
 * 加载主应用hooks配置
 * @return {Promise<IHookConfig>}
 */
export async function loadHooksConfig(): Promise<IHookConfig> {
  return new Promise((resolve, reject) => {
    if (cacheHooksConfig) {
      resolve(cacheHooksConfig);
    } else {
      // 动态加载 hooks配置
      fetch(`${envParams.domain}/assets/share/hooks.json`)
        .then((response) => {
          if (!response.ok) {
            throw new Error('读取主应用Hooks配置失败：' + response.statusText);
          }
          return response.json();
        })
        .then((data) => {
          cacheHooksConfig = data;
          resolve(data);
        })
        .catch((error) => {
          reject(error);
        });
    }
  });
}

/**
 * 格式化eventSourceName
 * @param {IFieldData} field - 字段数据
 * @return {string} - eventSourceName
 */
export function formatEventSourceName(field: IFieldTreeNodeInfo): string {
  if (field.data_type === 'array' || (field.is_array && ['true', true].includes(field.is_array))) {
    return `${field.data_name}[*]`;
  }
  return field.data_name;
}

/**
 * 格式化字段数据为字段树数据
 * @param {IFieldData[]} fields - 字段数据数组
 * @param {string} prefix - 前缀
 * @returns {IFieldTreeNode[]} - 字段树数据
 */
export function formatFieldToTreeData(
  fields: IFieldTreeNodeInfo[] = [],
  prefix?: string
): IFieldTreeNode[] {
  const results: IFieldTreeNode[] = [];
  fields.forEach((element) => {
    const eventSourceName = formatEventSourceName(element);
    const eventSource = !prefix ? eventSourceName : prefix + '.' + eventSourceName;
    const isLeaf = element.data_type !== 'object';
    const treeData: IFieldTreeNode = {
      value: eventSource,
      title: (element?.description?.[i18next.language as keyof ILangInfo] as string) ?? null,
      isLeaf,
      origin: element,
    };
    if (!treeData.isLeaf) {
      treeData.children = formatFieldToTreeData(element.children, eventSource);
    }
    results.push(treeData);
  });
  return results;
}

/**
 * 拉平元数据
 */
export function flatOriginTreeData(fields: IFieldTreeNode[] = []): IFieldTreeNode[] {
  const results: IFieldTreeNode[] = [];
  fields.forEach((element) => {
    results.push(element);
    if (!!element.children) {
      const data = flatOriginTreeData(element.children);
      results.push(...data);
    }
  });
  return results;
}

/**
 * 从eventSource格式化出data_name
 * @param {string} eventSource
 * @return {string} - data_name
 */
export function formatDataNameFromEventSource(eventSource: string = ''): string {
  const eventSourceName = last(eventSource?.split('.'));
  const pattern = new RegExp('\\[\\*\\]');
  return eventSourceName?.replace(pattern, '') ?? '';
}

/**
 * 数据hook下拉选项
 */
export function getDataTypeSelectOptions(
  hooksConfig: IHookParams[],
  formData?: string,
  treeData?: any[]
) {
  const { eventSource = '' } = formData ?? {};
  if (!!eventSource) {
    const dataType = getFieldDataType(eventSource, treeData);
    return !dataType
      ? []
      : hooksConfig
          .filter((item) => item.dataType?.includes(dataType))
          .map((hook) => ({
            value: hook.name,
            label: `${hook.name} ${hook.lang?.description?.[i18next.language] ?? hook.description}`,
          }));
  } else {
    return hooksConfig.map((hook) => ({
      value: hook.name,
      label: `${hook.name} ${hook.lang?.description?.[i18next.language] ?? hook.description}`,
    }));
  }
}

/**
 * 获取字段数据
 */
export function getFieldDataType(eventSource, treeData) {
  const item = treeData.find((item) => item.value === eventSource);
  if (!!item) {
    const isArray = item?.origin?.is_array;
    const data_type = item?.origin?.data_type;
    return isArray ? 'array' : data_type === 'object' ? 'object' : 'basicData';
  }
  return null;
}

/**
 * 获取组件hook的所有下拉选项
 * @param {string} eventSource - 组件id
 * @param {IHookConfig} hooksConfig - hooks配置
 * @param {IComponentInfo[]} componentList - 组件列表
 * @param {IHookParams[]} customHooks - 自定义hook
 * @param {boolean} isMobile - 是否移动端
 * @returns {IHookParams[]}
 */
export function getComponentAllOptions(
  eventSource: string,
  hooksConfig: IHookConfig,
  componentList: IComponentInfo[] = [],
  customHooks: IHookParams[] = [],
  isMobile: boolean = false
) {
  const component = componentList.find((comp) => comp.id === eventSource);
  if (component) {
    const { type, dslInfo = {} } = component;
    const { type: dslType } = dslInfo;
    const _customHooks = isMobile
      ? hooksConfig?.ComponentCustomHooksMobile
      : hooksConfig?.ComponentCustomHooks;
    const commonHooks = isMobile
      ? hooksConfig?.ComponentCommonHooksMobile
      : hooksConfig?.ComponentCommonHooks;
    let allHooks: IHookParams[] = [];
    if (_customHooks && _customHooks?.[type]?.length > 0) {
      allHooks = allHooks.concat(_customHooks[type]);
    }
    if (commonHooks && commonHooks?.length > 0) {
      allHooks = allHooks.concat(commonHooks);
    }
    if (type === 'BUTTON') {
      if (SubmitButtonTypeSet.has(dslType)) {
        allHooks = allHooks.concat(hooksConfig?.SubmitActionsHooks ?? []);
      }
      if (dslType === 'BUTTON_SUB_PAGE') {
        allHooks = allHooks.concat(hooksConfig?.SubpageButtonHooks ?? []);
      }
    }
    if (type === 'BUTTON_DECOUPLE') {
      if (newButtonTypeSet.has(dslType)) {
        allHooks = allHooks.concat(hooksConfig?.SubmitActionsHooks ?? []);
      }
    }
    /**
     * 这边不知道有没有顺序要求，看起来是有的，所以没有初始化allHooks直接放进去
     */
    allHooks = allHooks.concat(customHooks);
    return allHooks.map((hook) => ({
      ...hook,
      optionLabel: `${hook.name} ${hook.lang?.description?.[i18next.language] ?? hook.description}`,
    }));
  }
  return [];
}
