.edit-tag-modal {
  .ant-modal-header {
    margin-bottom: 20px;

    .ant-modal-title {
      text-align: center;
    }
  }

  .ant-modal-body {
    .preview-tag {
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      border: 1px dashed #ddd;
      margin-top: 10px;
      margin-bottom: 12px;
      height: 58px;
      border-radius: 3px;

      .preview-text {
        display: block;
        position: absolute;
        left: 12px;
        top: -10px;
        font-size: 12px;
        color: #ddd;
        background: #fff;
      }
    }

    .stamp {
      height: 104px !important;
    }

    .p-tag {
      display: flex;
      border-radius: 4px;
      padding: 5px 7px;
      font-size: 13px;
      background: #f2f3f5;
      color: #1d2129;

      span {
        display: inline-block;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }

    .p-tag-stamp {
      display: flex;
      border-radius: 4px;
      padding: 5px 7px;
      font-size: 13px;
      background: #f2f3f5;
      color: #1d2129;
      align-items: center;
      justify-content: center;

      span {
        display: inline-block;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        min-width: 76px;
        border-radius: 4px;
        height: 24px;
        text-align: center;
      }
    }
  }

  .ant-modal-footer {
    text-align: center;
  }

  .ant-form-item-has-error {
    margin-bottom: 24px;
  }
}

.base-tag {
  box-sizing: border-box;
  background: #f2f3f5;
  color: #1d2129;
  border-radius: 4px !important;
  padding: 5px 7px;
  // .tag-icon {
  //   color: #6a4cff;
  //   float: right;
  //   font-size: 17px;
  // }
}
