/**
 * 快捷键控制工具函数
 */

export const hotkeyUtils = {
  enable: () => {
    // 优先使用 lowcode 对象
    if ((window as any).lowcode?.hotkeyEventEmitter) {
      (window as any).lowcode.hotkeyEventEmitter.emit('hotkey:enable');
    } else {
      // 备用方案：使用 postMessage
      window.postMessage({ type: 'hotkey:enable' }, '*');
    }
  },
  
  disable: () => {
    // 优先使用 lowcode 对象
    if ((window as any).lowcode?.hotkeyEventEmitter) {
      (window as any).lowcode.hotkeyEventEmitter.emit('hotkey:disable');
    } else {
      // 备用方案：使用 postMessage
      window.postMessage({ type: 'hotkey:disable' }, '*');
    }
  },
  
  // 检查是否可用
  isAvailable: () => {
    return !!(window as any).lowcode?.hotkeyEventEmitter;
  }
}; 